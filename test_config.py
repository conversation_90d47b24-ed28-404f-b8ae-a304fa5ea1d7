#!/usr/bin/env python3
"""Test configuration and system readiness"""

import os
from dotenv import load_dotenv

load_dotenv()

print('🔧 Configuration Test:')
print(f'DEEPSEEK_API_KEY: {os.getenv("DEEPSEEK_API_KEY", "Not found")[:20]}...')
print(f'LIVE_TRADING_ENABLED: {os.getenv("LIVE_TRADING_ENABLED")}')
print(f'INITIAL_PORTFOLIO_VALUE: ${os.getenv("INITIAL_PORTFOLIO_VALUE")}')
print(f'MAX_POSITION_VALUE_USD: ${os.getenv("MAX_POSITION_VALUE_USD")}')
print(f'ENVIRONMENT: {os.getenv("ENVIRONMENT")}')
print('✅ Configuration loaded successfully')

# Test import
try:
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent / "NewQuantAgent"))
    from NewQuantAgent.main import NewQuantAgent
    print('✅ NewQuantAgent import successful')
except Exception as e:
    print(f'❌ Import failed: {e}')
