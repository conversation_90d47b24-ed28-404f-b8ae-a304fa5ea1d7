"""
Configuration management for Quantum Ape Signal
"""

from typing import Dict, Any
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings
import yaml
import os
from pathlib import Path


class DatabaseConfig(BaseModel):
    """Database configuration"""
    url: str = Field(..., description="Database connection URL")
    host: str = "localhost"
    port: int = 5432
    name: str = "mathematical_trading"
    user: str = "trading_user"
    password: str = ""
    pool_size: int = 20


class APIKeysConfig(BaseModel):
    """External API keys"""
    binance_api_key: str = ""
    binance_secret_key: str = ""
    coinbase_api_key: str = ""
    coinbase_secret_key: str = ""
    news_api_key: str = ""
    alpha_vantage_api_key: str = ""
    quandl_api_key: str = ""
    deepseek_api_key: str = ""
    deepseek_base_url: str = "https://api.deepseek.com"
    alpaca_api_key: str = ""
    alpaca_secret_key: str = ""
    alpaca_base_url: str = "https://paper-api.alpaca.markets"


class CyclesConfig(BaseModel):
    """Market cycle definitions"""
    micro: int = 60  # 1 minute
    mini: int = 300  # 5 minutes
    short: int = 900  # 15 minutes
    medium: int = 3600  # 1 hour
    macro: int = 86400  # 1 day


class PatternConfig(BaseModel):
    """Pattern recognition configuration"""
    min_occurrence: int = 50
    similarity_threshold: float = 0.85
    dbscan_eps: float = 0.3
    dbscan_min_samples: int = 10
    wavelet_type: str = "db4"
    wavelet_level: int = 4
    gmm_components: int = 3


class SequenceConfig(BaseModel):
    """Sequence learning configuration"""
    max_sequence_length: int = 20
    n_predictions: int = 3
    smoothing_alpha: float = 0.01


class WeatherConfig(BaseModel):
    """Weather agent configuration"""
    update_interval: int = 60  # seconds
    sentiment_threshold: float = 0.7
    volatility_threshold: float = 2.0


class MCPConfig(BaseModel):
    """Model Context Protocol configuration"""
    transport: str = "stdio"  # stdio, sse, websocket
    timeout: int = 30
    max_retries: int = 3
    agent_urls: Dict[str, str] = {}


class EnsembleWeights(BaseModel):
    """Ensemble prediction weights"""
    pattern: float = 0.35
    sequence: float = 0.25
    weather: float = 0.15
    ai: float = 0.25


class Settings(BaseSettings):
    """Main application settings"""

    # Environment
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")

    # Paths
    project_root: Path = Field(default_factory=lambda: Path(__file__).parent.parent.parent)
    data_path: Path = Field(default_factory=lambda: Path("data"))
    models_path: Path = Field(default_factory=lambda: Path("models"))
    logs_path: Path = Field(default_factory=lambda: Path("logs"))

    # Database
    database_url: str = Field(..., env="DATABASE_URL")

    # API Keys
    binance_api_key: str = Field(default="", env="BINANCE_API_KEY")
    binance_secret_key: str = Field(default="", env="BINANCE_SECRET_KEY")
    coinbase_api_key: str = Field(default="", env="COINBASE_API_KEY")
    coinbase_secret_key: str = Field(default="", env="COINBASE_SECRET_KEY")
    news_api_key: str = Field(default="", env="NEWS_API_KEY")
    alpha_vantage_api_key: str = Field(default="", env="ALPHA_VANTAGE_API_KEY")
    quandl_api_key: str = Field(default="", env="QUANDL_API_KEY")
    deepseek_api_key: str = Field(..., env="DEEPSEEK_API_KEY")
    deepseek_base_url: str = Field(default="https://api.deepseek.com", env="DEEPSEEK_BASE_URL")
    alpaca_api_key: str = Field(default="", env="ALPACA_API_KEY")
    alpaca_secret_key: str = Field(default="", env="ALPACA_SECRET_KEY")
    alpaca_base_url: str = Field(default="https://paper-api.alpaca.markets", env="ALPACA_BASE_URL")

    # Trading Configuration
    max_position_size: float = 0.1
    max_portfolio_volatility: float = 0.2
    max_drawdown_limit: float = 0.15
    update_interval_seconds: int = 60

    # Component Configurations
    cycles: CyclesConfig = Field(default_factory=CyclesConfig)
    patterns: PatternConfig = Field(default_factory=PatternConfig)
    sequences: SequenceConfig = Field(default_factory=SequenceConfig)
    weather: WeatherConfig = Field(default_factory=WeatherConfig)
    mcp: MCPConfig = Field(default_factory=MCPConfig)
    ensemble_weights: EnsembleWeights = Field(default_factory=EnsembleWeights)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    def load_yaml_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        path = self.project_root / config_path
        if path.exists():
            with open(path, 'r') as f:
                return yaml.safe_load(f)
        return {}

    def save_config(self, config_path: str, data: Dict[str, Any]):
        """Save configuration to YAML file"""
        path = self.project_root / config_path
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, 'w') as f:
            yaml.dump(data, f, default_flow_style=False)

    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration"""
        return DatabaseConfig(
            url=self.database_url,
            pool_size=20
        )

    def get_api_keys(self) -> APIKeysConfig:
        """Get API keys configuration"""
        return APIKeysConfig(
            binance_api_key=self.binance_api_key,
            binance_secret_key=self.binance_secret_key,
            coinbase_api_key=self.coinbase_api_key,
            coinbase_secret_key=self.coinbase_secret_key,
            news_api_key=self.news_api_key,
            alpha_vantage_api_key=self.alpha_vantage_api_key,
            quandl_api_key=self.quandl_api_key,
            deepseek_api_key=self.deepseek_api_key,
            deepseek_base_url=self.deepseek_base_url,
            alpaca_api_key=self.alpaca_api_key,
            alpaca_secret_key=self.alpaca_secret_key,
            alpaca_base_url=self.alpaca_base_url
        )


# Global settings instance
settings = Settings()
