"""
System constants for Quantum Ape Signal
"""

from enum import Enum

# Timeframe constants
class Timeframe(Enum):
    """Trading timeframes"""
    MICRO = "1min"
    MINI = "5min"
    SHORT = "15min"
    MEDIUM = "1hour"
    MACRO = "1day"

TIMEFRAME_SECONDS = {
    Timeframe.MICRO: 60,
    Timeframe.MINI: 300,
    Timeframe.SHORT: 900,
    Timeframe.MEDIUM: 3600,
    Timeframe.MACRO: 86400
}

# Pattern constants
MIN_PATTERN_SAMPLES = 50
DTW_CONFIDENCE_THRESHOLD = 0.7
PATTERN_MATCH_THRESHOLD = 0.85

# Sequence constants
MAX_SEQUENCE_LENGTH = 20
SEQUENCE_PREDICTION_COUNT = 3

# Weather conditions
class WeatherCondition(Enum):
    """Market weather conditions"""
    BULL_TREND = "bull_trend"
    BEAR_TREND = "bear_trend"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    NEWS_DRIVEN = "news_driven"
    CONSOLIDATION = "consolidation"

# Routine categories
class RoutineCategory(Enum):
    """Market routine categories"""
    OPENING = "opening"
    MIDDAY = "midday"
    CLOSING = "closing"
    BREAKOUT = "breakout"
    CONSOLIDATION = "consolidation"
    REVERSAL = "reversal"

# MCP constants
MCP_PROTOCOL_VERSION = "0.1.0"
MCP_TIMEOUT_SECONDS = 30
MCP_MAX_RETRIES = 3

# Agent names
PATTERN_AGENT_NAME = "pattern_agent"
SEQUENCE_AGENT_NAME = "sequence_agent"
WEATHER_AGENT_NAME = "weather_agent"
DEEPSEEK_AGENT_NAME = "deepseek_agent"

# Database constants
DB_BATCH_SIZE = 1000
DB_MAX_CONNECTIONS = 20

# Feature extraction
WAVELET_TYPE = "db4"
WAVELET_LEVEL = 4
TDA_MAX_DIMENSION = 2

# Performance constants
MAX_WORKERS = 8
CACHE_TTL_SECONDS = 300  # 5 minutes

# Prediction constants
CONFIDENCE_THRESHOLD = 0.6
MIN_PREDICTION_CONFIDENCE = 0.5
