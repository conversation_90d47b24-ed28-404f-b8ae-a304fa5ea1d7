"""
Custom exceptions for Quantum Ape Signal
"""


class QuantumApeException(Exception):
    """Base exception for all Quantum Ape Signal errors"""
    pass


class DataException(QuantumApeException):
    """Data loading or processing errors"""
    pass


class PatternException(QuantumApeException):
    """Pattern recognition errors"""
    pass


class SequenceException(QuantumApeException):
    """Sequence learning errors"""
    pass


class WeatherException(QuantumApeException):
    """Weather agent errors"""
    pass


class MCPException(QuantumApeException):
    """MCP communication errors"""
    pass


class AgentException(QuantumApeException):
    """Agent-specific errors"""
    pass


class PredictionException(QuantumApeException):
    """Prediction generation errors"""
    pass


class ConfigurationException(QuantumApeException):
    """Configuration errors"""
    pass


class DatabaseException(QuantumApeException):
    """Database operation errors"""
    pass
