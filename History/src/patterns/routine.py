"""
Market Routine class definition
"""

import numpy as np
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
import structlog

logger = structlog.get_logger()


@dataclass
class MarketRoutine:
    """
    Represents a recognizable market pattern/routine
    """
    name: str
    signature: np.ndarray  # Mathematical fingerprint
    cycle_type: str  # Timeframe (1min, 5min, etc.)
    category: str = "unknown"  # opening, midday, closing, breakout, etc.
    confidence: float = 1.0

    # Pattern characteristics
    characteristics: Dict[str, Any] = field(default_factory=dict)

    # Variations of this routine
    variations: List[np.ndarray] = field(default_factory=list)

    # Chronological rules
    precedence_rules: Dict[str, List[str]] = field(default_factory=dict)
    succession_rules: Dict[str, List[str]] = field(default_factory=dict)

    # Statistics
    occurrence_count: int = 0
    avg_duration: float = 0.0
    avg_amplitude: float = 0.0
    avg_volatility: float = 0.0

    def __post_init__(self):
        if len(self.precedence_rules) == 0:
            self.precedence_rules = {
                'cannot_follow': [],
                'must_follow': []
            }

        if len(self.succession_rules) == 0:
            self.succession_rules = {
                'cannot_precede': [],
                'must_precede': []
            }

    def add_variation(self, variant_signature: np.ndarray, threshold: float = 0.85):
        """
        Add a variation of this routine if it's similar enough
        """
        from src.patterns.dtw_matcher import DTWMatcher

        matcher = DTWMatcher()
        distance = matcher.distance(self.signature, variant_signature)
        similarity = 1.0 / (1.0 + distance)

        if similarity > threshold:
            self.variations.append(variant_signature)
            logger.debug(f"Added variation to {self.name}", similarity=similarity)
            return True

        return False

    def update_statistics(self, cycle_data: np.ndarray):
        """
        Update routine statistics with new occurrence
        """
        self.occurrence_count += 1

        # Update duration
        duration = len(cycle_data)
        self.avg_duration = ((self.avg_duration * (self.occurrence_count - 1)) + duration) / self.occurrence_count

        # Update amplitude
        amplitude = np.max(cycle_data) - np.min(cycle_data)
        self.avg_amplitude = ((self.avg_amplitude * (self.occurrence_count - 1)) + amplitude) / self.occurrence_count

        # Update volatility
        volatility = np.std(cycle_data)
        self.avg_volatility = ((self.avg_volatility * (self.occurrence_count - 1)) + volatility) / self.occurrence_count

    def can_follow(self, previous_routine: 'MarketRoutine') -> bool:
        """
        Check if this routine can logically follow another routine
        """
        if previous_routine.name in self.precedence_rules.get('cannot_follow', []):
            return False

        must_follow = self.precedence_rules.get('must_follow', [])
        if must_follow and previous_routine.name not in must_follow:
            return False

        return True

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary for serialization
        """
        return {
            'name': self.name,
            'signature': self.signature.tolist(),
            'cycle_type': self.cycle_type,
            'category': self.category,
            'confidence': self.confidence,
            'characteristics': self.characteristics,
            'occurrence_count': self.occurrence_count,
            'avg_duration': self.avg_duration,
            'avg_amplitude': self.avg_amplitude,
            'avg_volatility': self.avg_volatility,
            'precedence_rules': self.precedence_rules,
            'succession_rules': self.succession_rules
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MarketRoutine':
        """
        Create MarketRoutine from dictionary
        """
        data['signature'] = np.array(data['signature'])
        return cls(**data)


class RoutineLibrary:
    """
    Collection of discovered market routines
    """

    def __init__(self):
        self.routines: Dict[str, MarketRoutine] = {}
        self.logger = logger.bind(component="RoutineLibrary")

    def add_routine(self, routine: MarketRoutine):
        """Add a routine to the library"""
        self.routines[routine.name] = routine
        self.logger.info(f"Added routine: {routine.name}")

    def get_routine(self, name: str) -> Optional[MarketRoutine]:
        """Get a routine by name"""
        return self.routines.get(name)

    def get_routines_by_category(self, category: str) -> List[MarketRoutine]:
        """Get all routines of a specific category"""
        return [r for r in self.routines.values() if r.category == category]

    def get_routines_by_timeframe(self, timeframe: str) -> List[MarketRoutine]:
        """Get all routines for a specific timeframe"""
        return [r for r in self.routines.values() if r.cycle_type == timeframe]

    def find_similar_routines(self, signature: np.ndarray, threshold: float = 0.85) -> List[MarketRoutine]:
        """Find routines similar to given signature"""
        from src.patterns.dtw_matcher import DTWMatcher

        matcher = DTWMatcher()
        similar = []

        for routine in self.routines.values():
            distance = matcher.distance(routine.signature, signature)
            similarity = 1.0 / (1.0 + distance)

            if similarity > threshold:
                similar.append(routine)

        return similar

    def __len__(self) -> int:
        return len(self.routines)

    def __iter__(self):
        return iter(self.routines.values())
