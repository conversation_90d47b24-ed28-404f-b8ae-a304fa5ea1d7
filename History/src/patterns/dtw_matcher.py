"""
Dynamic Time Warping pattern matcher
"""

import numpy as np
from typing import <PERSON><PERSON>, Optional
from fastdtw import fastdtw
import structlog

logger = structlog.get_logger()


class DTWMatcher:
    """
    Pattern matching using Dynamic Time Warping
    """

    def __init__(self, distance_metric: str = 'euclidean'):
        self.distance_metric = distance_metric
        self.logger = logger.bind(component="DTWMatcher")

    def distance(self, pattern1: np.ndarray, pattern2: np.ndarray) -> float:
        """
        Calculate DTW distance between two patterns
        """
        # Ensure 2D arrays
        if pattern1.ndim == 1:
            pattern1 = pattern1.reshape(-1, 1)
        if pattern2.ndim == 1:
            pattern2 = pattern2.reshape(-1, 1)

        distance, _ = fastdtw(pattern1, pattern2, dist=self._dist_func)
        return distance

    def similarity(self, pattern1: np.ndarray, pattern2: np.ndarray) -> float:
        """
        Calculate similarity score (0-1) between two patterns
        """
        distance = self.distance(pattern1, pattern2)
        similarity = 1.0 / (1.0 + distance)
        return similarity

    def match(self, current_pattern: np.ndarray, library: list, threshold: float = 0.7) -> Tuple[Optional[int], float]:
        """
        Find best matching pattern from library

        Args:
            current_pattern: Pattern to match
            library: List of known patterns
            threshold: Minimum similarity threshold

        Returns:
            (index_of_best_match, similarity_score) or (None, 0.0)
        """
        best_match_idx = None
        best_similarity = 0.0

        for idx, template in enumerate(library):
            sim = self.similarity(current_pattern, template)

            if sim > best_similarity:
                best_similarity = sim
                best_match_idx = idx

        if best_similarity < threshold:
            return None, best_similarity

        return best_match_idx, best_similarity

    def _dist_func(self, x: np.ndarray, y: np.ndarray) -> float:
        """Distance function for DTW"""
        if self.distance_metric == 'euclidean':
            return np.linalg.norm(x - y)
        elif self.distance_metric == 'manhattan':
            return np.sum(np.abs(x - y))
        elif self.distance_metric == 'cosine':
            return 1 - np.dot(x, y) / (np.linalg.norm(x) * np.linalg.norm(y))
        else:
            return np.linalg.norm(x - y)
