"""
Pattern mining using clustering algorithms
Discovers market routines from historical cycles
"""

import numpy as np
import pandas as pd
from typing import List, Dict
from sklearn.cluster import DBSCAN
from sklearn.mixture import GaussianMixture
import structlog
import pickle
from pathlib import Path

from src.patterns.routine import MarketRoutine, RoutineLibrary
from src.patterns.feature_extractor import FeatureExtractor
from src.core.config import settings
from src.core.exceptions import PatternException

logger = structlog.get_logger()


class PatternMiner:
    """
    Discover recurring market patterns (routines) using clustering
    """

    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.eps = self.config.get('eps', 0.3)
        self.min_samples = self.config.get('min_samples', 50)
        self.similarity_threshold = self.config.get('similarity_threshold', 0.85)

        self.feature_extractor = FeatureExtractor()
        self.routine_library = RoutineLibrary()
        self.logger = logger.bind(component="PatternMiner")

    def discover_routines(self, cycles: List[pd.DataFrame], timeframe: str) -> List[MarketRoutine]:
        """
        Discover routines from a list of cycles using DBSCAN clustering

        Args:
            cycles: List of cycle dataframes
            timeframe: Timeframe identifier (e.g., '5min')

        Returns:
            List of discovered MarketRoutine objects
        """
        if len(cycles) < self.min_samples:
            raise PatternException(f"Insufficient cycles for pattern discovery: {len(cycles)} < {self.min_samples}")

        self.logger.info(f"Discovering routines from {len(cycles)} cycles ({timeframe})")

        # Extract features from all cycles
        features = self._extract_features_from_cycles(cycles)

        # Cluster using DBSCAN
        clustering = DBSCAN(eps=self.eps, min_samples=self.min_samples, metric='euclidean')
        labels = clustering.fit_predict(features)

        # Create routine templates from clusters
        routines = []
        unique_labels = np.unique(labels)

        for label in unique_labels:
            if label == -1:  # Skip noise cluster
                continue

            # Get cycles belonging to this cluster
            cluster_indices = np.where(labels == label)[0]
            cluster_cycles = [cycles[i] for i in cluster_indices]

            # Create routine template
            routine = self._create_routine_template(cluster_cycles, timeframe, f"routine_{timeframe}_{label}")
            routines.append(routine)

            # Add to library
            self.routine_library.add_routine(routine)

            self.logger.info(f"Discovered routine {routine.name} with {len(cluster_cycles)} occurrences")

        self.logger.info(f"Discovered {len(routines)} routines for {timeframe}")
        return routines

    def _extract_features_from_cycles(self, cycles: List[pd.DataFrame]) -> np.ndarray:
        """
        Extract feature vectors from all cycles
        """
        features_list = []

        for cycle in cycles:
            try:
                features = self.feature_extractor.extract_all_features(cycle)
                features_list.append(features)
            except Exception as e:
                self.logger.warning(f"Failed to extract features: {str(e)}")

        if not features_list:
            raise PatternException("Failed to extract features from any cycle")

        return np.array(features_list)

    def _create_routine_template(self, cluster_cycles: List[pd.DataFrame], timeframe: str, name: str) -> MarketRoutine:
        """
        Create a MarketRoutine template from cluster of similar cycles
        """
        # Use median/average of features as signature
        features_list = []
        for cycle in cluster_cycles:
            features = self.feature_extractor.extract_all_features(cycle)
            features_list.append(features)

        signature = np.median(features_list, axis=0)

        # Calculate characteristics
        characteristics = self._calculate_characteristics(cluster_cycles)

        # Create routine
        routine = MarketRoutine(
            name=name,
            signature=signature,
            cycle_type=timeframe,
            characteristics=characteristics,
            occurrence_count=len(cluster_cycles)
        )

        # Update statistics
        for cycle in cluster_cycles:
            routine.update_statistics(cycle['close'].values)

        # Cluster variations within this routine
        self._cluster_variations(routine, cluster_cycles)

        return routine

    def _calculate_characteristics(self, cycles: List[pd.DataFrame]) -> Dict:
        """
        Calculate common characteristics of cycles
        """
        characteristics = {}

        # Aggregate statistics
        all_returns = []
        all_volumes = []
        all_amplitudes = []

        for cycle in cycles:
            all_returns.extend(cycle['close'].pct_change().dropna().values)
            all_volumes.extend(cycle['volume'].values)

            amplitude = cycle['high'].max() - cycle['low'].min()
            all_amplitudes.append(amplitude)

        characteristics['avg_return'] = np.mean(all_returns)
        characteristics['avg_volatility'] = np.std(all_returns)
        characteristics['avg_volume'] = np.mean(all_volumes)
        characteristics['avg_amplitude'] = np.mean(all_amplitudes)

        # Direction
        positive_returns = sum(1 for r in all_returns if r > 0)
        characteristics['directional_bias'] = 'bullish' if positive_returns > len(all_returns) / 2 else 'bearish'

        return characteristics

    def _cluster_variations(self, routine: MarketRoutine, cycles: List[pd.DataFrame]):
        """
        Cluster variations within a routine using GMM
        """
        if len(cycles) < 10:
            return  # Too few samples for variation clustering

        # Extract variation-specific features
        variation_features = []
        for cycle in cycles:
            features = self.feature_extractor.extract_variation_features(cycle)
            variation_features.append(features)

        variation_features = np.array(variation_features)

        # Use GMM to find variations
        n_components = min(3, len(cycles) // 5)  # Max 3 variations
        if n_components < 2:
            return

        try:
            gmm = GaussianMixture(n_components=n_components, covariance_type='full')
            variation_labels = gmm.fit_predict(variation_features)

            # Store variations
            for label in range(n_components):
                variant_indices = np.where(variation_labels == label)[0]
                if len(variant_indices) > 0:
                    variant_signature = np.median([variation_features[i] for i in variant_indices], axis=0)
                    routine.variations.append(variant_signature)

            self.logger.debug(f"Found {n_components} variations for {routine.name}")

        except Exception as e:
            self.logger.warning(f"Variation clustering failed: {str(e)}")

    def save_library(self, path: Path = None):
        """
        Save routine library to disk
        """
        if path is None:
            path = settings.models_path / "routine_library.pkl"

        path.parent.mkdir(parents=True, exist_ok=True)

        with open(path, 'wb') as f:
            pickle.dump(self.routine_library, f)

        self.logger.info(f"Saved routine library to {path}")

    def load_library(self, path: Path = None):
        """
        Load routine library from disk
        """
        if path is None:
            path = settings.models_path / "routine_library.pkl"

        if not path.exists():
            raise PatternException(f"Library file not found: {path}")

        with open(path, 'rb') as f:
            self.routine_library = pickle.load(f)

        self.logger.info(f"Loaded routine library from {path} ({len(self.routine_library)} routines)")

    def get_all_patterns(self) -> List[Dict]:
        """
        Get all discovered patterns as dictionaries
        """
        return [routine.to_dict() for routine in self.routine_library]
