"""
Feature extraction for pattern recognition
Uses wavelets, TDA, and statistical features
"""

import numpy as np
import pandas as pd
from typing import Dict, Any
import pywt
import structlog
from scipy.stats import skew, kurtosis

logger = structlog.get_logger()


class FeatureExtractor:
    """
    Extract mathematical features from market cycles
    """

    def __init__(self, wavelet_type: str = 'db4', wavelet_level: int = 4):
        self.wavelet_type = wavelet_type
        self.wavelet_level = wavelet_level
        self.logger = logger.bind(component="FeatureExtractor")

    def extract_wavelet_features(self, signal: np.ndarray) -> np.ndarray:
        """
        Extract wavelet decomposition coefficients
        """
        try:
            coeffs = pywt.wavedec(signal, self.wavelet_type, level=self.wavelet_level)
            # Flatten and concatenate all coefficient arrays
            wavelet_features = np.concatenate([c.flatten() for c in coeffs])
            return wavelet_features
        except Exception as e:
            self.logger.warning(f"Wavelet extraction failed: {str(e)}")
            return np.zeros(100)  # Fallback

    def extract_statistical_moments(self, signal: np.ndarray) -> np.ndarray:
        """
        Extract statistical moments: mean, std, skewness, kurtosis
        """
        return np.array([
            np.mean(signal),
            np.std(signal),
            skew(signal),
            kurtosis(signal)
        ])

    def extract_tda_features(self, signal: np.ndarray) -> np.ndarray:
        """
        Extract Topological Data Analysis features
        Uses persistent homology to capture "shape" of signal
        """
        try:
            from ripser import ripser
            from persim import plot_diagrams

            # Create point cloud from signal
            points = np.column_stack([np.arange(len(signal)), signal])

            # Compute persistence diagrams
            result = ripser(points, maxdim=1)
            diagrams = result['dgms']

            # Extract features from persistence diagrams
            features = []

            for dim_diagram in diagrams:
                if len(dim_diagram) > 0:
                    # Birth-death statistics
                    births = dim_diagram[:, 0]
                    deaths = dim_diagram[:, 1]
                    lifetimes = deaths - births

                    features.extend([
                        np.mean(lifetimes),
                        np.std(lifetimes),
                        np.max(lifetimes) if len(lifetimes) > 0 else 0,
                        len(lifetimes)
                    ])
                else:
                    features.extend([0, 0, 0, 0])

            return np.array(features)

        except Exception as e:
            self.logger.warning(f"TDA extraction failed: {str(e)}")
            return np.zeros(8)  # Fallback

    def extract_price_features(self, cycle_df: pd.DataFrame) -> np.ndarray:
        """
        Extract price-specific features
        """
        features = []

        # Price range
        price_range = cycle_df['high'].max() - cycle_df['low'].min()
        features.append(price_range)

        # Average body size
        body_size = np.abs(cycle_df['close'] - cycle_df['open']).mean()
        features.append(body_size)

        # Average wick size
        upper_wick = (cycle_df['high'] - cycle_df[['open', 'close']].max(axis=1)).mean()
        lower_wick = (cycle_df[['open', 'close']].min(axis=1) - cycle_df['low']).mean()
        features.extend([upper_wick, lower_wick])

        # Price trend
        price_change = cycle_df['close'].iloc[-1] - cycle_df['close'].iloc[0]
        features.append(price_change)

        # Volatility
        volatility = cycle_df['close'].pct_change().std()
        features.append(volatility)

        return np.array(features)

    def extract_volume_features(self, cycle_df: pd.DataFrame) -> np.ndarray:
        """
        Extract volume-specific features
        """
        features = []

        # Average volume
        avg_volume = cycle_df['volume'].mean()
        features.append(avg_volume)

        # Volume trend
        volume_trend = cycle_df['volume'].iloc[-1] - cycle_df['volume'].iloc[0]
        features.append(volume_trend)

        # Volume volatility
        volume_std = cycle_df['volume'].std()
        features.append(volume_std)

        # Volume spikes
        volume_mean = cycle_df['volume'].mean()
        volume_spikes = (cycle_df['volume'] > 2 * volume_mean).sum()
        features.append(volume_spikes)

        return np.array(features)

    def extract_all_features(self, cycle_df: pd.DataFrame) -> np.ndarray:
        """
        Extract complete feature vector from a cycle
        """
        # Use log returns for price-invariant features
        if 'log_return' in cycle_df.columns:
            price_signal = cycle_df['log_return'].fillna(0).values
        else:
            price_signal = cycle_df['close'].pct_change().fillna(0).values

        # Extract different feature types
        wavelet_features = self.extract_wavelet_features(price_signal)
        statistical_features = self.extract_statistical_moments(price_signal)
        tda_features = self.extract_tda_features(price_signal)
        price_features = self.extract_price_features(cycle_df)
        volume_features = self.extract_volume_features(cycle_df)

        # Concatenate all features
        all_features = np.concatenate([
            wavelet_features,
            statistical_features,
            tda_features,
            price_features,
            volume_features
        ])

        return all_features

    def extract_variation_features(self, cycle_df: pd.DataFrame) -> np.ndarray:
        """
        Extract features specifically for clustering variations
        Focus on amplitude, duration, volatility
        """
        features = []

        # Amplitude
        amplitude = cycle_df['high'].max() - cycle_df['low'].min()
        features.append(amplitude)

        # Duration
        duration = len(cycle_df)
        features.append(duration)

        # Volatility
        volatility = cycle_df['close'].pct_change().std()
        features.append(volatility)

        # Directional strength
        price_change = cycle_df['close'].iloc[-1] - cycle_df['close'].iloc[0]
        features.append(price_change / amplitude if amplitude > 0 else 0)

        # Volume intensity
        avg_volume = cycle_df['volume'].mean()
        features.append(avg_volume)

        return np.array(features)
