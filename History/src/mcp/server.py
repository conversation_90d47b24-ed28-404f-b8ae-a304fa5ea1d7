"""
MCP Server base implementation for Quantum Ape Signal agents
"""

from typing import Dict, List, Any, Callable, Optional
from abc import ABC, abstractmethod
import asyncio
import json
import structlog

logger = structlog.get_logger()


class Tool:
    """MCP Tool definition"""
    def __init__(self, name: str, description: str, input_schema: Dict[str, Any], handler: Callable):
        self.name = name
        self.description = description
        self.input_schema = input_schema
        self.handler = handler


class Resource:
    """MCP Resource definition"""
    def __init__(self, uri: str, name: str, description: str, mime_type: str = "application/json"):
        self.uri = uri
        self.name = name
        self.description = description
        self.mime_type = mime_type


class Prompt:
    """MCP Prompt template"""
    def __init__(self, name: str, description: str, arguments: List[Dict[str, Any]]):
        self.name = name
        self.description = description
        self.arguments = arguments


class MCPMessage:
    """MCP Protocol message"""
    def __init__(self, method: str, params: Optional[Dict[str, Any]] = None, id: Optional[str] = None):
        self.method = method
        self.params = params or {}
        self.id = id

    def to_dict(self) -> Dict[str, Any]:
        msg = {
            "jsonrpc": "2.0",
            "method": self.method,
            "params": self.params
        }
        if self.id:
            msg["id"] = self.id
        return msg

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPMessage':
        return cls(
            method=data.get("method", ""),
            params=data.get("params", {}),
            id=data.get("id")
        )


class QuantumApeAgent(ABC):
    """
    Base MCP server for all Quantum Ape agents
    Uses the Model Context Protocol for inter-agent communication
    """

    def __init__(self, name: str):
        self.name = name
        self.tools: Dict[str, Tool] = {}
        self.resources: Dict[str, Resource] = {}
        self.prompts: Dict[str, Prompt] = {}
        self.context: Dict[str, Any] = {}
        self.logger = logger.bind(agent=name)

    @abstractmethod
    async def initialize(self):
        """Initialize agent-specific components"""
        pass

    def register_tool(self, name: str, description: str, input_schema: Dict[str, Any], handler: Callable):
        """Register a tool that this agent provides"""
        tool = Tool(name, description, input_schema, handler)
        self.tools[name] = tool
        self.logger.info(f"Registered tool: {name}")

    def register_resource(self, uri: str, name: str, description: str, mime_type: str = "application/json"):
        """Register a resource that this agent provides"""
        resource = Resource(uri, name, description, mime_type)
        self.resources[name] = resource
        self.logger.info(f"Registered resource: {name}")

    def register_prompt(self, name: str, description: str, arguments: List[Dict[str, Any]]):
        """Register a prompt template"""
        prompt = Prompt(name, description, arguments)
        self.prompts[name] = prompt
        self.logger.info(f"Registered prompt: {name}")

    async def list_tools(self) -> List[Dict[str, Any]]:
        """List all available tools"""
        return [
            {
                "name": tool.name,
                "description": tool.description,
                "inputSchema": tool.input_schema
            }
            for tool in self.tools.values()
        ]

    async def list_resources(self) -> List[Dict[str, Any]]:
        """List all available resources"""
        return [
            {
                "uri": resource.uri,
                "name": resource.name,
                "description": resource.description,
                "mimeType": resource.mime_type
            }
            for resource in self.resources.values()
        ]

    async def list_prompts(self) -> List[Dict[str, Any]]:
        """List all available prompts"""
        return [
            {
                "name": prompt.name,
                "description": prompt.description,
                "arguments": prompt.arguments
            }
            for prompt in self.prompts.values()
        ]

    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool"""
        if name not in self.tools:
            raise ValueError(f"Unknown tool: {name}")

        self.logger.info(f"Calling tool: {name}", arguments=arguments)

        try:
            tool = self.tools[name]
            result = await tool.handler(arguments)

            self.logger.info(f"Tool completed: {name}")
            return {
                "content": [
                    {
                        "type": "text",
                        "text": json.dumps(result) if not isinstance(result, str) else result
                    }
                ],
                "isError": False
            }
        except Exception as e:
            self.logger.error(f"Tool error: {name}", error=str(e))
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Error: {str(e)}"
                    }
                ],
                "isError": True
            }

    async def read_resource(self, uri: str) -> Dict[str, Any]:
        """Read a resource"""
        # Extract resource name from URI
        resource_name = uri.split("://")[-1] if "://" in uri else uri

        if resource_name not in self.resources:
            raise ValueError(f"Unknown resource: {uri}")

        self.logger.info(f"Reading resource: {uri}")

        # Get resource data from context or storage
        data = self.context.get(resource_name, {})

        return {
            "contents": [
                {
                    "uri": uri,
                    "mimeType": self.resources[resource_name].mime_type,
                    "text": json.dumps(data)
                }
            ]
        }

    async def handle_message(self, message: MCPMessage) -> Dict[str, Any]:
        """Handle incoming MCP message"""
        self.logger.debug(f"Handling message: {message.method}")

        try:
            if message.method == "tools/list":
                tools = await self.list_tools()
                return {"tools": tools}

            elif message.method == "tools/call":
                tool_name = message.params.get("name")
                arguments = message.params.get("arguments", {})
                return await self.call_tool(tool_name, arguments)

            elif message.method == "resources/list":
                resources = await self.list_resources()
                return {"resources": resources}

            elif message.method == "resources/read":
                uri = message.params.get("uri")
                return await self.read_resource(uri)

            elif message.method == "prompts/list":
                prompts = await self.list_prompts()
                return {"prompts": prompts}

            else:
                raise ValueError(f"Unknown method: {message.method}")

        except Exception as e:
            self.logger.error(f"Message handling error", error=str(e))
            raise

    def update_context(self, key: str, value: Any):
        """Update agent context"""
        self.context[key] = value
        self.logger.debug(f"Context updated: {key}")

    def get_context(self, key: str, default: Any = None) -> Any:
        """Get value from agent context"""
        return self.context.get(key, default)


class AgentServer:
    """
    MCP server runner for stdio transport
    """

    def __init__(self, agent: QuantumApeAgent):
        self.agent = agent
        self.logger = structlog.get_logger().bind(server=agent.name)

    async def run(self):
        """Run the MCP server via stdio"""
        import sys

        self.logger.info(f"Starting MCP server: {self.agent.name}")

        # Initialize agent
        await self.agent.initialize()

        # Send server info
        info = {
            "jsonrpc": "2.0",
            "method": "server/info",
            "params": {
                "name": self.agent.name,
                "version": "1.0.0",
                "protocolVersion": "0.1.0"
            }
        }
        print(json.dumps(info), flush=True)

        # Message loop
        for line in sys.stdin:
            try:
                data = json.loads(line.strip())
                message = MCPMessage.from_dict(data)

                result = await self.agent.handle_message(message)

                response = {
                    "jsonrpc": "2.0",
                    "id": message.id,
                    "result": result
                }
                print(json.dumps(response), flush=True)

            except Exception as e:
                self.logger.error("Message processing error", error=str(e))
                error_response = {
                    "jsonrpc": "2.0",
                    "id": data.get("id") if 'data' in locals() else None,
                    "error": {
                        "code": -32603,
                        "message": str(e)
                    }
                }
                print(json.dumps(error_response), flush=True)


def run_agent(agent: QuantumApeAgent):
    """Helper function to run an MCP agent server"""
    server = AgentServer(agent)
    asyncio.run(server.run())
