"""
Data preprocessing and cleaning
"""

import pandas as pd
import numpy as np
import structlog
from typing import Optional

from src.core.exceptions import DataException

logger = structlog.get_logger()


class Preprocessor:
    """
    Clean and normalize market data
    """

    def __init__(self):
        self.logger = logger.bind(component="Preprocessor")

    def remove_outliers(self, df: pd.DataFrame, columns: Optional[list] = None, method: str = "iqr") -> pd.DataFrame:
        """
        Remove outliers using IQR or Z-score method
        """
        if columns is None:
            columns = ['open', 'high', 'low', 'close', 'volume']

        df_clean = df.copy()

        for col in columns:
            if col not in df.columns:
                continue

            if method == "iqr":
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                outliers = (df[col] < lower_bound) | (df[col] > upper_bound)
                num_outliers = outliers.sum()

                if num_outliers > 0:
                    self.logger.info(f"Removing {num_outliers} outliers from {col}")
                    df_clean = df_clean[~outliers]

            elif method == "zscore":
                z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
                outliers = z_scores > 3
                num_outliers = outliers.sum()

                if num_outliers > 0:
                    self.logger.info(f"Removing {num_outliers} outliers from {col}")
                    df_clean = df_clean[~outliers]

        return df_clean

    def handle_missing_values(self, df: pd.DataFrame, method: str = "ffill", max_gap: int = 5) -> pd.DataFrame:
        """
        Handle missing values in the data
        """
        df_filled = df.copy()

        # Check for missing values
        missing_count = df_filled.isnull().sum().sum()
        if missing_count == 0:
            return df_filled

        self.logger.info(f"Handling {missing_count} missing values")

        if method == "ffill":
            # Forward fill with limit
            df_filled = df_filled.fillna(method='ffill', limit=max_gap)

        elif method == "interpolate":
            # Linear interpolation
            df_filled = df_filled.interpolate(method='linear', limit=max_gap)

        elif method == "drop":
            # Drop rows with missing values
            df_filled = df_filled.dropna()

        # Drop any remaining NaN values
        remaining_na = df_filled.isnull().sum().sum()
        if remaining_na > 0:
            self.logger.warning(f"Dropping {remaining_na} remaining NaN values")
            df_filled = df_filled.dropna()

        return df_filled

    def add_returns(self, df: pd.DataFrame, price_col: str = 'close') -> pd.DataFrame:
        """
        Add return columns (simple and log returns)
        """
        df_with_returns = df.copy()

        # Simple returns
        df_with_returns['return'] = df[price_col].pct_change()

        # Log returns
        df_with_returns['log_return'] = np.log(df[price_col] / df[price_col].shift(1))

        return df_with_returns

    def normalize(self, df: pd.DataFrame, columns: Optional[list] = None, method: str = "minmax") -> pd.DataFrame:
        """
        Normalize data using min-max or z-score normalization
        """
        if columns is None:
            columns = ['open', 'high', 'low', 'close', 'volume']

        df_normalized = df.copy()

        for col in columns:
            if col not in df.columns:
                continue

            if method == "minmax":
                min_val = df[col].min()
                max_val = df[col].max()
                df_normalized[f'{col}_normalized'] = (df[col] - min_val) / (max_val - min_val)

            elif method == "zscore":
                mean_val = df[col].mean()
                std_val = df[col].std()
                df_normalized[f'{col}_normalized'] = (df[col] - mean_val) / std_val

        return df_normalized

    def resample(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """
        Resample OHLCV data to different timeframe
        """
        resampled = df.resample(timeframe).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()

        return resampled

    def process(self, df: pd.DataFrame, add_features: bool = True) -> pd.DataFrame:
        """
        Complete preprocessing pipeline
        """
        self.logger.info(f"Processing {len(df)} records")

        # 1. Remove outliers
        df = self.remove_outliers(df)

        # 2. Handle missing values
        df = self.handle_missing_values(df)

        # 3. Add returns
        if add_features:
            df = self.add_returns(df)

        # 4. Remove any NaN from returns
        df = df.dropna()

        self.logger.info(f"Processed {len(df)} records")
        return df
