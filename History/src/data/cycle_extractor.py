"""
Temporal cycle extraction from market data
"""

import pandas as pd
import numpy as np
from typing import List, Dict
import structlog

from src.core.constants import TIMEFRAME_SECONDS, Timeframe
from src.core.exceptions import DataException

logger = structlog.get_logger()


class CycleExtractor:
    """
    Extract temporal cycles from continuous market data
    """

    def __init__(self):
        self.logger = logger.bind(component="CycleExtractor")

    def extract_cycles(self, df: pd.DataFrame, cycle_length: int, overlap: float = 0.0) -> List[pd.DataFrame]:
        """
        Extract fixed-length cycles from data

        Args:
            df: Input dataframe with OHLCV data
            cycle_length: Number of data points per cycle
            overlap: Fraction of overlap between consecutive cycles (0.0 to 0.9)

        Returns:
            List of cycle dataframes
        """
        if len(df) < cycle_length:
            raise DataException(f"Insufficient data: {len(df)} < {cycle_length}")

        cycles = []
        step = int(cycle_length * (1 - overlap))

        if step <= 0:
            step = 1

        for i in range(0, len(df) - cycle_length + 1, step):
            cycle = df.iloc[i:i + cycle_length].copy()
            if len(cycle) == cycle_length:
                cycles.append(cycle)

        self.logger.info(f"Extracted {len(cycles)} cycles of length {cycle_length}")
        return cycles

    def extract_by_timeframe(self, df: pd.DataFrame, timeframe: Timeframe, num_points: int = 100) -> List[pd.DataFrame]:
        """
        Extract cycles based on timeframe

        Args:
            df: Input dataframe
            timeframe: Timeframe enum (MICRO, MINI, SHORT, MEDIUM, MACRO)
            num_points: Number of data points per cycle

        Returns:
            List of cycles
        """
        # First, resample data to match timeframe if needed
        timeframe_str = {
            Timeframe.MICRO: '1min',
            Timeframe.MINI: '5min',
            Timeframe.SHORT: '15min',
            Timeframe.MEDIUM: '1H',
            Timeframe.MACRO: '1D'
        }[timeframe]

        # Resample
        resampled = df.resample(timeframe_str).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()

        # Extract cycles
        return self.extract_cycles(resampled, num_points)

    def extract_all_timeframes(self, df: pd.DataFrame, num_points: int = 100) -> Dict[str, List[pd.DataFrame]]:
        """
        Extract cycles for all timeframes

        Returns:
            Dictionary mapping timeframe name to list of cycles
        """
        all_cycles = {}

        for timeframe in Timeframe:
            try:
                cycles = self.extract_by_timeframe(df, timeframe, num_points)
                all_cycles[timeframe.value] = cycles
                self.logger.info(f"Extracted {len(cycles)} cycles for {timeframe.value}")
            except Exception as e:
                self.logger.warning(f"Failed to extract cycles for {timeframe.value}: {str(e)}")

        return all_cycles

    def extract_adaptive_cycles(self, df: pd.DataFrame, volatility_threshold: float = 0.02) -> List[pd.DataFrame]:
        """
        Extract cycles with adaptive length based on volatility

        High volatility periods get shorter cycles, low volatility gets longer cycles
        """
        # Calculate rolling volatility
        df = df.copy()
        df['volatility'] = df['close'].pct_change().rolling(window=20).std()

        cycles = []
        i = 0

        while i < len(df) - 20:
            # Determine cycle length based on current volatility
            current_vol = df['volatility'].iloc[i]

            if pd.isna(current_vol):
                i += 1
                continue

            if current_vol > volatility_threshold:
                cycle_length = 50  # Shorter cycles for high volatility
            else:
                cycle_length = 200  # Longer cycles for low volatility

            # Extract cycle
            if i + cycle_length <= len(df):
                cycle = df.iloc[i:i + cycle_length].copy()
                cycles.append(cycle)
                i += cycle_length // 2  # 50% overlap
            else:
                break

        self.logger.info(f"Extracted {len(cycles)} adaptive cycles")
        return cycles
