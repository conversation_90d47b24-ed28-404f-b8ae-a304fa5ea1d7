"""
Data loader for historical cryptocurrency data
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Optional, List
import zipfile
import io
import structlog
from sqlalchemy import create_engine, text
from datetime import datetime, timedelta

from src.core.config import settings
from src.core.exceptions import DataException

logger = structlog.get_logger()


class DataLoader:
    """
    Load cryptocurrency data from ZIP files or database
    """

    def __init__(self, data_path: Optional[Path] = None, db_url: Optional[str] = None):
        self.data_path = data_path or settings.data_path
        self.db_url = db_url or settings.database_url
        self.engine = None

        if self.db_url:
            self.engine = create_engine(self.db_url, pool_size=settings.database.pool_size)

        self.logger = logger.bind(component="DataLoader")

    def load_from_zip(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Load data from ZIP files for a given symbol and date range
        """
        self.logger.info(f"Loading {symbol} from ZIP files", start=start_date, end=end_date)

        # Find ZIP files for this symbol
        symbol_path = self.data_path / symbol
        if not symbol_path.exists():
            raise DataException(f"No data found for symbol: {symbol}")

        zip_files = sorted(symbol_path.glob("*.zip"))
        if not zip_files:
            raise DataException(f"No ZIP files found for symbol: {symbol}")

        # Load and concat all ZIP files
        dataframes = []
        for zip_file in zip_files:
            try:
                with zipfile.ZipFile(zip_file, 'r') as zf:
                    # Assuming CSV inside ZIP
                    csv_name = zf.namelist()[0]
                    with zf.open(csv_name) as f:
                        df = pd.read_csv(io.TextIOWrapper(f))
                        dataframes.append(df)
            except Exception as e:
                self.logger.warning(f"Failed to load {zip_file}: {str(e)}")

        if not dataframes:
            raise DataException(f"No data loaded for {symbol}")

        # Combine all dataframes
        df = pd.concat(dataframes, ignore_index=True)

        # Parse timestamps
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df = df.set_index('timestamp').sort_index()

        # Filter by date range
        df = df[start_date:end_date]

        self.logger.info(f"Loaded {len(df)} records for {symbol}")
        return df

    def load_from_database(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Load data from PostgreSQL database
        """
        self.logger.info(f"Loading {symbol} from database", start=start_date, end=end_date)

        if not self.engine:
            raise DataException("Database engine not initialized")

        query = text("""
            SELECT
                ps.timestamp,
                ps.open,
                ps.high,
                ps.low,
                ps.close,
                ps.volume
            FROM price_snapshots ps
            JOIN listings l ON ps.listing_id = l.id
            WHERE l.symbol = :symbol
            AND ps.timestamp BETWEEN :start_date AND :end_date
            ORDER BY ps.timestamp
        """)

        with self.engine.connect() as conn:
            df = pd.read_sql(
                query,
                conn,
                params={
                    'symbol': symbol,
                    'start_date': start_date,
                    'end_date': end_date
                }
            )

        if df.empty:
            raise DataException(f"No data found for {symbol} in database")

        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp')

        self.logger.info(f"Loaded {len(df)} records for {symbol}")
        return df

    def load(self, symbol: str, start_date: str, end_date: str, source: str = "auto") -> pd.DataFrame:
        """
        Load data from best available source
        """
        if source == "database" or (source == "auto" and self.engine):
            try:
                return self.load_from_database(symbol, start_date, end_date)
            except DataException:
                if source == "database":
                    raise
                self.logger.info("Database load failed, trying ZIP files")

        return self.load_from_zip(symbol, start_date, end_date)

    def get_latest(self, symbol: str, lookback: int = 1000) -> pd.DataFrame:
        """
        Get latest N data points for a symbol
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)  # Adjust based on lookback

        df = self.load(symbol, start_date.isoformat(), end_date.isoformat())

        # Return last N records
        return df.tail(lookback)

    def get_symbols(self) -> List[str]:
        """
        Get list of available symbols
        """
        if self.engine:
            query = text("SELECT DISTINCT symbol FROM listings ORDER BY symbol")
            with self.engine.connect() as conn:
                result = conn.execute(query)
                return [row[0] for row in result]
        else:
            # List from filesystem
            return [d.name for d in self.data_path.iterdir() if d.is_dir()]
