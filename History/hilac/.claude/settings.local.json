{"permissions": {"allow": ["Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"SELECT COUNT(DISTINCT listing_id) as listings, COUNT(*) as snapshots FROM price_snapshots;\")", "Bash(./test-small-capitals.sh:*)", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"\nSELECT COUNT(DISTINCT listing_id) as listings, COUNT(*) as snapshots \nFROM price_snapshots;\n\")", "Bash(ps:*)", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"ALTER TABLE price_snapshots ADD COLUMN IF NOT EXISTS sentiment_score DECIMAL(5,4);\")", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"ALTER TABLE price_snapshots ADD COLUMN IF NOT EXISTS sentiment_analyzed_at TIMESTAMP;\")", "Bash(dotnet build:*)", "Bash(dotnet run:*)", "<PERSON><PERSON>(chmod:*)", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"\nSELECT \n    EXTRACT(YEAR FROM timestamp) as year,\n    COUNT(DISTINCT listing_id) as unique_listings,\n    COUNT(*) as total_snapshots\nFROM price_snapshots\nGROUP BY EXTRACT(YEAR FROM timestamp)\nORDER BY year;\n\")", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"\nSELECT \n    exchange,\n    COUNT(DISTINCT listing_id) as unique_listings,\n    COUNT(*) as total_snapshots,\n    MIN(timestamp) as earliest_data,\n    MAX(timestamp) as latest_data\nFROM price_snapshots\nGROUP BY exchange\nORDER BY unique_listings DESC;\n\")", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"\nSELECT COUNT(DISTINCT listing_id) as total_unique_listings FROM price_snapshots;\n\")", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"\nSELECT \n    cl.symbol,\n    cl.token_name,\n    cl.exchange,\n    cl.listing_time,\n    COUNT(ps.id) as snapshots\nFROM core_listings cl\nLEFT JOIN price_snapshots ps ON cl.id = ps.listing_id\nGROUP BY cl.id, cl.symbol, cl.token_name, cl.exchange, cl.listing_time\nORDER BY cl.listing_time DESC\nLIMIT 10;\n\")", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"\nSELECT \n    EXTRACT(YEAR FROM listing_time) as year,\n    COUNT(*) as listings_announced\nFROM core_listings\nGROUP BY EXTRACT(YEAR FROM listing_time)\nORDER BY year;\n\")", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"SELECT pid, usename, application_name, state, query_start, state_change, LEFT(query, 100) as query FROM pg_stat_activity WHERE datname = ''hilac_db'' AND state != ''idle'' ORDER BY query_start;\")", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"SELECT pid, usename, application_name, state, query_start, state_change, LEFT(query, 100) as query FROM pg_stat_activity WHERE datname = ''hilac_db'' AND state <> ''idle'' ORDER BY query_start;\")", "<PERSON><PERSON>(top:*)", "Ba<PERSON>(strace:*)", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"\nSELECT \n    cl.symbol,\n    cl.exchange,\n    COUNT(ps.id) as snapshots,\n    MIN(ps.timestamp) as earliest,\n    MAX(ps.timestamp) as latest\nFROM core_listings cl\nLEFT JOIN price_snapshots ps ON cl.id = ps.listing_id\nGROUP BY cl.id, cl.symbol, cl.exchange\nHAVING COUNT(ps.id) > 0\nORDER BY snapshots DESC\nLIMIT 20;\n\")", "Bash(./test-efficient-insert.sh:*)", "Bash(./restart-with-fix.sh:*)", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSYU=' psql -U hilac_user -d hilac_db -h localhost -c \"\n-- Check data completeness (any gaps?)\nSELECT \n    ''✅ DATA QUALITY'' as check_type,\n    COUNT(*) as total_snapshots,\n    COUNT(CASE WHEN price > 0 THEN 1 END) as valid_prices,\n    COUNT(CASE WHEN volume_usd > 0 THEN 1 END) as with_volume,\n    ROUND(100.0 * COUNT(CASE WHEN price > 0 THEN 1 END) / COUNT(*), 2) as price_completeness_pct\nFROM price_snapshots;\n\")", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"\n-- Check data quality\nSELECT \n    ''✅ DATA QUALITY'' as check_type,\n    COUNT(*) as total_snapshots,\n    COUNT(CASE WHEN price > 0 THEN 1 END) as valid_prices,\n    COUNT(CASE WHEN volume_usd > 0 THEN 1 END) as with_volume,\n    ROUND(100.0 * COUNT(CASE WHEN price > 0 THEN 1 END) / COUNT(*), 2) as price_completeness_pct\nFROM price_snapshots;\n\")", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql -U hilac_user -d hilac_db -h localhost -c \"\n-- Check year distribution\nSELECT \n    EXTRACT(YEAR FROM timestamp) as year,\n    COUNT(*) as snapshots,\n    COUNT(DISTINCT listing_id) as unique_listings\nFROM price_snapshots\nGROUP BY EXTRACT(YEAR FROM timestamp)\nORDER BY year;\n\")", "Bash(PGPASSWORD='SB2cgBh4PsjGFZc/CuIKwIVJdCOMUP0PWILChI2HSVY=' psql:*)", "Bash(PGPASSWORD:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(timeout:*)", "Bash(awk:*)", "<PERSON><PERSON>(xargs -r kill -9)", "Bash(/dev/null echo \"\" echo \"=== LEGACY_BACKUP CONTENTS ===\" echo \"\" find LEGACY_BACKUP -type f)", "Bash(kill:*)"], "deny": [], "ask": []}}