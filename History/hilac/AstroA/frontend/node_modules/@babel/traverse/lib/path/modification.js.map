{"version": 3, "names": ["_cache", "require", "_index", "_context", "_removal", "_t", "_hoister", "arrowFunctionExpression", "assertExpression", "assignmentExpression", "blockStatement", "callExpression", "cloneNode", "expressionStatement", "isAssignmentExpression", "isCallExpression", "isExportNamedDeclaration", "isExpression", "isIdentifier", "isSequenceExpression", "is<PERSON><PERSON><PERSON>", "thisExpression", "insertBefore", "nodes_", "_assertUnremoved", "call", "nodes", "_verifyNodeList", "parentPath", "parent", "isExpressionStatement", "isLabeledStatement", "isExportDefaultDeclaration", "isDeclaration", "isNodeType", "isJSXElement", "isForStatement", "key", "node", "push", "replaceExpressionWithStatements", "Array", "isArray", "container", "_containerInsertBefore", "isStatementOrBlock", "shouldInsertCurrentNode", "expression", "blockPath", "replaceWith", "unshiftContainer", "Error", "_containerInsert", "from", "updateSiblingKeys", "length", "paths", "splice", "i", "_this$context", "to", "path", "getSibling", "context", "queue", "pushContext", "contexts", "_getQueueContexts", "setScope", "debug", "maybeQueue", "_containerInsertAfter", "last", "arr", "isHiddenInSequenceExpression", "expressions", "isAlmostConstantAssignment", "scope", "left", "blockScope", "getBlockParent", "hasOwnBinding", "name", "getOwnBinding", "constantViolations", "insertAfter", "get", "map", "self", "isPattern", "unshift", "callee", "isPure", "isMethod", "computed", "temp", "generateDeclaredUidIdentifier", "pushContainer", "fromIndex", "incrementBy", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "msg", "type", "NodePath", "<PERSON><PERSON><PERSON>", "verifiedNodes", "setContext", "replaceWithMultiple", "exports", "hoist", "hoister", "PathHoister", "run"], "sources": ["../../src/path/modification.ts"], "sourcesContent": ["// This file contains methods that modify the path/node in some ways.\n\nimport { getCachedPaths } from \"../cache.ts\";\nimport NodePath from \"./index.ts\";\nimport { _getQueueContexts, pushContext, setScope } from \"./context.ts\";\nimport { _assertUnremoved } from \"./removal.ts\";\nimport {\n  arrowFunctionExpression,\n  assertExpression,\n  assignmentExpression,\n  blockStatement,\n  callExpression,\n  cloneNode,\n  expressionStatement,\n  isAssignmentExpression,\n  isCallExpression,\n  isExportNamedDeclaration,\n  isExpression,\n  isIdentifier,\n  isSequenceExpression,\n  isSuper,\n  thisExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type Scope from \"../scope/index.ts\";\nimport type {\n  NodeList,\n  NodeOrNodeList,\n  NodeListType,\n  NodePaths,\n} from \"./index.ts\";\n\n/**\n * Insert the provided nodes before the current one.\n */\n\nexport function insertBefore<Nodes extends NodeOrNodeList<t.Node>>(\n  this: NodePath,\n  nodes_: Nodes,\n): NodePaths<Nodes> {\n  _assertUnremoved.call(this);\n\n  const nodes = _verifyNodeList.call(this, nodes_);\n\n  const { parentPath, parent } = this;\n\n  if (\n    parentPath.isExpressionStatement() ||\n    parentPath.isLabeledStatement() ||\n    // https://github.com/babel/babel/issues/15293\n    // When Babel transforms `export class String { field }`, the class properties plugin will inject the defineProperty\n    // helper, which depends on the builtins e.g. String, Number, Symbol, etc. To prevent them from being shadowed by local\n    // exports, the helper injector replaces the named export into `class _String { field }; export { _String as String }`,\n    // with `parentPath` here changed to the moved ClassDeclaration, causing rare inconsistency between `parent` and `parentPath`.\n    // Here we retrieve the parent type from the `parent` property. This is a temporary fix and we should revisit when\n    // helpers should get injected.\n    isExportNamedDeclaration(parent) ||\n    (parentPath.isExportDefaultDeclaration() && this.isDeclaration())\n  ) {\n    return parentPath.insertBefore(nodes as Nodes);\n  } else if (\n    (this.isNodeType(\"Expression\") && !this.isJSXElement()) ||\n    (parentPath.isForStatement() && this.key === \"init\")\n  ) {\n    if (this.node) nodes.push(this.node);\n    // @ts-expect-error todo(flow->ts): check that nodes is an array of statements\n    return this.replaceExpressionWithStatements(nodes);\n  } else if (Array.isArray(this.container)) {\n    return _containerInsertBefore.call(this, nodes) as NodePaths<Nodes>;\n  } else if (this.isStatementOrBlock()) {\n    const node = this.node as t.Statement;\n    const shouldInsertCurrentNode =\n      node &&\n      (!this.isExpressionStatement() ||\n        (node as t.ExpressionStatement).expression != null);\n\n    const [blockPath] = this.replaceWith(\n      blockStatement(shouldInsertCurrentNode ? [node] : []),\n    );\n    return blockPath.unshiftContainer(\n      \"body\",\n      nodes as t.Statement[],\n    ) as NodePaths<Nodes>;\n  } else {\n    throw new Error(\n      \"We don't know what to do with this node type. \" +\n        \"We were previously a Statement but we can't fit in here?\",\n    );\n  }\n}\n\nexport function _containerInsert<Nodes extends NodeList<t.Node>>(\n  this: NodePath,\n  from: number,\n  nodes: Nodes,\n): NodePaths<Nodes> {\n  updateSiblingKeys.call(this, from, nodes.length);\n\n  const paths: NodePath[] = [];\n\n  // @ts-expect-error todo(flow->ts): this.container could be a NodePath\n  this.container.splice(from, 0, ...nodes);\n  for (let i = 0; i < nodes.length; i++) {\n    const to = from + i;\n    const path = this.getSibling(to);\n    paths.push(path);\n\n    if (this.context?.queue) {\n      pushContext.call(path, this.context);\n    }\n  }\n\n  const contexts = _getQueueContexts.call(this);\n\n  for (const path of paths) {\n    setScope.call(path);\n    path.debug(\"Inserted.\");\n\n    for (const context of contexts) {\n      context.maybeQueue(path, true);\n    }\n  }\n\n  return paths as NodePaths<Nodes>;\n}\n\nexport function _containerInsertBefore<Nodes extends NodeList<t.Node>>(\n  this: NodePath,\n  nodes: Nodes,\n): NodePaths<Nodes> {\n  return _containerInsert.call(\n    this,\n    this.key as number,\n    nodes,\n  ) as NodePaths<Nodes>;\n}\n\nexport function _containerInsertAfter<Nodes extends NodeList<t.Node>>(\n  this: NodePath,\n  nodes: Nodes,\n): NodePaths<Nodes> {\n  return _containerInsert.call(\n    this,\n    (this.key as number) + 1,\n    nodes,\n  ) as NodePaths<Nodes>;\n}\n\nconst last = <T>(arr: T[]) => arr[arr.length - 1];\n\nfunction isHiddenInSequenceExpression(path: NodePath): boolean {\n  return (\n    isSequenceExpression(path.parent) &&\n    (last(path.parent.expressions) !== path.node ||\n      isHiddenInSequenceExpression(path.parentPath))\n  );\n}\n\nfunction isAlmostConstantAssignment(\n  node: t.Node,\n  scope: Scope,\n): node is t.AssignmentExpression & { left: t.Identifier } {\n  if (!isAssignmentExpression(node) || !isIdentifier(node.left)) {\n    return false;\n  }\n\n  // Not every scope can contain variables. For example, we might be in\n  // a ClassScope either in the ClassHeritage or in a computed key.\n  const blockScope = scope.getBlockParent();\n\n  // If the variable is defined in the current scope and only assigned here,\n  // we can be sure that its value won't change.\n  return (\n    blockScope.hasOwnBinding(node.left.name) &&\n    blockScope.getOwnBinding(node.left.name).constantViolations.length <= 1\n  );\n}\n\n/**\n * Insert the provided nodes after the current one. When inserting nodes after an\n * expression, ensure that the completion record is correct by pushing the current node.\n */\n\nexport function insertAfter<Nodes extends NodeOrNodeList<t.Node>>(\n  this: NodePath,\n  nodes_: Nodes,\n): NodePaths<Nodes> {\n  _assertUnremoved.call(this);\n\n  if (this.isSequenceExpression()) {\n    return last(this.get(\"expressions\")).insertAfter(nodes_);\n  }\n\n  const nodes = _verifyNodeList.call(this, nodes_);\n\n  const { parentPath, parent } = this;\n  if (\n    parentPath.isExpressionStatement() ||\n    parentPath.isLabeledStatement() ||\n    // see insertBefore\n    isExportNamedDeclaration(parent) ||\n    (parentPath.isExportDefaultDeclaration() && this.isDeclaration())\n  ) {\n    return parentPath.insertAfter(\n      nodes.map(node => {\n        // Usually after an expression we can safely insert another expression:\n        //   A.insertAfter(B)\n        //     foo = A;  -> foo = (A, B);\n        // If A is an expression statement, it isn't safe anymore so we need to\n        // convert B to an expression statement\n        //     A;        -> A; B // No semicolon! It could break if followed by [!\n        return isExpression(node) ? expressionStatement(node) : node;\n      }),\n      // todo: this cast is unsound, we wrap some expression nodes in expressionStatement but never unwrap them in the return values.\n    ) as NodePaths<Nodes>;\n  } else if (\n    (this.isNodeType(\"Expression\") &&\n      !this.isJSXElement() &&\n      !parentPath.isJSXElement()) ||\n    (parentPath.isForStatement() && this.key === \"init\")\n  ) {\n    const self = this as NodePath<t.Expression | t.VariableDeclaration>;\n    if (self.node) {\n      const node = self.node;\n      let { scope } = this;\n\n      if (scope.path.isPattern()) {\n        assertExpression(node);\n\n        self.replaceWith(callExpression(arrowFunctionExpression([], node), []));\n        (self.get(\"callee.body\") as NodePath<t.Expression>).insertAfter(nodes);\n        // todo: this cast is unsound, we wrap nodes in the IIFE but never unwrap them in the return values.\n        // consider just returning the insertAfter result.\n        return [self] as NodePaths<Nodes>;\n      }\n\n      if (isHiddenInSequenceExpression(self)) {\n        nodes.unshift(node);\n      }\n      // We need to preserve the value of this expression.\n      else if (isCallExpression(node) && isSuper(node.callee)) {\n        nodes.unshift(node);\n        // `super(...)` always evaluates to `this`.\n        nodes.push(thisExpression());\n      } else if (isAlmostConstantAssignment(node, scope)) {\n        nodes.unshift(node);\n        nodes.push(cloneNode(node.left));\n      } else if (scope.isPure(node, true)) {\n        // Insert the nodes before rather than after; it's not observable.\n        nodes.push(node);\n      } else {\n        // Inserting after the computed key of a method should insert the\n        // temporary binding in the method's parent's scope.\n        if (parentPath.isMethod({ computed: true, key: node })) {\n          scope = scope.parent;\n        }\n        const temp = scope.generateDeclaredUidIdentifier();\n        nodes.unshift(\n          expressionStatement(\n            // @ts-expect-error todo(flow->ts): This can be a variable\n            // declaration in the \"init\" of a for statement, but that's\n            // invalid here.\n            assignmentExpression(\"=\", cloneNode(temp), node),\n          ),\n        );\n        nodes.push(expressionStatement(cloneNode(temp)));\n      }\n    }\n    // @ts-expect-error todo(flow->ts): check that nodes is an array of statements\n    return this.replaceExpressionWithStatements(nodes);\n  } else if (Array.isArray(this.container)) {\n    return _containerInsertAfter.call(this, nodes) as NodePaths<Nodes>;\n  } else if (this.isStatementOrBlock()) {\n    const node = this.node as t.Statement;\n    const shouldInsertCurrentNode =\n      node &&\n      (!this.isExpressionStatement() ||\n        (node as t.ExpressionStatement).expression != null);\n\n    const [blockPath] = this.replaceWith(\n      blockStatement(shouldInsertCurrentNode ? [node] : []),\n    );\n    return blockPath.pushContainer(\n      \"body\",\n      nodes as t.Statement[],\n    ) as NodePaths<Nodes>;\n  } else {\n    throw new Error(\n      \"We don't know what to do with this node type. \" +\n        \"We were previously a Statement but we can't fit in here?\",\n    );\n  }\n}\n\n/**\n * Update all sibling node paths after `fromIndex` by `incrementBy`.\n */\n\nexport function updateSiblingKeys(\n  this: NodePath,\n  fromIndex: number,\n  incrementBy: number,\n) {\n  if (!this.parent) return;\n\n  const paths = getCachedPaths(this);\n  if (!paths) return;\n\n  for (const [, path] of paths) {\n    if (\n      typeof path.key === \"number\" &&\n      path.container === this.container &&\n      path.key >= fromIndex\n    ) {\n      path.key += incrementBy;\n    }\n  }\n}\n\nexport function _verifyNodeList<N extends t.Node | null>(\n  this: NodePath,\n  nodes: N | N[],\n) {\n  if (!nodes) {\n    return [];\n  }\n\n  if (!Array.isArray(nodes)) {\n    nodes = [nodes];\n  }\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    let msg;\n\n    if (!node) {\n      msg = \"has falsy node\";\n    } else if (typeof node !== \"object\") {\n      msg = \"contains a non-object node\";\n    } else if (!node.type) {\n      msg = \"without a type\";\n    } else if (node instanceof NodePath) {\n      msg = \"has a NodePath when it expected a raw object\";\n    }\n\n    if (msg) {\n      const type = Array.isArray(node) ? \"array\" : typeof node;\n      throw new Error(\n        `Node list ${msg} with the index of ${i} and type of ${type}`,\n      );\n    }\n  }\n\n  return nodes;\n}\n\ntype NodeKeyOfArrays<N extends t.Node> = {\n  [P in string & keyof N]-?: N[P] extends Array<t.Node | null> ? P : never;\n}[string & keyof N];\n\nexport function unshiftContainer<\n  N extends t.Node,\n  K extends NodeKeyOfArrays<N>,\n  Nodes extends NodeOrNodeList<NodeListType<N, K>>,\n>(this: NodePath<N>, listKey: K, nodes: Nodes): NodePaths<Nodes> {\n  _assertUnremoved.call(this);\n\n  const verifiedNodes = _verifyNodeList.call(this, nodes);\n\n  // get the first path and insert our nodes before it, if it doesn't exist then it\n  // doesn't matter, our nodes will be inserted anyway\n  const container = (this.node as N)[listKey] as t.Node[];\n  const path = NodePath.get({\n    parentPath: this,\n    parent: this.node,\n    container,\n    listKey,\n    key: 0,\n  }).setContext(this.context);\n\n  return _containerInsertBefore.call(path, verifiedNodes) as NodePaths<Nodes>;\n}\n\nexport function pushContainer<\n  N extends t.Node,\n  K extends NodeKeyOfArrays<N>,\n  Nodes extends NodeOrNodeList<NodeListType<N, K>>,\n>(this: NodePath<N>, listKey: K, nodes: Nodes): NodePaths<Nodes> {\n  _assertUnremoved.call(this);\n\n  const verifiedNodes = _verifyNodeList.call(this, nodes);\n\n  // get an invisible path that represents the last node + 1 and replace it with our\n  // nodes, effectively inlining it\n\n  const container = (this.node as N)[listKey] as t.Node[];\n  const path = NodePath.get({\n    parentPath: this,\n    parent: this.node,\n    container,\n    listKey,\n    key: container.length,\n  }).setContext(this.context);\n\n  return path.replaceWithMultiple(verifiedNodes) as NodePaths<Nodes>;\n}\n\nimport PathHoister from \"./lib/hoister.ts\" with { if: \"!process.env.BABEL_8_BREAKING && !USE_ESM\" };\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  /**\n   * Hoist the current node to the highest scope possible and return a UID\n   * referencing it.\n   */\n  // eslint-disable-next-line no-restricted-globals\n  exports.hoist = function hoist<N extends t.Node>(\n    this: NodePath<N>,\n    scope: Scope = this.scope,\n  ) {\n    const hoister = new PathHoister<N>(this, scope);\n    return hoister.run();\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;AAEA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,EAAA,GAAAJ,OAAA;AAiZA,IAAAK,QAAA,GAAAL,OAAA;AAAoG;EAhZlGM,uBAAuB;EACvBC,gBAAgB;EAChBC,oBAAoB;EACpBC,cAAc;EACdC,cAAc;EACdC,SAAS;EACTC,mBAAmB;EACnBC,sBAAsB;EACtBC,gBAAgB;EAChBC,wBAAwB;EACxBC,YAAY;EACZC,YAAY;EACZC,oBAAoB;EACpBC,OAAO;EACPC;AAAc,IAAAhB,EAAA;AAeT,SAASiB,YAAYA,CAE1BC,MAAa,EACK;EAClBC,yBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;EAE3B,MAAMC,KAAK,GAAGC,eAAe,CAACF,IAAI,CAAC,IAAI,EAAEF,MAAM,CAAC;EAEhD,MAAM;IAAEK,UAAU;IAAEC;EAAO,CAAC,GAAG,IAAI;EAEnC,IACED,UAAU,CAACE,qBAAqB,CAAC,CAAC,IAClCF,UAAU,CAACG,kBAAkB,CAAC,CAAC,IAQ/Bf,wBAAwB,CAACa,MAAM,CAAC,IAC/BD,UAAU,CAACI,0BAA0B,CAAC,CAAC,IAAI,IAAI,CAACC,aAAa,CAAC,CAAE,EACjE;IACA,OAAOL,UAAU,CAACN,YAAY,CAACI,KAAc,CAAC;EAChD,CAAC,MAAM,IACJ,IAAI,CAACQ,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,IACrDP,UAAU,CAACQ,cAAc,CAAC,CAAC,IAAI,IAAI,CAACC,GAAG,KAAK,MAAO,EACpD;IACA,IAAI,IAAI,CAACC,IAAI,EAAEZ,KAAK,CAACa,IAAI,CAAC,IAAI,CAACD,IAAI,CAAC;IAEpC,OAAO,IAAI,CAACE,+BAA+B,CAACd,KAAK,CAAC;EACpD,CAAC,MAAM,IAAIe,KAAK,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;IACxC,OAAOC,sBAAsB,CAACnB,IAAI,CAAC,IAAI,EAAEC,KAAK,CAAC;EACjD,CAAC,MAAM,IAAI,IAAI,CAACmB,kBAAkB,CAAC,CAAC,EAAE;IACpC,MAAMP,IAAI,GAAG,IAAI,CAACA,IAAmB;IACrC,MAAMQ,uBAAuB,GAC3BR,IAAI,KACH,CAAC,IAAI,CAACR,qBAAqB,CAAC,CAAC,IAC3BQ,IAAI,CAA2BS,UAAU,IAAI,IAAI,CAAC;IAEvD,MAAM,CAACC,SAAS,CAAC,GAAG,IAAI,CAACC,WAAW,CAClCvC,cAAc,CAACoC,uBAAuB,GAAG,CAACR,IAAI,CAAC,GAAG,EAAE,CACtD,CAAC;IACD,OAAOU,SAAS,CAACE,gBAAgB,CAC/B,MAAM,EACNxB,KACF,CAAC;EACH,CAAC,MAAM;IACL,MAAM,IAAIyB,KAAK,CACb,gDAAgD,GAC9C,0DACJ,CAAC;EACH;AACF;AAEO,SAASC,gBAAgBA,CAE9BC,IAAY,EACZ3B,KAAY,EACM;EAClB4B,iBAAiB,CAAC7B,IAAI,CAAC,IAAI,EAAE4B,IAAI,EAAE3B,KAAK,CAAC6B,MAAM,CAAC;EAEhD,MAAMC,KAAiB,GAAG,EAAE;EAG5B,IAAI,CAACb,SAAS,CAACc,MAAM,CAACJ,IAAI,EAAE,CAAC,EAAE,GAAG3B,KAAK,CAAC;EACxC,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhC,KAAK,CAAC6B,MAAM,EAAEG,CAAC,EAAE,EAAE;IAAA,IAAAC,aAAA;IACrC,MAAMC,EAAE,GAAGP,IAAI,GAAGK,CAAC;IACnB,MAAMG,IAAI,GAAG,IAAI,CAACC,UAAU,CAACF,EAAE,CAAC;IAChCJ,KAAK,CAACjB,IAAI,CAACsB,IAAI,CAAC;IAEhB,KAAAF,aAAA,GAAI,IAAI,CAACI,OAAO,aAAZJ,aAAA,CAAcK,KAAK,EAAE;MACvBC,oBAAW,CAACxC,IAAI,CAACoC,IAAI,EAAE,IAAI,CAACE,OAAO,CAAC;IACtC;EACF;EAEA,MAAMG,QAAQ,GAAGC,0BAAiB,CAAC1C,IAAI,CAAC,IAAI,CAAC;EAE7C,KAAK,MAAMoC,IAAI,IAAIL,KAAK,EAAE;IACxBY,iBAAQ,CAAC3C,IAAI,CAACoC,IAAI,CAAC;IACnBA,IAAI,CAACQ,KAAK,CAAC,WAAW,CAAC;IAEvB,KAAK,MAAMN,OAAO,IAAIG,QAAQ,EAAE;MAC9BH,OAAO,CAACO,UAAU,CAACT,IAAI,EAAE,IAAI,CAAC;IAChC;EACF;EAEA,OAAOL,KAAK;AACd;AAEO,SAASZ,sBAAsBA,CAEpClB,KAAY,EACM;EAClB,OAAO0B,gBAAgB,CAAC3B,IAAI,CAC1B,IAAI,EACJ,IAAI,CAACY,GAAG,EACRX,KACF,CAAC;AACH;AAEO,SAAS6C,qBAAqBA,CAEnC7C,KAAY,EACM;EAClB,OAAO0B,gBAAgB,CAAC3B,IAAI,CAC1B,IAAI,EACH,IAAI,CAACY,GAAG,GAAc,CAAC,EACxBX,KACF,CAAC;AACH;AAEA,MAAM8C,IAAI,GAAOC,GAAQ,IAAKA,GAAG,CAACA,GAAG,CAAClB,MAAM,GAAG,CAAC,CAAC;AAEjD,SAASmB,4BAA4BA,CAACb,IAAc,EAAW;EAC7D,OACE1C,oBAAoB,CAAC0C,IAAI,CAAChC,MAAM,CAAC,KAChC2C,IAAI,CAACX,IAAI,CAAChC,MAAM,CAAC8C,WAAW,CAAC,KAAKd,IAAI,CAACvB,IAAI,IAC1CoC,4BAA4B,CAACb,IAAI,CAACjC,UAAU,CAAC,CAAC;AAEpD;AAEA,SAASgD,0BAA0BA,CACjCtC,IAAY,EACZuC,KAAY,EAC6C;EACzD,IAAI,CAAC/D,sBAAsB,CAACwB,IAAI,CAAC,IAAI,CAACpB,YAAY,CAACoB,IAAI,CAACwC,IAAI,CAAC,EAAE;IAC7D,OAAO,KAAK;EACd;EAIA,MAAMC,UAAU,GAAGF,KAAK,CAACG,cAAc,CAAC,CAAC;EAIzC,OACED,UAAU,CAACE,aAAa,CAAC3C,IAAI,CAACwC,IAAI,CAACI,IAAI,CAAC,IACxCH,UAAU,CAACI,aAAa,CAAC7C,IAAI,CAACwC,IAAI,CAACI,IAAI,CAAC,CAACE,kBAAkB,CAAC7B,MAAM,IAAI,CAAC;AAE3E;AAOO,SAAS8B,WAAWA,CAEzB9D,MAAa,EACK;EAClBC,yBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;EAE3B,IAAI,IAAI,CAACN,oBAAoB,CAAC,CAAC,EAAE;IAC/B,OAAOqD,IAAI,CAAC,IAAI,CAACc,GAAG,CAAC,aAAa,CAAC,CAAC,CAACD,WAAW,CAAC9D,MAAM,CAAC;EAC1D;EAEA,MAAMG,KAAK,GAAGC,eAAe,CAACF,IAAI,CAAC,IAAI,EAAEF,MAAM,CAAC;EAEhD,MAAM;IAAEK,UAAU;IAAEC;EAAO,CAAC,GAAG,IAAI;EACnC,IACED,UAAU,CAACE,qBAAqB,CAAC,CAAC,IAClCF,UAAU,CAACG,kBAAkB,CAAC,CAAC,IAE/Bf,wBAAwB,CAACa,MAAM,CAAC,IAC/BD,UAAU,CAACI,0BAA0B,CAAC,CAAC,IAAI,IAAI,CAACC,aAAa,CAAC,CAAE,EACjE;IACA,OAAOL,UAAU,CAACyD,WAAW,CAC3B3D,KAAK,CAAC6D,GAAG,CAACjD,IAAI,IAAI;MAOhB,OAAOrB,YAAY,CAACqB,IAAI,CAAC,GAAGzB,mBAAmB,CAACyB,IAAI,CAAC,GAAGA,IAAI;IAC9D,CAAC,CAEH,CAAC;EACH,CAAC,MAAM,IACJ,IAAI,CAACJ,UAAU,CAAC,YAAY,CAAC,IAC5B,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,IACpB,CAACP,UAAU,CAACO,YAAY,CAAC,CAAC,IAC3BP,UAAU,CAACQ,cAAc,CAAC,CAAC,IAAI,IAAI,CAACC,GAAG,KAAK,MAAO,EACpD;IACA,MAAMmD,IAAI,GAAG,IAAsD;IACnE,IAAIA,IAAI,CAAClD,IAAI,EAAE;MACb,MAAMA,IAAI,GAAGkD,IAAI,CAAClD,IAAI;MACtB,IAAI;QAAEuC;MAAM,CAAC,GAAG,IAAI;MAEpB,IAAIA,KAAK,CAAChB,IAAI,CAAC4B,SAAS,CAAC,CAAC,EAAE;QAC1BjF,gBAAgB,CAAC8B,IAAI,CAAC;QAEtBkD,IAAI,CAACvC,WAAW,CAACtC,cAAc,CAACJ,uBAAuB,CAAC,EAAE,EAAE+B,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACtEkD,IAAI,CAACF,GAAG,CAAC,aAAa,CAAC,CAA4BD,WAAW,CAAC3D,KAAK,CAAC;QAGtE,OAAO,CAAC8D,IAAI,CAAC;MACf;MAEA,IAAId,4BAA4B,CAACc,IAAI,CAAC,EAAE;QACtC9D,KAAK,CAACgE,OAAO,CAACpD,IAAI,CAAC;MACrB,CAAC,MAEI,IAAIvB,gBAAgB,CAACuB,IAAI,CAAC,IAAIlB,OAAO,CAACkB,IAAI,CAACqD,MAAM,CAAC,EAAE;QACvDjE,KAAK,CAACgE,OAAO,CAACpD,IAAI,CAAC;QAEnBZ,KAAK,CAACa,IAAI,CAAClB,cAAc,CAAC,CAAC,CAAC;MAC9B,CAAC,MAAM,IAAIuD,0BAA0B,CAACtC,IAAI,EAAEuC,KAAK,CAAC,EAAE;QAClDnD,KAAK,CAACgE,OAAO,CAACpD,IAAI,CAAC;QACnBZ,KAAK,CAACa,IAAI,CAAC3B,SAAS,CAAC0B,IAAI,CAACwC,IAAI,CAAC,CAAC;MAClC,CAAC,MAAM,IAAID,KAAK,CAACe,MAAM,CAACtD,IAAI,EAAE,IAAI,CAAC,EAAE;QAEnCZ,KAAK,CAACa,IAAI,CAACD,IAAI,CAAC;MAClB,CAAC,MAAM;QAGL,IAAIV,UAAU,CAACiE,QAAQ,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEzD,GAAG,EAAEC;QAAK,CAAC,CAAC,EAAE;UACtDuC,KAAK,GAAGA,KAAK,CAAChD,MAAM;QACtB;QACA,MAAMkE,IAAI,GAAGlB,KAAK,CAACmB,6BAA6B,CAAC,CAAC;QAClDtE,KAAK,CAACgE,OAAO,CACX7E,mBAAmB,CAIjBJ,oBAAoB,CAAC,GAAG,EAAEG,SAAS,CAACmF,IAAI,CAAC,EAAEzD,IAAI,CACjD,CACF,CAAC;QACDZ,KAAK,CAACa,IAAI,CAAC1B,mBAAmB,CAACD,SAAS,CAACmF,IAAI,CAAC,CAAC,CAAC;MAClD;IACF;IAEA,OAAO,IAAI,CAACvD,+BAA+B,CAACd,KAAK,CAAC;EACpD,CAAC,MAAM,IAAIe,KAAK,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;IACxC,OAAO4B,qBAAqB,CAAC9C,IAAI,CAAC,IAAI,EAAEC,KAAK,CAAC;EAChD,CAAC,MAAM,IAAI,IAAI,CAACmB,kBAAkB,CAAC,CAAC,EAAE;IACpC,MAAMP,IAAI,GAAG,IAAI,CAACA,IAAmB;IACrC,MAAMQ,uBAAuB,GAC3BR,IAAI,KACH,CAAC,IAAI,CAACR,qBAAqB,CAAC,CAAC,IAC3BQ,IAAI,CAA2BS,UAAU,IAAI,IAAI,CAAC;IAEvD,MAAM,CAACC,SAAS,CAAC,GAAG,IAAI,CAACC,WAAW,CAClCvC,cAAc,CAACoC,uBAAuB,GAAG,CAACR,IAAI,CAAC,GAAG,EAAE,CACtD,CAAC;IACD,OAAOU,SAAS,CAACiD,aAAa,CAC5B,MAAM,EACNvE,KACF,CAAC;EACH,CAAC,MAAM;IACL,MAAM,IAAIyB,KAAK,CACb,gDAAgD,GAC9C,0DACJ,CAAC;EACH;AACF;AAMO,SAASG,iBAAiBA,CAE/B4C,SAAiB,EACjBC,WAAmB,EACnB;EACA,IAAI,CAAC,IAAI,CAACtE,MAAM,EAAE;EAElB,MAAM2B,KAAK,GAAG,IAAA4C,qBAAc,EAAC,IAAI,CAAC;EAClC,IAAI,CAAC5C,KAAK,EAAE;EAEZ,KAAK,MAAM,GAAGK,IAAI,CAAC,IAAIL,KAAK,EAAE;IAC5B,IACE,OAAOK,IAAI,CAACxB,GAAG,KAAK,QAAQ,IAC5BwB,IAAI,CAAClB,SAAS,KAAK,IAAI,CAACA,SAAS,IACjCkB,IAAI,CAACxB,GAAG,IAAI6D,SAAS,EACrB;MACArC,IAAI,CAACxB,GAAG,IAAI8D,WAAW;IACzB;EACF;AACF;AAEO,SAASxE,eAAeA,CAE7BD,KAAc,EACd;EACA,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,EAAE;EACX;EAEA,IAAI,CAACe,KAAK,CAACC,OAAO,CAAChB,KAAK,CAAC,EAAE;IACzBA,KAAK,GAAG,CAACA,KAAK,CAAC;EACjB;EAEA,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhC,KAAK,CAAC6B,MAAM,EAAEG,CAAC,EAAE,EAAE;IACrC,MAAMpB,IAAI,GAAGZ,KAAK,CAACgC,CAAC,CAAC;IACrB,IAAI2C,GAAG;IAEP,IAAI,CAAC/D,IAAI,EAAE;MACT+D,GAAG,GAAG,gBAAgB;IACxB,CAAC,MAAM,IAAI,OAAO/D,IAAI,KAAK,QAAQ,EAAE;MACnC+D,GAAG,GAAG,4BAA4B;IACpC,CAAC,MAAM,IAAI,CAAC/D,IAAI,CAACgE,IAAI,EAAE;MACrBD,GAAG,GAAG,gBAAgB;IACxB,CAAC,MAAM,IAAI/D,IAAI,YAAYiE,cAAQ,EAAE;MACnCF,GAAG,GAAG,8CAA8C;IACtD;IAEA,IAAIA,GAAG,EAAE;MACP,MAAMC,IAAI,GAAG7D,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,GAAG,OAAO,GAAG,OAAOA,IAAI;MACxD,MAAM,IAAIa,KAAK,CACb,aAAakD,GAAG,sBAAsB3C,CAAC,gBAAgB4C,IAAI,EAC7D,CAAC;IACH;EACF;EAEA,OAAO5E,KAAK;AACd;AAMO,SAASwB,gBAAgBA,CAIXsD,OAAU,EAAE9E,KAAY,EAAoB;EAC/DF,yBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;EAE3B,MAAMgF,aAAa,GAAG9E,eAAe,CAACF,IAAI,CAAC,IAAI,EAAEC,KAAK,CAAC;EAIvD,MAAMiB,SAAS,GAAI,IAAI,CAACL,IAAI,CAAOkE,OAAO,CAAa;EACvD,MAAM3C,IAAI,GAAG0C,cAAQ,CAACjB,GAAG,CAAC;IACxB1D,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,IAAI,CAACS,IAAI;IACjBK,SAAS;IACT6D,OAAO;IACPnE,GAAG,EAAE;EACP,CAAC,CAAC,CAACqE,UAAU,CAAC,IAAI,CAAC3C,OAAO,CAAC;EAE3B,OAAOnB,sBAAsB,CAACnB,IAAI,CAACoC,IAAI,EAAE4C,aAAa,CAAC;AACzD;AAEO,SAASR,aAAaA,CAIRO,OAAU,EAAE9E,KAAY,EAAoB;EAC/DF,yBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;EAE3B,MAAMgF,aAAa,GAAG9E,eAAe,CAACF,IAAI,CAAC,IAAI,EAAEC,KAAK,CAAC;EAKvD,MAAMiB,SAAS,GAAI,IAAI,CAACL,IAAI,CAAOkE,OAAO,CAAa;EACvD,MAAM3C,IAAI,GAAG0C,cAAQ,CAACjB,GAAG,CAAC;IACxB1D,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,IAAI,CAACS,IAAI;IACjBK,SAAS;IACT6D,OAAO;IACPnE,GAAG,EAAEM,SAAS,CAACY;EACjB,CAAC,CAAC,CAACmD,UAAU,CAAC,IAAI,CAAC3C,OAAO,CAAC;EAE3B,OAAOF,IAAI,CAAC8C,mBAAmB,CAACF,aAAa,CAAC;AAChD;AAG+C;EAM7CG,OAAO,CAACC,KAAK,GAAG,SAASA,KAAKA,CAE5BhC,KAAY,GAAG,IAAI,CAACA,KAAK,EACzB;IACA,MAAMiC,OAAO,GAAG,IAAIC,gBAAW,CAAI,IAAI,EAAElC,KAAK,CAAC;IAC/C,OAAOiC,OAAO,CAACE,GAAG,CAAC,CAAC;EACtB,CAAC;AACH", "ignoreList": []}