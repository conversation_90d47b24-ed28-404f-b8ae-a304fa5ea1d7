{"version": 3, "names": ["isModule", "path", "node", "sourceType"], "sources": ["../src/is-module.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\n\n/**\n * A small utility to check if a file qualifies as a module.\n */\nexport default function isModule(path: NodePath<t.Program>) {\n  return path.node.sourceType === \"module\";\n}\n"], "mappings": ";;;;;;AAMe,SAASA,QAAQA,CAACC,IAAyB,EAAE;EAC1D,OAAOA,IAAI,CAACC,IAAI,CAACC,UAAU,KAAK,QAAQ;AAC1C", "ignoreList": []}