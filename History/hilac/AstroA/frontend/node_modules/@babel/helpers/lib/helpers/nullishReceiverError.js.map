{"version": 3, "names": ["_nullishReceiverError", "r", "TypeError"], "sources": ["../../src/helpers/nullishReceiverError.js"], "sourcesContent": ["/* @minVersion 7.22.6 */\n\n// eslint-disable-next-line no-unused-vars\nexport default function _nullishReceiverError(r) {\n  throw new TypeError(\"Cannot set property of null or undefined.\");\n}\n"], "mappings": ";;;;;;AAGe,SAASA,qBAAqBA,CAACC,CAAC,EAAE;EAC/C,MAAM,IAAIC,SAAS,CAAC,2CAA2C,CAAC;AAClE", "ignoreList": []}