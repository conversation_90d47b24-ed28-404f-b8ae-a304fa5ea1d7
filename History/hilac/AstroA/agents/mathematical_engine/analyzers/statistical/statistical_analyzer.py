"""
Statistical Analysis Engine for Market Data
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from scipy import stats
import logging

from shared.mathematical.utils import (
    safe_divide, normalize_series, calculate_returns,
    detect_outliers, validate_data_quality, calculate_derivative, calculate_integral
)
from shared.mathematical.constants import (
    DEFAULT_PERIODS, CONFIDENCE_LEVELS, TRADING_DAYS_PER_YEAR
)
from shared.mathematical.formulas import CalculusFormulas, ProbabilityFormulas

class StatisticalAnalyzer:
    """Advanced statistical analysis for market data"""

    def __init__(self):
        self.logger = logging.getLogger("statistical_analyzer")
        self.calculus = CalculusFormulas()
        self.probability = ProbabilityFormulas()

    def calculate_moving_averages(self, data: pd.Series, periods: List[int] = None) -> pd.DataFrame:
        """Calculate multiple moving averages"""
        if periods is None:
            periods = [DEFAULT_PERIODS['short_ma'], DEFAULT_PERIODS['medium_ma'], DEFAULT_PERIODS['long_ma']]

        result = pd.DataFrame(index=data.index)

        for period in periods:
            # Simple Moving Average
            result[f'sma_{period}'] = data.rolling(window=period).mean()

            # Exponential Moving Average
            result[f'ema_{period}'] = data.ewm(span=period).mean()

            # Weighted Moving Average
            weights = np.arange(1, period + 1)
            result[f'wma_{period}'] = data.rolling(window=period).apply(
                lambda x: np.dot(x, weights) / weights.sum(), raw=True
            )

        return result

    def calculate_bollinger_bands(self, data: pd.Series, period: int = None,
                                 std_dev: float = 2.0) -> pd.DataFrame:
        """Calculate Bollinger Bands"""
        if period is None:
            period = DEFAULT_PERIODS['bollinger_period']

        sma = data.rolling(window=period).mean()
        std = data.rolling(window=period).std()

        return pd.DataFrame({
            'bb_middle': sma,
            'bb_upper': sma + (std * std_dev),
            'bb_lower': sma - (std * std_dev),
            'bb_width': (std * std_dev * 2) / sma,
            'bb_position': (data - sma) / (std * std_dev)
        }, index=data.index)

    def calculate_statistical_moments(self, data: pd.Series, window: int = 50) -> pd.DataFrame:
        """Calculate rolling statistical moments"""
        return pd.DataFrame({
            'mean': data.rolling(window=window).mean(),
            'std': data.rolling(window=window).std(),
            'variance': data.rolling(window=window).var(),
            'skewness': data.rolling(window=window).skew(),
            'kurtosis': data.rolling(window=window).kurt(),
            'median': data.rolling(window=window).median(),
            'quantile_25': data.rolling(window=window).quantile(0.25),
            'quantile_75': data.rolling(window=window).quantile(0.75)
        }, index=data.index)

    def calculate_z_scores(self, data: pd.Series, window: int = 50) -> pd.Series:
        """Calculate rolling Z-scores"""
        rolling_mean = data.rolling(window=window).mean()
        rolling_std = data.rolling(window=window).std()
        return safe_divide(data - rolling_mean, rolling_std)

    def perform_normality_tests(self, data: pd.Series) -> Dict[str, Dict]:
        """Perform various normality tests"""
        # Remove NaN values
        clean_data = data.dropna()

        if len(clean_data) < 8:
            return {"error": "Insufficient data for normality tests"}

        results = {}

        # Shapiro-Wilk test (for small samples)
        if len(clean_data) <= 5000:
            stat, p_value = stats.shapiro(clean_data)
            results['shapiro_wilk'] = {
                'statistic': stat,
                'p_value': p_value,
                'is_normal': p_value > 0.05
            }

        # Kolmogorov-Smirnov test
        stat, p_value = stats.kstest(clean_data, 'norm', args=(clean_data.mean(), clean_data.std()))
        results['kolmogorov_smirnov'] = {
            'statistic': stat,
            'p_value': p_value,
            'is_normal': p_value > 0.05
        }

        # Anderson-Darling test
        stat, critical_values, significance_levels = stats.anderson(clean_data, dist='norm')
        results['anderson_darling'] = {
            'statistic': stat,
            'critical_values': critical_values.tolist(),
            'significance_levels': significance_levels.tolist(),
            'is_normal': stat < critical_values[2]  # 5% significance level
        }

        return results

    def calculate_technical_indicators(self, ohlcv_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate comprehensive technical indicators"""
        close = ohlcv_data['close_price']
        high = ohlcv_data['high_price']
        low = ohlcv_data['low_price']
        volume = ohlcv_data['volume']

        indicators = pd.DataFrame(index=ohlcv_data.index)

        # RSI (Relative Strength Index)
        delta = close.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = safe_divide(gain, loss)
        indicators['rsi'] = 100 - (100 / (1 + rs))

        # MACD (Moving Average Convergence Divergence)
        ema_12 = close.ewm(span=12).mean()
        ema_26 = close.ewm(span=26).mean()
        indicators['macd'] = ema_12 - ema_26
        indicators['macd_signal'] = indicators['macd'].ewm(span=9).mean()
        indicators['macd_histogram'] = indicators['macd'] - indicators['macd_signal']

        # Stochastic Oscillator
        lowest_low = low.rolling(window=14).min()
        highest_high = high.rolling(window=14).max()
        indicators['stoch_k'] = 100 * safe_divide(
            close - lowest_low,
            highest_high - lowest_low
        )
        indicators['stoch_d'] = indicators['stoch_k'].rolling(window=3).mean()

        # Average True Range (ATR)
        tr1 = high - low
        tr2 = np.abs(high - close.shift(1))
        tr3 = np.abs(low - close.shift(1))
        true_range = np.maximum(tr1, np.maximum(tr2, tr3))
        indicators['atr'] = true_range.rolling(window=14).mean()

        # Volume indicators
        indicators['volume_sma'] = volume.rolling(window=20).mean()
        indicators['volume_ratio'] = safe_divide(volume, indicators['volume_sma'])

        # Calculus-based indicators
        indicators['price_momentum'] = self.calculus.derivative_price_momentum(close)
        indicators['price_acceleration'] = self.calculus.second_derivative_acceleration(close)

        return indicators

    def detect_regime_changes(self, data: pd.Series, window: int = 50) -> pd.DataFrame:
        """Detect market regime changes using statistical methods"""
        # Calculate rolling statistics
        rolling_mean = data.rolling(window=window).mean()
        rolling_std = data.rolling(window=window).std()
        rolling_vol = calculate_returns(data).rolling(window=window).std() * np.sqrt(TRADING_DAYS_PER_YEAR)

        # Regime indicators
        regime_data = pd.DataFrame({
            'price_trend': np.where(data > rolling_mean, 1, -1),
            'volatility_regime': np.where(rolling_vol > rolling_vol.median(), 1, 0),
            'momentum': data.pct_change(window),
            'mean_reversion_signal': self.calculate_z_scores(data, window)
        }, index=data.index)

        # Composite regime score
        regime_data['regime_score'] = (
            regime_data['price_trend'] * 0.4 +
            regime_data['volatility_regime'] * 0.3 +
            np.sign(regime_data['momentum']) * 0.3
        )

        return regime_data

    def calculate_advanced_statistics(self, data: pd.Series, window: int = 50) -> pd.DataFrame:
        """Calculate advanced statistical measures"""
        results = pd.DataFrame(index=data.index)

        # Higher order moments
        results['mean'] = data.rolling(window).mean()
        results['variance'] = data.rolling(window).var()
        results['skewness'] = data.rolling(window).skew()
        results['kurtosis'] = data.rolling(window).kurt()

        # Percentile-based measures
        results['iqr'] = (data.rolling(window).quantile(0.75) -
                         data.rolling(window).quantile(0.25))
        results['median_abs_deviation'] = data.rolling(window).apply(
            lambda x: np.median(np.abs(x - np.median(x)))
        )

        # Entropy measures
        def calculate_entropy(series):
            if len(series) < 5:
                return np.nan
            # Bin the data for entropy calculation
            hist, _ = np.histogram(series, bins=10)
            hist = hist[hist > 0]  # Remove zero bins
            probabilities = hist / np.sum(hist)
            return -np.sum(probabilities * np.log2(probabilities))

        results['entropy'] = data.rolling(window).apply(calculate_entropy)

        # Autocorrelation
        results['autocorr_1'] = data.rolling(window).apply(lambda x: x.autocorr(lag=1))
        results['autocorr_5'] = data.rolling(window).apply(lambda x: x.autocorr(lag=5))

        return results

    def perform_comprehensive_analysis(self, market_data: pd.DataFrame,
                                     symbol: str) -> Dict[str, Union[pd.DataFrame, Dict, float]]:
        """Perform comprehensive statistical analysis on market data"""
        self.logger.info(f"Starting comprehensive statistical analysis for {symbol}")

        # Data quality check
        data_quality = validate_data_quality(market_data)
        if data_quality['data_quality_score'] < 50:
            self.logger.warning(f"Low data quality score: {data_quality['data_quality_score']}")

        close_prices = market_data['close_price']
        returns = calculate_returns(close_prices)

        analysis_results = {
            'symbol': symbol,
            'data_quality': data_quality,
            'basic_stats': {
                'count': len(market_data),
                'mean_price': close_prices.mean(),
                'std_price': close_prices.std(),
                'min_price': close_prices.min(),
                'max_price': close_prices.max(),
                'current_price': close_prices.iloc[-1] if len(close_prices) > 0 else None
            },
            'return_stats': {
                'mean_return': returns.mean(),
                'std_return': returns.std(),
                'skewness': returns.skew(),
                'kurtosis': returns.kurt(),
                'annualized_volatility': returns.std() * np.sqrt(TRADING_DAYS_PER_YEAR)
            },
            'moving_averages': self.calculate_moving_averages(close_prices),
            'bollinger_bands': self.calculate_bollinger_bands(close_prices),
            'statistical_moments': self.calculate_statistical_moments(close_prices),
            'technical_indicators': self.calculate_technical_indicators(market_data),
            'regime_analysis': self.detect_regime_changes(close_prices),
            'normality_tests': self.perform_normality_tests(returns),
            'outlier_detection': detect_outliers(returns),
            'advanced_statistics': self.calculate_advanced_statistics(close_prices)
        }

        # Add calculus-based analysis
        if len(close_prices) > 10:
            analysis_results['calculus_analysis'] = {
                'momentum_trends': self.calculus.derivative_price_momentum(close_prices),
                'acceleration_patterns': self.calculus.second_derivative_acceleration(close_prices),
                'volume_accumulation': (self.calculus.integral_volume_accumulation(market_data['volume'])
                                      if 'volume' in market_data.columns else None)
            }

        # Add probability analysis
        if len(close_prices) > 30:
            target_price = close_prices.iloc[-1] * 1.05  # 5% upside target
            analysis_results['probability_analysis'] = self.probability.normal_distribution_price_probability(
                close_prices, target_price
            )

        self.logger.info(f"Completed statistical analysis for {symbol}")
        return analysis_results

    def calculate_comparative_statistics(self, data_dict: Dict[str, pd.Series]) -> Dict:
        """Calculate comparative statistics across multiple assets"""
        if len(data_dict) < 2:
            return {"error": "At least 2 assets required for comparison"}

        results = {}
        assets = list(data_dict.keys())

        # Basic comparative stats
        for asset in assets:
            returns = calculate_returns(data_dict[asset])
            results[asset] = {
                'mean_return': returns.mean(),
                'volatility': returns.std(),
                'sharpe_ratio': returns.mean() / returns.std() if returns.std() > 0 else 0,
                'max_drawdown': self._calculate_max_drawdown(data_dict[asset]),
                'skewness': returns.skew(),
                'kurtosis': returns.kurt()
            }

        # Cross-asset correlations
        all_returns = pd.DataFrame({asset: calculate_returns(series)
                                   for asset, series in data_dict.items()})
        results['correlation_matrix'] = all_returns.corr()

        return results

    def _calculate_max_drawdown(self, prices: pd.Series) -> float:
        """Calculate maximum drawdown"""
        cumulative = (1 + calculate_returns(prices)).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()