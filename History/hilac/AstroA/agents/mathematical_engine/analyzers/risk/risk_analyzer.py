"""
Risk Analysis Engine for Portfolio and Trading Risk Management
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from scipy import stats
import logging

from shared.mathematical.utils import (
    safe_divide, calculate_returns, matrix_determinant, matrix_inverse
)
from shared.mathematical.constants import (
    RISK_FREE_RATE, TRADING_DAYS_PER_YEAR, VAR_CONFIDENCE_LEVELS
)
from shared.mathematical.formulas import MatrixFormulas, ProbabilityFormulas

class RiskAnalyzer:
    """Advanced risk analysis for trading and portfolio management"""

    def __init__(self):
        self.logger = logging.getLogger("risk_analyzer")
        self.matrix_formulas = MatrixFormulas()
        self.probability_formulas = ProbabilityFormulas()

    def calculate_value_at_risk(self, returns: pd.Series, confidence_levels: List[float] = None) -> Dict:
        """Calculate Value at Risk (VaR) using multiple methods"""
        if confidence_levels is None:
            confidence_levels = VAR_CONFIDENCE_LEVELS

        clean_returns = returns.dropna()
        if len(clean_returns) < 30:
            return {"error": "Insufficient data for VaR calculation"}

        var_results = {}

        for confidence in confidence_levels:
            alpha = 1 - confidence

            # Historical VaR (non-parametric)
            historical_var = np.percentile(clean_returns, alpha * 100)

            # Parametric VaR (assumes normal distribution)
            mean_return = clean_returns.mean()
            std_return = clean_returns.std()
            z_score = stats.norm.ppf(alpha)
            parametric_var = mean_return + z_score * std_return

            # Modified Cornish-Fisher VaR (accounts for skewness and kurtosis)
            skewness = clean_returns.skew()
            kurtosis = clean_returns.kurt()

            # Cornish-Fisher adjustment
            cf_adjustment = (z_score +
                           (z_score**2 - 1) * skewness / 6 +
                           (z_score**3 - 3*z_score) * kurtosis / 24 -
                           (2*z_score**3 - 5*z_score) * (skewness**2) / 36)

            cornish_fisher_var = mean_return + cf_adjustment * std_return

            var_results[f'{confidence:.0%}'] = {
                'historical_var': historical_var,
                'parametric_var': parametric_var,
                'cornish_fisher_var': cornish_fisher_var,
                'confidence_level': confidence
            }

        return var_results

    def calculate_conditional_var(self, returns: pd.Series, confidence_levels: List[float] = None) -> Dict:
        """Calculate Conditional Value at Risk (Expected Shortfall)"""
        if confidence_levels is None:
            confidence_levels = VAR_CONFIDENCE_LEVELS

        clean_returns = returns.dropna()
        cvar_results = {}

        for confidence in confidence_levels:
            alpha = 1 - confidence
            var_threshold = np.percentile(clean_returns, alpha * 100)

            # CVaR is the average of returns worse than VaR
            tail_returns = clean_returns[clean_returns <= var_threshold]
            cvar = tail_returns.mean() if len(tail_returns) > 0 else var_threshold

            cvar_results[f'{confidence:.0%}'] = {
                'cvar': cvar,
                'var_threshold': var_threshold,
                'tail_observations': len(tail_returns),
                'confidence_level': confidence
            }

        return cvar_results

    def calculate_portfolio_risk_metrics(self, returns_df: pd.DataFrame, weights: np.ndarray = None) -> Dict:
        """Calculate comprehensive portfolio risk metrics"""
        if weights is None:
            weights = np.ones(len(returns_df.columns)) / len(returns_df.columns)

        # Ensure weights sum to 1
        weights = weights / np.sum(weights)

        # Portfolio returns
        portfolio_returns = (returns_df * weights).sum(axis=1)

        # Basic risk metrics
        portfolio_std = portfolio_returns.std()
        annualized_vol = portfolio_std * np.sqrt(TRADING_DAYS_PER_YEAR)

        # Risk-adjusted returns
        portfolio_mean = portfolio_returns.mean()
        sharpe_ratio = (portfolio_mean - RISK_FREE_RATE/252) / portfolio_std if portfolio_std > 0 else 0

        # Maximum drawdown
        cumulative_returns = (1 + portfolio_returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()

        # VaR and CVaR
        var_results = self.calculate_value_at_risk(portfolio_returns)
        cvar_results = self.calculate_conditional_var(portfolio_returns)

        # Beta calculation (if market returns available)
        beta = self._calculate_portfolio_beta(returns_df, weights)

        # Tracking error (vs equal-weighted portfolio)
        equal_weight_returns = returns_df.mean(axis=1)
        tracking_error = (portfolio_returns - equal_weight_returns).std() * np.sqrt(TRADING_DAYS_PER_YEAR)

        # Information ratio
        excess_return = portfolio_returns.mean() - equal_weight_returns.mean()
        information_ratio = (excess_return * TRADING_DAYS_PER_YEAR) / tracking_error if tracking_error > 0 else 0

        return {
            'portfolio_volatility': annualized_vol,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'var_analysis': var_results,
            'cvar_analysis': cvar_results,
            'beta': beta,
            'tracking_error': tracking_error,
            'information_ratio': information_ratio,
            'weights': weights,
            'portfolio_mean_return': portfolio_mean * TRADING_DAYS_PER_YEAR
        }

    def _calculate_portfolio_beta(self, returns_df: pd.DataFrame, weights: np.ndarray) -> Optional[float]:
        """Calculate portfolio beta (simplified using first asset as market proxy)"""
        if len(returns_df.columns) < 2:
            return None

        portfolio_returns = (returns_df * weights).sum(axis=1)
        market_returns = returns_df.iloc[:, 0]  # Use first asset as market proxy

        covariance = np.cov(portfolio_returns, market_returns)[0, 1]
        market_variance = np.var(market_returns)

        return covariance / market_variance if market_variance > 0 else None

    def calculate_marginal_var(self, returns_df: pd.DataFrame, weights: np.ndarray,
                             confidence: float = 0.95) -> Dict:
        """Calculate Marginal VaR for each asset"""
        portfolio_returns = (returns_df * weights).sum(axis=1)
        base_var = np.percentile(portfolio_returns, (1 - confidence) * 100)

        marginal_vars = {}
        delta = 0.01  # 1% weight change

        for i, asset in enumerate(returns_df.columns):
            # Create perturbed weights
            perturbed_weights = weights.copy()
            perturbed_weights[i] += delta

            # Renormalize weights
            perturbed_weights = perturbed_weights / np.sum(perturbed_weights)

            # Calculate new portfolio returns and VaR
            new_portfolio_returns = (returns_df * perturbed_weights).sum(axis=1)
            new_var = np.percentile(new_portfolio_returns, (1 - confidence) * 100)

            # Marginal VaR
            marginal_var = (new_var - base_var) / delta

            marginal_vars[asset] = {
                'marginal_var': marginal_var,
                'contribution_to_var': marginal_var * weights[i],
                'current_weight': weights[i]
            }

        return marginal_vars

    def calculate_component_var(self, returns_df: pd.DataFrame, weights: np.ndarray,
                              confidence: float = 0.95) -> Dict:
        """Calculate Component VaR using matrix approach"""
        portfolio_returns = (returns_df * weights).sum(axis=1)
        portfolio_var = np.percentile(portfolio_returns, (1 - confidence) * 100)

        # Covariance matrix
        cov_matrix = returns_df.cov().values

        # Portfolio variance
        portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
        portfolio_vol = np.sqrt(portfolio_variance)

        # Component VaR calculation
        marginal_contributions = np.dot(cov_matrix, weights) / portfolio_vol
        component_vars = weights * marginal_contributions * portfolio_var / portfolio_vol

        component_results = {}
        for i, asset in enumerate(returns_df.columns):
            component_results[asset] = {
                'component_var': component_vars[i],
                'percent_contribution': component_vars[i] / abs(portfolio_var) * 100,
                'marginal_contribution': marginal_contributions[i],
                'weight': weights[i]
            }

        return {
            'component_vars': component_results,
            'portfolio_var': portfolio_var,
            'total_component_var': np.sum(component_vars)
        }

    def calculate_risk_budgeting(self, returns_df: pd.DataFrame, risk_budgets: np.ndarray) -> Dict:
        """Calculate optimal weights based on risk budgeting"""
        # Simplified risk budgeting using equal risk contribution
        cov_matrix = returns_df.cov().values
        n_assets = len(returns_df.columns)

        # Initial equal weights
        weights = np.ones(n_assets) / n_assets

        # Iterative process to achieve risk budgets
        max_iterations = 100
        tolerance = 1e-6

        for iteration in range(max_iterations):
            # Calculate current risk contributions
            portfolio_var = np.dot(weights.T, np.dot(cov_matrix, weights))
            marginal_contribs = np.dot(cov_matrix, weights)
            risk_contribs = weights * marginal_contribs / portfolio_var

            # Update weights based on risk budget targets
            new_weights = weights * np.sqrt(risk_budgets / risk_contribs)
            new_weights = new_weights / np.sum(new_weights)  # Normalize

            # Check convergence
            if np.max(np.abs(new_weights - weights)) < tolerance:
                break

            weights = new_weights

        # Calculate final risk metrics
        final_portfolio_var = np.dot(weights.T, np.dot(cov_matrix, weights))
        final_risk_contribs = weights * np.dot(cov_matrix, weights) / final_portfolio_var

        return {
            'optimal_weights': weights,
            'target_risk_budgets': risk_budgets,
            'achieved_risk_contributions': final_risk_contribs,
            'portfolio_variance': final_portfolio_var,
            'iterations': iteration + 1
        }

    def calculate_stress_testing(self, returns_df: pd.DataFrame, weights: np.ndarray,
                               stress_scenarios: Dict[str, Dict]) -> Dict:
        """Perform stress testing under various scenarios"""
        stress_results = {}

        for scenario_name, scenario in stress_scenarios.items():
            stressed_returns = returns_df.copy()

            # Apply stress to specific assets
            for asset, stress_params in scenario.items():
                if asset in stressed_returns.columns:
                    if 'shift' in stress_params:
                        stressed_returns[asset] += stress_params['shift']
                    if 'scale' in stress_params:
                        stressed_returns[asset] *= stress_params['scale']

            # Calculate stressed portfolio metrics
            portfolio_returns = (stressed_returns * weights).sum(axis=1)
            stressed_metrics = self.calculate_portfolio_risk_metrics(stressed_returns, weights)

            stress_results[scenario_name] = {
                'stressed_portfolio_vol': stressed_metrics['portfolio_volatility'],
                'stressed_sharpe_ratio': stressed_metrics['sharpe_ratio'],
                'stressed_max_drawdown': stressed_metrics['max_drawdown'],
                'stressed_var_95': stressed_metrics['var_analysis'].get('95%', {}).get('historical_var', 0),
                'portfolio_return_impact': portfolio_returns.mean() - (returns_df * weights).sum(axis=1).mean()
            }

        return stress_results

    def calculate_liquidity_risk(self, volumes: pd.DataFrame, returns_df: pd.DataFrame) -> Dict:
        """Calculate liquidity risk metrics"""
        if volumes.shape != returns_df.shape:
            return {"error": "Volume and returns data must have same dimensions"}

        liquidity_metrics = {}

        for asset in returns_df.columns:
            if asset in volumes.columns:
                asset_returns = returns_df[asset]
                asset_volumes = volumes[asset]

                # Amihud illiquidity measure
                amihud_illiquidity = (np.abs(asset_returns) / asset_volumes).mean()

                # Bid-ask spread proxy (using price volatility and volume)
                bid_ask_proxy = asset_returns.std() / asset_volumes.mean()

                # Volume volatility
                volume_volatility = asset_volumes.std() / asset_volumes.mean()

                # Liquidity-adjusted VaR (simplified)
                base_var = np.percentile(asset_returns, 5)  # 95% VaR
                liquidity_adjustment = 1 + amihud_illiquidity * 1000  # Scaling factor
                liquidity_adjusted_var = base_var * liquidity_adjustment

                liquidity_metrics[asset] = {
                    'amihud_illiquidity': amihud_illiquidity,
                    'bid_ask_spread_proxy': bid_ask_proxy,
                    'volume_volatility': volume_volatility,
                    'liquidity_adjusted_var': liquidity_adjusted_var,
                    'average_volume': asset_volumes.mean()
                }

        return liquidity_metrics

    def calculate_correlation_risk(self, returns_df: pd.DataFrame) -> Dict:
        """Calculate correlation risk and concentration metrics"""
        correlation_matrix = returns_df.corr()

        # Average correlation
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool), k=1)
        avg_correlation = correlation_matrix.values[mask].mean()

        # Maximum correlation
        max_correlation = correlation_matrix.values[mask].max()

        # Correlation concentration (eigenvalue analysis)
        eigenvalues, eigenvectors = np.linalg.eig(correlation_matrix)
        eigenvalues = np.real(eigenvalues)  # Remove small imaginary parts
        eigenvalues_normalized = eigenvalues / np.sum(eigenvalues)

        # Effective number of independent bets
        effective_bets = 1 / np.sum(eigenvalues_normalized ** 2)

        # Absorption ratio (sum of eigenvalues of first k factors)
        k_factors = min(5, len(eigenvalues))
        absorption_ratio = np.sum(sorted(eigenvalues, reverse=True)[:k_factors]) / np.sum(eigenvalues)

        return {
            'average_correlation': avg_correlation,
            'maximum_correlation': max_correlation,
            'effective_number_of_bets': effective_bets,
            'absorption_ratio': absorption_ratio,
            'eigenvalues': eigenvalues.tolist(),
            'correlation_matrix_determinant': np.linalg.det(correlation_matrix),
            'correlation_concentration_score': 1 - effective_bets / len(returns_df.columns)
        }

    def comprehensive_risk_analysis(self, returns_df: pd.DataFrame, weights: np.ndarray = None,
                                   volumes_df: pd.DataFrame = None) -> Dict:
        """Perform comprehensive risk analysis"""
        self.logger.info("Starting comprehensive risk analysis")

        if weights is None:
            weights = np.ones(len(returns_df.columns)) / len(returns_df.columns)

        results = {
            'analysis_info': {
                'assets': returns_df.columns.tolist(),
                'observation_period': {
                    'start': returns_df.index[0],
                    'end': returns_df.index[-1],
                    'total_periods': len(returns_df)
                },
                'portfolio_weights': dict(zip(returns_df.columns, weights))
            }
        }

        # Portfolio risk metrics
        results['portfolio_risk'] = self.calculate_portfolio_risk_metrics(returns_df, weights)

        # Individual asset VaR analysis
        results['individual_var'] = {}
        for asset in returns_df.columns:
            results['individual_var'][asset] = self.calculate_value_at_risk(returns_df[asset])

        # Marginal and component VaR
        results['marginal_var'] = self.calculate_marginal_var(returns_df, weights)
        results['component_var'] = self.calculate_component_var(returns_df, weights)

        # Correlation risk
        results['correlation_risk'] = self.calculate_correlation_risk(returns_df)

        # Liquidity risk (if volume data available)
        if volumes_df is not None:
            results['liquidity_risk'] = self.calculate_liquidity_risk(volumes_df, returns_df)

        # Basic stress testing scenarios
        default_stress_scenarios = {
            'market_crash': {returns_df.columns[0]: {'shift': -0.10}},  # 10% drop in first asset
            'volatility_spike': {asset: {'scale': 2.0} for asset in returns_df.columns},  # Double volatility
            'correlation_breakdown': {}  # Placeholder for correlation stress
        }

        results['stress_testing'] = self.calculate_stress_testing(returns_df, weights, default_stress_scenarios)

        self.logger.info("Completed comprehensive risk analysis")
        return results