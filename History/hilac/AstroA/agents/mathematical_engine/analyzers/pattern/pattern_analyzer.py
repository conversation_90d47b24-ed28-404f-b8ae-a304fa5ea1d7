"""
Pattern Recognition Analysis Engine for Market Data
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from scipy import signal
from scipy.spatial.distance import euclidean
import logging

from shared.mathematical.utils import (
    safe_divide, normalize_series, calculate_returns,
    calculate_derivative, rolling_window
)
from shared.mathematical.constants import (
    PATTERN_TOLERANCE, MIN_PATTERN_LENGTH, MAX_PATTERN_LENGTH
)

class PatternAnalyzer:
    """Advanced pattern recognition for market data analysis"""

    def __init__(self):
        self.logger = logging.getLogger("pattern_analyzer")

    def detect_candlestick_patterns(self, ohlcv_data: pd.DataFrame) -> pd.DataFrame:
        """Detect common candlestick patterns"""
        open_price = ohlcv_data['open_price']
        high_price = ohlcv_data['high_price']
        low_price = ohlcv_data['low_price']
        close_price = ohlcv_data['close_price']

        patterns = pd.DataFrame(index=ohlcv_data.index)

        # Calculate basic candle properties
        body = close_price - open_price
        body_abs = np.abs(body)
        upper_shadow = high_price - np.maximum(open_price, close_price)
        lower_shadow = np.minimum(open_price, close_price) - low_price
        total_range = high_price - low_price

        # Doji pattern
        patterns['doji'] = (body_abs / total_range) < 0.1

        # Hammer pattern
        patterns['hammer'] = (
            (lower_shadow > 2 * body_abs) &
            (upper_shadow < 0.1 * total_range) &
            (body < 0)  # Bearish hammer
        )

        # Shooting star pattern
        patterns['shooting_star'] = (
            (upper_shadow > 2 * body_abs) &
            (lower_shadow < 0.1 * total_range) &
            (body > 0)  # Bullish shooting star
        )

        # Engulfing patterns
        prev_body = body_abs.shift(1)
        patterns['bullish_engulfing'] = (
            (body > 0) &  # Current candle bullish
            (body.shift(1) < 0) &  # Previous candle bearish
            (body_abs > prev_body) &  # Current body larger
            (open_price < close_price.shift(1)) &  # Opens below previous close
            (close_price > open_price.shift(1))  # Closes above previous open
        )

        patterns['bearish_engulfing'] = (
            (body < 0) &  # Current candle bearish
            (body.shift(1) > 0) &  # Previous candle bullish
            (body_abs > prev_body) &  # Current body larger
            (open_price > close_price.shift(1)) &  # Opens above previous close
            (close_price < open_price.shift(1))  # Closes below previous open
        )

        # Morning star pattern (3-candle pattern)
        patterns['morning_star'] = (
            (body.shift(2) < 0) &  # First candle bearish
            (body_abs.shift(1) < 0.3 * body_abs.shift(2)) &  # Middle candle small
            (body > 0) &  # Third candle bullish
            (close_price > (open_price.shift(2) + close_price.shift(2)) / 2)  # Closes above midpoint
        )

        # Evening star pattern
        patterns['evening_star'] = (
            (body.shift(2) > 0) &  # First candle bullish
            (body_abs.shift(1) < 0.3 * body_abs.shift(2)) &  # Middle candle small
            (body < 0) &  # Third candle bearish
            (close_price < (open_price.shift(2) + close_price.shift(2)) / 2)  # Closes below midpoint
        )

        return patterns

    def detect_chart_patterns(self, prices: pd.Series, window: int = 50) -> Dict:
        """Detect chart patterns like head and shoulders, triangles, etc."""
        patterns_detected = {}

        if len(prices) < window:
            return {"error": "Insufficient data for pattern detection"}

        # Find local peaks and troughs
        peaks, _ = signal.find_peaks(prices.values, distance=10)
        troughs, _ = signal.find_peaks(-prices.values, distance=10)

        patterns_detected['peaks'] = peaks.tolist()
        patterns_detected['troughs'] = troughs.tolist()

        # Head and shoulders detection
        head_shoulders = self._detect_head_and_shoulders(prices, peaks, troughs)
        patterns_detected['head_and_shoulders'] = head_shoulders

        # Triangle patterns
        triangles = self._detect_triangles(prices, peaks, troughs)
        patterns_detected['triangles'] = triangles

        # Support and resistance levels
        support_resistance = self._detect_support_resistance(prices, peaks, troughs)
        patterns_detected['support_resistance'] = support_resistance

        # Double top/bottom patterns
        double_patterns = self._detect_double_patterns(prices, peaks, troughs)
        patterns_detected['double_patterns'] = double_patterns

        return patterns_detected

    def _detect_head_and_shoulders(self, prices: pd.Series, peaks: np.ndarray, troughs: np.ndarray) -> List[Dict]:
        """Detect head and shoulders patterns"""
        patterns = []

        if len(peaks) < 3:
            return patterns

        for i in range(len(peaks) - 2):
            left_peak = peaks[i]
            center_peak = peaks[i + 1]
            right_peak = peaks[i + 2]

            left_price = prices.iloc[left_peak]
            center_price = prices.iloc[center_peak]
            right_price = prices.iloc[right_peak]

            # Check if center peak is higher (head) and shoulders are roughly equal
            if (center_price > left_price and center_price > right_price and
                abs(left_price - right_price) / left_price < PATTERN_TOLERANCE):

                # Find neckline (connecting troughs between peaks)
                relevant_troughs = troughs[(troughs > left_peak) & (troughs < right_peak)]

                if len(relevant_troughs) >= 1:
                    neckline_level = np.mean([prices.iloc[t] for t in relevant_troughs])

                    patterns.append({
                        'type': 'head_and_shoulders',
                        'left_shoulder': {'index': left_peak, 'price': left_price},
                        'head': {'index': center_peak, 'price': center_price},
                        'right_shoulder': {'index': right_peak, 'price': right_price},
                        'neckline': neckline_level,
                        'strength': center_price / neckline_level,
                        'start_index': left_peak,
                        'end_index': right_peak
                    })

        return patterns

    def _detect_triangles(self, prices: pd.Series, peaks: np.ndarray, troughs: np.ndarray) -> List[Dict]:
        """Detect triangle patterns (ascending, descending, symmetrical)"""
        patterns = []

        if len(peaks) < 2 or len(troughs) < 2:
            return patterns

        # Check for ascending triangles (horizontal resistance, rising support)
        for i in range(len(peaks) - 1):
            peak1, peak2 = peaks[i], peaks[i + 1]
            price1, price2 = prices.iloc[peak1], prices.iloc[peak2]

            # Horizontal resistance line
            if abs(price1 - price2) / price1 < PATTERN_TOLERANCE:
                # Find troughs between these peaks
                relevant_troughs = troughs[(troughs > peak1) & (troughs < peak2)]

                if len(relevant_troughs) >= 1:
                    trough_prices = [prices.iloc[t] for t in relevant_troughs]
                    # Check if support is rising
                    if len(trough_prices) > 1 and trough_prices[-1] > trough_prices[0]:
                        patterns.append({
                            'type': 'ascending_triangle',
                            'resistance_level': (price1 + price2) / 2,
                            'support_slope': (trough_prices[-1] - trough_prices[0]) / (relevant_troughs[-1] - relevant_troughs[0]),
                            'start_index': peak1,
                            'end_index': peak2
                        })

        # Check for descending triangles (falling resistance, horizontal support)
        for i in range(len(troughs) - 1):
            trough1, trough2 = troughs[i], troughs[i + 1]
            price1, price2 = prices.iloc[trough1], prices.iloc[trough2]

            # Horizontal support line
            if abs(price1 - price2) / price1 < PATTERN_TOLERANCE:
                # Find peaks between these troughs
                relevant_peaks = peaks[(peaks > trough1) & (peaks < trough2)]

                if len(relevant_peaks) >= 1:
                    peak_prices = [prices.iloc[p] for p in relevant_peaks]
                    # Check if resistance is falling
                    if len(peak_prices) > 1 and peak_prices[-1] < peak_prices[0]:
                        patterns.append({
                            'type': 'descending_triangle',
                            'support_level': (price1 + price2) / 2,
                            'resistance_slope': (peak_prices[-1] - peak_prices[0]) / (relevant_peaks[-1] - relevant_peaks[0]),
                            'start_index': trough1,
                            'end_index': trough2
                        })

        return patterns

    def _detect_support_resistance(self, prices: pd.Series, peaks: np.ndarray, troughs: np.ndarray) -> Dict:
        """Detect support and resistance levels"""
        # Cluster peaks and troughs by price level
        tolerance = prices.std() * 0.5

        # Resistance levels (from peaks)
        resistance_levels = []
        peak_prices = [prices.iloc[p] for p in peaks]

        for price in peak_prices:
            similar_peaks = [p for p in peak_prices if abs(p - price) <= tolerance]
            if len(similar_peaks) >= 2:  # At least 2 touches
                resistance_levels.append({
                    'level': np.mean(similar_peaks),
                    'touches': len(similar_peaks),
                    'strength': len(similar_peaks) / len(peak_prices)
                })

        # Support levels (from troughs)
        support_levels = []
        trough_prices = [prices.iloc[t] for t in troughs]

        for price in trough_prices:
            similar_troughs = [t for t in trough_prices if abs(t - price) <= tolerance]
            if len(similar_troughs) >= 2:  # At least 2 touches
                support_levels.append({
                    'level': np.mean(similar_troughs),
                    'touches': len(similar_troughs),
                    'strength': len(similar_troughs) / len(trough_prices)
                })

        return {
            'resistance_levels': resistance_levels,
            'support_levels': support_levels,
            'current_price': prices.iloc[-1],
            'distance_to_resistance': min([abs(prices.iloc[-1] - r['level']) for r in resistance_levels], default=float('inf')),
            'distance_to_support': min([abs(prices.iloc[-1] - s['level']) for s in support_levels], default=float('inf'))
        }

    def _detect_double_patterns(self, prices: pd.Series, peaks: np.ndarray, troughs: np.ndarray) -> List[Dict]:
        """Detect double top and double bottom patterns"""
        patterns = []

        # Double tops
        for i in range(len(peaks) - 1):
            peak1, peak2 = peaks[i], peaks[i + 1]
            price1, price2 = prices.iloc[peak1], prices.iloc[peak2]

            # Check if peaks are roughly equal
            if abs(price1 - price2) / price1 < PATTERN_TOLERANCE:
                # Find the trough between them
                between_troughs = troughs[(troughs > peak1) & (troughs < peak2)]

                if len(between_troughs) > 0:
                    valley_price = min([prices.iloc[t] for t in between_troughs])
                    valley_index = between_troughs[np.argmin([prices.iloc[t] for t in between_troughs])]

                    patterns.append({
                        'type': 'double_top',
                        'first_peak': {'index': peak1, 'price': price1},
                        'second_peak': {'index': peak2, 'price': price2},
                        'valley': {'index': valley_index, 'price': valley_price},
                        'neckline': valley_price,
                        'height': (price1 + price2) / 2 - valley_price,
                        'target': valley_price - ((price1 + price2) / 2 - valley_price)
                    })

        # Double bottoms
        for i in range(len(troughs) - 1):
            trough1, trough2 = troughs[i], troughs[i + 1]
            price1, price2 = prices.iloc[trough1], prices.iloc[trough2]

            # Check if troughs are roughly equal
            if abs(price1 - price2) / price1 < PATTERN_TOLERANCE:
                # Find the peak between them
                between_peaks = peaks[(peaks > trough1) & (peaks < trough2)]

                if len(between_peaks) > 0:
                    peak_price = max([prices.iloc[p] for p in between_peaks])
                    peak_index = between_peaks[np.argmax([prices.iloc[p] for p in between_peaks])]

                    patterns.append({
                        'type': 'double_bottom',
                        'first_trough': {'index': trough1, 'price': price1},
                        'second_trough': {'index': trough2, 'price': price2},
                        'peak': {'index': peak_index, 'price': peak_price},
                        'neckline': peak_price,
                        'height': peak_price - (price1 + price2) / 2,
                        'target': peak_price + (peak_price - (price1 + price2) / 2)
                    })

        return patterns

    def detect_trend_patterns(self, prices: pd.Series, window: int = 20) -> Dict:
        """Detect trend patterns and channels"""
        # Calculate trend lines using linear regression
        trend_analysis = {}

        # Overall trend
        x = np.arange(len(prices))
        slope, intercept = np.polyfit(x, prices.values, 1)

        trend_analysis['overall_trend'] = {
            'slope': slope,
            'intercept': intercept,
            'direction': 'uptrend' if slope > 0 else 'downtrend',
            'strength': abs(slope) / prices.mean(),
            'r_squared': np.corrcoef(x, prices.values)[0, 1] ** 2
        }

        # Rolling trend analysis
        rolling_trends = []
        for i in range(window, len(prices)):
            segment = prices.iloc[i-window:i]
            x_segment = np.arange(len(segment))
            slope_segment, _ = np.polyfit(x_segment, segment.values, 1)
            rolling_trends.append(slope_segment)

        trend_analysis['rolling_trends'] = pd.Series(rolling_trends, index=prices.index[window:])

        # Trend changes
        trend_changes = pd.Series(rolling_trends).diff()
        significant_changes = np.abs(trend_changes) > trend_changes.std() * 2

        trend_analysis['trend_changes'] = {
            'change_points': trend_changes.index[significant_changes].tolist(),
            'change_magnitudes': trend_changes[significant_changes].tolist()
        }

        return trend_analysis

    def detect_volume_patterns(self, ohlcv_data: pd.DataFrame) -> Dict:
        """Detect volume-based patterns"""
        if 'volume' not in ohlcv_data.columns:
            return {"error": "Volume data not available"}

        volume = ohlcv_data['volume']
        close_price = ohlcv_data['close_price']
        price_change = close_price.pct_change()

        volume_patterns = {}

        # Volume moving averages
        volume_sma_20 = volume.rolling(20).mean()
        volume_sma_50 = volume.rolling(50).mean()

        # High volume breakouts
        high_volume = volume > volume_sma_20 * 2
        significant_price_move = np.abs(price_change) > price_change.std() * 2

        volume_patterns['volume_breakouts'] = pd.DataFrame({
            'high_volume': high_volume,
            'significant_move': significant_price_move,
            'volume_breakout': high_volume & significant_price_move
        }, index=ohlcv_data.index)

        # Volume trend analysis
        volume_trend_slope = self._calculate_rolling_slope(volume, window=20)
        volume_patterns['volume_trend'] = volume_trend_slope

        # On-Balance Volume (OBV)
        obv = np.where(price_change > 0, volume,
               np.where(price_change < 0, -volume, 0)).cumsum()
        volume_patterns['obv'] = pd.Series(obv, index=ohlcv_data.index)

        # Volume Rate of Change
        volume_roc = volume.pct_change(periods=10)
        volume_patterns['volume_roc'] = volume_roc

        return volume_patterns

    def _calculate_rolling_slope(self, series: pd.Series, window: int) -> pd.Series:
        """Calculate rolling slope using linear regression"""
        slopes = []
        for i in range(window, len(series)):
            segment = series.iloc[i-window:i]
            x = np.arange(len(segment))
            slope, _ = np.polyfit(x, segment.values, 1)
            slopes.append(slope)

        return pd.Series(slopes, index=series.index[window:])

    def detect_momentum_patterns(self, prices: pd.Series) -> Dict:
        """Detect momentum patterns using derivatives and oscillators"""
        momentum_patterns = {}

        # Price momentum using calculus derivatives
        first_derivative = calculate_derivative(prices, method='gradient')
        second_derivative = calculate_derivative(first_derivative, method='gradient')

        momentum_patterns['velocity'] = first_derivative
        momentum_patterns['acceleration'] = second_derivative

        # Momentum oscillator
        momentum_14 = prices.diff(periods=14)
        momentum_patterns['momentum_14'] = momentum_14

        # Rate of Change
        roc_14 = prices.pct_change(periods=14) * 100
        momentum_patterns['roc_14'] = roc_14

        # Momentum divergence detection
        price_peaks, _ = signal.find_peaks(prices.values, distance=10)
        momentum_peaks, _ = signal.find_peaks(momentum_14.values, distance=10)

        # Check for divergences between price and momentum peaks
        divergences = []
        if len(price_peaks) > 1 and len(momentum_peaks) > 1:
            recent_price_peaks = price_peaks[-2:]
            recent_momentum_peaks = momentum_peaks[-2:]

            if len(recent_price_peaks) == 2 and len(recent_momentum_peaks) == 2:
                price_trend = prices.iloc[recent_price_peaks[1]] - prices.iloc[recent_price_peaks[0]]
                momentum_trend = momentum_14.iloc[recent_momentum_peaks[1]] - momentum_14.iloc[recent_momentum_peaks[0]]

                if price_trend > 0 and momentum_trend < 0:
                    divergences.append('bearish_divergence')
                elif price_trend < 0 and momentum_trend > 0:
                    divergences.append('bullish_divergence')

        momentum_patterns['divergences'] = divergences

        return momentum_patterns

    def comprehensive_pattern_analysis(self, ohlcv_data: pd.DataFrame) -> Dict:
        """Perform comprehensive pattern analysis"""
        self.logger.info("Starting comprehensive pattern analysis")

        if len(ohlcv_data) < MIN_PATTERN_LENGTH:
            return {"error": f"Insufficient data: need at least {MIN_PATTERN_LENGTH} data points"}

        close_prices = ohlcv_data['close_price']

        results = {
            'analysis_info': {
                'total_periods': len(ohlcv_data),
                'start_date': ohlcv_data.index[0],
                'end_date': ohlcv_data.index[-1],
                'current_price': close_prices.iloc[-1]
            }
        }

        # Candlestick patterns
        results['candlestick_patterns'] = self.detect_candlestick_patterns(ohlcv_data)

        # Chart patterns
        results['chart_patterns'] = self.detect_chart_patterns(close_prices)

        # Trend patterns
        results['trend_patterns'] = self.detect_trend_patterns(close_prices)

        # Volume patterns
        results['volume_patterns'] = self.detect_volume_patterns(ohlcv_data)

        # Momentum patterns
        results['momentum_patterns'] = self.detect_momentum_patterns(close_prices)

        self.logger.info("Completed comprehensive pattern analysis")
        return results