"""
Correlation Analysis Engine for Multi-Asset Analysis
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from scipy import stats
from scipy.stats import pearsonr, spearmanr
import logging

from shared.mathematical.utils import safe_divide, calculate_returns
from shared.mathematical.constants import CORRELATION_THRESHOLDS
from shared.mathematical.formulas import MatrixFormulas

class CorrelationAnalyzer:
    """Advanced correlation analysis for multi-asset portfolios"""

    def __init__(self):
        self.logger = logging.getLogger("correlation_analyzer")
        self.matrix_formulas = MatrixFormulas()

    def calculate_pearson_correlation(self, data1: pd.Series, data2: pd.Series,
                                    window: Optional[int] = None) -> Union[pd.Series, float]:
        """Calculate Pearson correlation coefficient"""
        if window is None:
            # Static correlation
            correlation, p_value = pearsonr(data1.dropna(), data2.dropna())
            return {
                'correlation': correlation,
                'p_value': p_value,
                'significance': p_value < 0.05
            }
        else:
            # Rolling correlation
            return data1.rolling(window).corr(data2)

    def calculate_spearman_correlation(self, data1: pd.Series, data2: pd.Series,
                                     window: Optional[int] = None) -> Union[pd.Series, float]:
        """Calculate Spearman rank correlation"""
        if window is None:
            correlation, p_value = spearmanr(data1.dropna(), data2.dropna())
            return {
                'correlation': correlation,
                'p_value': p_value,
                'significance': p_value < 0.05
            }
        else:
            def spearman_rolling(x, y):
                if len(x) < 5 or len(y) < 5:
                    return np.nan
                try:
                    corr, _ = spearmanr(x, y)
                    return corr
                except:
                    return np.nan

            return data1.rolling(window).apply(
                lambda x: spearman_rolling(x, data2.iloc[x.index])
            )

    def calculate_rolling_correlation_matrix(self, returns_df: pd.DataFrame,
                                           window: int = 60) -> Dict:
        """Calculate rolling correlation matrices"""
        n_assets = len(returns_df.columns)
        assets = returns_df.columns.tolist()

        # Initialize storage for rolling correlations
        rolling_corrs = {}

        for i, asset1 in enumerate(assets):
            for j, asset2 in enumerate(assets):
                if i <= j:  # Only calculate upper triangle
                    key = f"{asset1}_{asset2}"
                    rolling_corrs[key] = returns_df[asset1].rolling(window).corr(returns_df[asset2])

        # Calculate correlation statistics
        corr_stats = {}
        for key, corr_series in rolling_corrs.items():
            corr_stats[key] = {
                'mean_correlation': corr_series.mean(),
                'std_correlation': corr_series.std(),
                'max_correlation': corr_series.max(),
                'min_correlation': corr_series.min(),
                'current_correlation': corr_series.iloc[-1] if len(corr_series) > 0 else np.nan
            }

        return {
            'rolling_correlations': rolling_corrs,
            'correlation_statistics': corr_stats,
            'window': window,
            'assets': assets
        }

    def detect_correlation_breakdowns(self, returns_df: pd.DataFrame,
                                    window: int = 60, threshold: float = 0.3) -> Dict:
        """Detect periods of correlation breakdown/crisis"""
        rolling_corr_data = self.calculate_rolling_correlation_matrix(returns_df, window)

        breakdown_periods = {}

        for pair_key, corr_series in rolling_corr_data['rolling_correlations'].items():
            if '_' not in pair_key or pair_key.split('_')[0] == pair_key.split('_')[1]:
                continue  # Skip self-correlations

            # Detect sudden increases in correlation (crisis periods)
            corr_changes = corr_series.diff()
            mean_corr = corr_series.mean()
            std_corr = corr_series.std()

            # Breakdown signals
            high_corr_periods = corr_series > (mean_corr + 2 * std_corr)
            sudden_increases = corr_changes > (threshold)

            breakdown_periods[pair_key] = {
                'high_correlation_periods': high_corr_periods.sum(),
                'sudden_increase_events': sudden_increases.sum(),
                'avg_correlation': mean_corr,
                'correlation_volatility': std_corr,
                'crisis_probability': (high_corr_periods | sudden_increases).mean()
            }

        return breakdown_periods

    def calculate_cross_correlation(self, data1: pd.Series, data2: pd.Series,
                                   max_lags: int = 20) -> Dict:
        """Calculate cross-correlation at different lags"""
        cross_corrs = {}

        for lag in range(-max_lags, max_lags + 1):
            if lag < 0:
                # data1 leads data2
                shifted_data2 = data2.shift(-lag)
                valid_data = pd.concat([data1, shifted_data2], axis=1).dropna()
                if len(valid_data) > 10:
                    corr = valid_data.iloc[:, 0].corr(valid_data.iloc[:, 1])
                    cross_corrs[lag] = corr
            elif lag > 0:
                # data2 leads data1
                shifted_data1 = data1.shift(lag)
                valid_data = pd.concat([shifted_data1, data2], axis=1).dropna()
                if len(valid_data) > 10:
                    corr = valid_data.iloc[:, 0].corr(valid_data.iloc[:, 1])
                    cross_corrs[lag] = corr
            else:
                # No lag
                valid_data = pd.concat([data1, data2], axis=1).dropna()
                if len(valid_data) > 10:
                    corr = valid_data.iloc[:, 0].corr(valid_data.iloc[:, 1])
                    cross_corrs[lag] = corr

        # Find optimal lag
        best_lag = max(cross_corrs.items(), key=lambda x: abs(x[1]))[0] if cross_corrs else 0

        return {
            'cross_correlations': cross_corrs,
            'best_lag': best_lag,
            'best_correlation': cross_corrs.get(best_lag, 0),
            'lead_lag_relationship': "data1_leads" if best_lag < 0 else ("data2_leads" if best_lag > 0 else "synchronous")
        }

    def analyze_correlation_clusters(self, returns_df: pd.DataFrame) -> Dict:
        """Identify correlation clusters using hierarchical clustering"""
        try:
            from scipy.cluster.hierarchy import linkage, dendrogram, fcluster
            from scipy.spatial.distance import squareform

            # Calculate correlation matrix
            corr_matrix = returns_df.corr()

            # Convert correlation to distance
            distance_matrix = 1 - np.abs(corr_matrix)

            # Perform hierarchical clustering
            condensed_distances = squareform(distance_matrix)
            linkage_matrix = linkage(condensed_distances, method='ward')

            # Form clusters
            n_clusters = min(5, len(returns_df.columns) // 2 + 1)
            clusters = fcluster(linkage_matrix, n_clusters, criterion='maxclust')

            # Organize results
            cluster_dict = {}
            for i, asset in enumerate(returns_df.columns):
                cluster_id = clusters[i]
                if cluster_id not in cluster_dict:
                    cluster_dict[cluster_id] = []
                cluster_dict[cluster_id].append(asset)

            # Calculate within-cluster and between-cluster correlations
            cluster_stats = {}
            for cluster_id, assets in cluster_dict.items():
                if len(assets) > 1:
                    cluster_corr_matrix = corr_matrix.loc[assets, assets]
                    # Average within-cluster correlation (excluding diagonal)
                    mask = np.triu(np.ones_like(cluster_corr_matrix, dtype=bool), k=1)
                    within_cluster_corr = cluster_corr_matrix.values[mask].mean()
                    cluster_stats[cluster_id] = {
                        'assets': assets,
                        'within_cluster_correlation': within_cluster_corr,
                        'cluster_size': len(assets)
                    }

            return {
                'clusters': cluster_dict,
                'cluster_statistics': cluster_stats,
                'linkage_matrix': linkage_matrix,
                'distance_matrix': distance_matrix,
                'correlation_matrix': corr_matrix
            }

        except ImportError:
            self.logger.warning("scipy.cluster not available for clustering analysis")
            return {"error": "Clustering analysis requires scipy.cluster"}

    def calculate_dynamic_conditional_correlation(self, returns_df: pd.DataFrame,
                                                window: int = 60) -> Dict:
        """Calculate Dynamic Conditional Correlation (DCC)"""
        # Simplified DCC implementation
        n_assets = len(returns_df.columns)
        assets = returns_df.columns.tolist()

        # Step 1: Estimate GARCH models for each asset (simplified)
        standardized_returns = {}
        for asset in assets:
            returns = returns_df[asset].dropna()
            rolling_vol = returns.rolling(window).std()
            standardized_returns[asset] = returns / rolling_vol

        # Step 2: Calculate time-varying correlations
        standardized_df = pd.DataFrame(standardized_returns)

        dcc_results = {}
        for i, asset1 in enumerate(assets):
            for j, asset2 in enumerate(assets):
                if i < j:  # Upper triangle only
                    # Rolling correlation of standardized returns
                    rolling_corr = standardized_df[asset1].rolling(window).corr(standardized_df[asset2])

                    dcc_results[f"{asset1}_{asset2}"] = {
                        'time_varying_correlation': rolling_corr,
                        'average_correlation': rolling_corr.mean(),
                        'correlation_volatility': rolling_corr.std(),
                        'persistence': rolling_corr.autocorr(lag=1)
                    }

        return dcc_results

    def test_correlation_stability(self, returns_df: pd.DataFrame,
                                 split_ratio: float = 0.5) -> Dict:
        """Test correlation stability across time periods"""
        split_point = int(len(returns_df) * split_ratio)

        period1_data = returns_df.iloc[:split_point]
        period2_data = returns_df.iloc[split_point:]

        period1_corr = period1_data.corr()
        period2_corr = period2_data.corr()

        # Calculate correlation differences
        corr_diff = period2_corr - period1_corr

        # Statistical test for correlation stability
        stability_results = {}
        assets = returns_df.columns.tolist()

        for i, asset1 in enumerate(assets):
            for j, asset2 in enumerate(assets):
                if i < j:
                    corr1 = period1_corr.loc[asset1, asset2]
                    corr2 = period2_corr.loc[asset1, asset2]

                    # Fisher's z-transformation for correlation comparison
                    z1 = 0.5 * np.log((1 + corr1) / (1 - corr1))
                    z2 = 0.5 * np.log((1 + corr2) / (1 - corr2))

                    # Standard error
                    se = np.sqrt(1/(len(period1_data)-3) + 1/(len(period2_data)-3))

                    # Test statistic
                    z_stat = (z1 - z2) / se
                    p_value = 2 * (1 - stats.norm.cdf(abs(z_stat)))

                    stability_results[f"{asset1}_{asset2}"] = {
                        'period1_correlation': corr1,
                        'period2_correlation': corr2,
                        'correlation_change': corr2 - corr1,
                        'z_statistic': z_stat,
                        'p_value': p_value,
                        'is_stable': p_value > 0.05
                    }

        return {
            'stability_tests': stability_results,
            'period1_correlation_matrix': period1_corr,
            'period2_correlation_matrix': period2_corr,
            'correlation_difference_matrix': corr_diff
        }

    def comprehensive_correlation_analysis(self, returns_df: pd.DataFrame) -> Dict:
        """Perform comprehensive correlation analysis"""
        self.logger.info("Starting comprehensive correlation analysis")

        if len(returns_df.columns) < 2:
            return {"error": "At least 2 assets required for correlation analysis"}

        results = {
            'assets': returns_df.columns.tolist(),
            'analysis_period': {
                'start_date': returns_df.index[0],
                'end_date': returns_df.index[-1],
                'total_observations': len(returns_df)
            }
        }

        # Basic correlation matrix
        results['static_correlation'] = self.matrix_formulas.correlation_matrix_analysis(returns_df)

        # Rolling correlations
        if len(returns_df) > 60:
            results['rolling_correlations'] = self.calculate_rolling_correlation_matrix(returns_df)

        # Correlation breakdown detection
        if len(returns_df) > 100:
            results['breakdown_analysis'] = self.detect_correlation_breakdowns(returns_df)

        # Clustering analysis
        results['cluster_analysis'] = self.analyze_correlation_clusters(returns_df)

        # Stability testing
        if len(returns_df) > 200:
            results['stability_analysis'] = self.test_correlation_stability(returns_df)

        # Cross-correlation analysis for first two assets
        if len(returns_df.columns) >= 2:
            asset1, asset2 = returns_df.columns[:2]
            results['cross_correlation'] = self.calculate_cross_correlation(
                returns_df[asset1], returns_df[asset2]
            )

        self.logger.info("Completed comprehensive correlation analysis")
        return results

    def interpret_correlation_strength(self, correlation: float) -> str:
        """Interpret correlation strength based on thresholds"""
        abs_corr = abs(correlation)

        if abs_corr >= CORRELATION_THRESHOLDS['strong_positive']:
            return "Strong"
        elif abs_corr >= CORRELATION_THRESHOLDS['moderate_positive']:
            return "Moderate"
        elif abs_corr >= CORRELATION_THRESHOLDS['weak']:
            return "Weak"
        else:
            return "Very Weak"

    def generate_correlation_summary(self, correlation_results: Dict) -> Dict:
        """Generate human-readable correlation summary"""
        if 'static_correlation' not in correlation_results:
            return {"error": "No correlation results to summarize"}

        corr_matrix = correlation_results['static_correlation']['correlation_matrix']

        # Extract upper triangle correlations
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool), k=1)
        correlations = corr_matrix.values[mask]

        summary = {
            'average_correlation': np.mean(correlations),
            'max_correlation': np.max(correlations),
            'min_correlation': np.min(correlations),
            'correlation_distribution': {
                'strong_correlations': np.sum(np.abs(correlations) >= CORRELATION_THRESHOLDS['strong_positive']),
                'moderate_correlations': np.sum((np.abs(correlations) >= CORRELATION_THRESHOLDS['moderate_positive']) &
                                              (np.abs(correlations) < CORRELATION_THRESHOLDS['strong_positive'])),
                'weak_correlations': np.sum(np.abs(correlations) < CORRELATION_THRESHOLDS['moderate_positive'])
            },
            'diversification_score': 1 - np.mean(np.abs(correlations))  # Higher score = better diversification
        }

        return summary