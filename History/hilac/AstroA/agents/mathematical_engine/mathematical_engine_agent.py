"""
Mathematical Engine Agent - Main coordinator for mathematical analysis
"""
import asyncio
import psycopg2
import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Any, Optional
import os
from dotenv import load_dotenv
import logging

from shared.utils.base_agent import BaseAgent
from shared.types.agent_types import (
    AgentType, MessageType, AnalysisResult
)
from shared.mathematical.formulas import IntegratedMathematicalAnalysis
from agents.mathematical_engine.analyzers.statistical.statistical_analyzer import StatisticalAnalyzer
from agents.mathematical_engine.analyzers.correlation.correlation_analyzer import CorrelationAnalyzer
from agents.mathematical_engine.analyzers.pattern.pattern_analyzer import PatternAnalyzer
from agents.mathematical_engine.analyzers.risk.risk_analyzer import RiskAnalyzer

load_dotenv()

class MathematicalEngineAgent(BaseAgent):
    """Agent responsible for advanced mathematical analysis of market data"""

    def __init__(self, agent_id: str = "mathematical_engine_001"):
        super().__init__(agent_id, AgentType.MATHEMATICAL_ENGINE)

        # Initialize analyzers
        self.statistical_analyzer = StatisticalAnalyzer()
        self.correlation_analyzer = CorrelationAnalyzer()
        self.pattern_analyzer = PatternAnalyzer()
        self.risk_analyzer = RiskAnalyzer()
        self.integrated_analysis = IntegratedMathematicalAnalysis()

        # Database connection
        self.db_connection = None

        # Analysis intervals (in seconds)
        self.analysis_interval = 1800  # 30 minutes
        self.last_analysis_time = None

        # Register message handlers
        self.register_handler(MessageType.DATA_RESPONSE, self._handle_data_response)
        self.register_handler(MessageType.ANALYSIS_REQUEST, self._handle_analysis_request)

    async def initialize(self):
        """Initialize database connection and mathematical engine"""
        self.logger.info("Initializing Mathematical Engine Agent...")

        # Setup database connection
        try:
            self.db_connection = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'mathematical_trading'),
                user=os.getenv('DB_USER', 'trading_user'),
                password=os.getenv('DB_PASSWORD', 'secure_password_123')
            )
            self.logger.info("Database connection established")
        except Exception as e:
            self.logger.error(f"Failed to connect to database: {e}")
            raise

        self.logger.info("Mathematical Engine Agent initialized successfully")

    async def execute_main_logic(self):
        """Main execution logic for mathematical analysis"""
        current_time = datetime.now()

        # Check if it's time to perform analysis
        if (self.last_analysis_time is None or
            (current_time - self.last_analysis_time).seconds >= self.analysis_interval):

            await self.perform_comprehensive_analysis()
            self.last_analysis_time = current_time

    async def _handle_data_response(self, message):
        """Handle data response messages from data collection agent"""
        payload = message.payload
        data_type = payload.get('data_type')

        if data_type == 'market_data':
            self.logger.info(f"Received market data notification: {payload.get('count')} new data points")
            # Trigger analysis for new data
            await self.perform_comprehensive_analysis()

    async def _handle_analysis_request(self, message):
        """Handle analysis request messages"""
        payload = message.payload
        request_type = payload.get('request_type', 'comprehensive')
        symbols = payload.get('symbols', [])

        if request_type == 'comprehensive':
            results = await self.perform_comprehensive_analysis(symbols)
        elif request_type == 'statistical':
            results = await self.perform_statistical_analysis(symbols)
        elif request_type == 'correlation':
            results = await self.perform_correlation_analysis(symbols)
        elif request_type == 'pattern':
            results = await self.perform_pattern_analysis(symbols)
        elif request_type == 'risk':
            results = await self.perform_risk_analysis(symbols)
        else:
            results = {"error": f"Unknown request type: {request_type}"}

        # Send results back to requester
        await self.send_message(
            receiver_id=message.sender_id,
            message_type=MessageType.ANALYSIS_RESPONSE,
            payload={
                "analysis_type": request_type,
                "results": results,
                "timestamp": datetime.now().isoformat(),
                "agent_id": self.agent_id
            }
        )

    async def fetch_market_data(self, symbols: List[str] = None, limit: int = 1000) -> Dict[str, pd.DataFrame]:
        """Fetch market data from database"""
        market_data = {}

        cursor = self.db_connection.cursor()

        try:
            if symbols:
                symbol_filter = f"WHERE symbol IN ({','.join(['%s'] * len(symbols))})"
                query_params = symbols
            else:
                symbol_filter = ""
                query_params = []

            query = f"""
            SELECT symbol, timestamp, open_price, high_price, low_price, close_price, volume, exchange, timeframe
            FROM market_data
            {symbol_filter}
            ORDER BY symbol, timestamp DESC
            LIMIT %s
            """

            cursor.execute(query, query_params + [limit])
            results = cursor.fetchall()

            # Organize data by symbol
            for row in results:
                symbol, timestamp, open_p, high_p, low_p, close_p, volume, exchange, timeframe = row

                if symbol not in market_data:
                    market_data[symbol] = []

                market_data[symbol].append({
                    'timestamp': timestamp,
                    'open_price': float(open_p),
                    'high_price': float(high_p),
                    'low_price': float(low_p),
                    'close_price': float(close_p),
                    'volume': float(volume),
                    'exchange': exchange,
                    'timeframe': timeframe
                })

            # Convert to DataFrames
            for symbol in market_data:
                df = pd.DataFrame(market_data[symbol])
                df.set_index('timestamp', inplace=True)
                df.sort_index(inplace=True)
                market_data[symbol] = df

        except Exception as e:
            self.logger.error(f"Error fetching market data: {e}")
            market_data = {}
        finally:
            cursor.close()

        return market_data

    async def perform_comprehensive_analysis(self, symbols: List[str] = None) -> Dict:
        """Perform comprehensive mathematical analysis"""
        self.logger.info("Starting comprehensive mathematical analysis")

        try:
            # Fetch market data
            market_data = await self.fetch_market_data(symbols)

            if not market_data:
                return {"error": "No market data available for analysis"}

            analysis_results = {
                'analysis_timestamp': datetime.now().isoformat(),
                'symbols_analyzed': list(market_data.keys()),
                'total_symbols': len(market_data)
            }

            # Individual symbol analysis
            individual_analysis = {}
            for symbol, data in market_data.items():
                if len(data) > 50:  # Minimum data requirement
                    self.logger.info(f"Analyzing {symbol}")

                    # Statistical analysis
                    statistical_results = self.statistical_analyzer.perform_comprehensive_analysis(data, symbol)

                    # Pattern analysis
                    pattern_results = self.pattern_analyzer.comprehensive_pattern_analysis(data)

                    # Integrated mathematical analysis
                    integrated_results = self.integrated_analysis.comprehensive_market_analysis(data)

                    individual_analysis[symbol] = {
                        'statistical_analysis': statistical_results,
                        'pattern_analysis': pattern_results,
                        'integrated_analysis': integrated_results,
                        'data_points': len(data),
                        'analysis_quality': self._assess_analysis_quality(data)
                    }

            analysis_results['individual_analysis'] = individual_analysis

            # Multi-asset analysis (if multiple symbols)
            if len(market_data) > 1:
                multi_asset_results = await self.perform_multi_asset_analysis(market_data)
                analysis_results['multi_asset_analysis'] = multi_asset_results

            # Store analysis results in database
            await self.store_analysis_results(analysis_results)

            # Send notification to trading strategy agent
            await self.send_message(
                receiver_id="trading_strategy_001",
                message_type=MessageType.ANALYSIS_RESPONSE,
                payload={
                    "analysis_type": "comprehensive",
                    "symbols": list(market_data.keys()),
                    "timestamp": datetime.now().isoformat(),
                    "summary": {
                        "symbols_analyzed": len(individual_analysis),
                        "patterns_detected": sum([len(analysis.get('pattern_analysis', {}).get('candlestick_patterns', {}))
                                                for analysis in individual_analysis.values()]),
                        "high_quality_analyses": sum([1 for analysis in individual_analysis.values()
                                                    if analysis.get('analysis_quality', 0) > 0.8])
                    }
                }
            )

            self.logger.info(f"Completed comprehensive analysis for {len(individual_analysis)} symbols")
            return analysis_results

        except Exception as e:
            self.logger.error(f"Error in comprehensive analysis: {e}")
            return {"error": str(e)}

    async def perform_multi_asset_analysis(self, market_data: Dict[str, pd.DataFrame]) -> Dict:
        """Perform multi-asset correlation and risk analysis"""
        # Create returns DataFrame for all assets
        returns_data = {}
        volume_data = {}

        for symbol, data in market_data.items():
            if len(data) > 30:
                returns = data['close_price'].pct_change().dropna()
                returns_data[symbol] = returns

                if 'volume' in data.columns:
                    volume_data[symbol] = data['volume']

        if len(returns_data) < 2:
            return {"error": "Insufficient assets for multi-asset analysis"}

        # Align data by timestamp
        returns_df = pd.DataFrame(returns_data).dropna()
        volume_df = pd.DataFrame(volume_data).dropna() if volume_data else None

        results = {}

        # Correlation analysis
        if len(returns_df) > 60:
            correlation_results = self.correlation_analyzer.comprehensive_correlation_analysis(returns_df)
            results['correlation_analysis'] = correlation_results

        # Risk analysis
        if len(returns_df) > 100:
            risk_results = self.risk_analyzer.comprehensive_risk_analysis(returns_df, volumes_df=volume_df)
            results['risk_analysis'] = risk_results

        # Portfolio optimization suggestions
        if len(returns_df) > 252:  # At least 1 year of data
            target_return = returns_df.mean().mean() * 252  # Annualized average return
            portfolio_optimization = self.correlation_analyzer.matrix_formulas.portfolio_optimization_matrix(
                returns_df, target_return
            )
            results['portfolio_optimization'] = portfolio_optimization

        return results

    async def perform_statistical_analysis(self, symbols: List[str] = None) -> Dict:
        """Perform statistical analysis only"""
        market_data = await self.fetch_market_data(symbols)
        results = {}

        for symbol, data in market_data.items():
            if len(data) > 30:
                analysis = self.statistical_analyzer.perform_comprehensive_analysis(data, symbol)
                results[symbol] = analysis

        return results

    async def perform_correlation_analysis(self, symbols: List[str] = None) -> Dict:
        """Perform correlation analysis only"""
        market_data = await self.fetch_market_data(symbols)

        # Create returns DataFrame
        returns_data = {}
        for symbol, data in market_data.items():
            if len(data) > 30:
                returns = data['close_price'].pct_change().dropna()
                returns_data[symbol] = returns

        if len(returns_data) < 2:
            return {"error": "Need at least 2 symbols for correlation analysis"}

        returns_df = pd.DataFrame(returns_data).dropna()
        return self.correlation_analyzer.comprehensive_correlation_analysis(returns_df)

    async def perform_pattern_analysis(self, symbols: List[str] = None) -> Dict:
        """Perform pattern analysis only"""
        market_data = await self.fetch_market_data(symbols)
        results = {}

        for symbol, data in market_data.items():
            if len(data) > 50:
                analysis = self.pattern_analyzer.comprehensive_pattern_analysis(data)
                results[symbol] = analysis

        return results

    async def perform_risk_analysis(self, symbols: List[str] = None) -> Dict:
        """Perform risk analysis only"""
        market_data = await self.fetch_market_data(symbols)

        # Create returns DataFrame
        returns_data = {}
        volume_data = {}

        for symbol, data in market_data.items():
            if len(data) > 30:
                returns = data['close_price'].pct_change().dropna()
                returns_data[symbol] = returns
                if 'volume' in data.columns:
                    volume_data[symbol] = data['volume']

        if not returns_data:
            return {"error": "No valid return data for risk analysis"}

        returns_df = pd.DataFrame(returns_data).dropna()
        volume_df = pd.DataFrame(volume_data).dropna() if volume_data else None

        return self.risk_analyzer.comprehensive_risk_analysis(returns_df, volumes_df=volume_df)

    def _assess_analysis_quality(self, data: pd.DataFrame) -> float:
        """Assess the quality of analysis based on data characteristics"""
        quality_score = 1.0

        # Penalize for missing data
        missing_ratio = data.isnull().sum().sum() / (len(data) * len(data.columns))
        quality_score -= missing_ratio * 0.3

        # Penalize for insufficient data
        if len(data) < 100:
            quality_score -= (100 - len(data)) / 100 * 0.4

        # Bonus for long time series
        if len(data) > 1000:
            quality_score += 0.1

        # Check for data consistency
        if 'close_price' in data.columns:
            price_changes = data['close_price'].pct_change().abs()
            extreme_changes = (price_changes > 0.5).sum()
            if extreme_changes > len(data) * 0.01:  # More than 1% extreme moves
                quality_score -= 0.2

        return max(0.0, min(1.0, quality_score))

    async def store_analysis_results(self, analysis_results: Dict):
        """Store analysis results in database"""
        cursor = self.db_connection.cursor()

        try:
            # Store in analysis_results table
            run_id = str(uuid.uuid4()) if 'uuid' in globals() else f"run_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            cursor.execute("""
                INSERT INTO analysis_results
                (run_id, timestamp, selected_symbols, mathematical_scores, correlation_matrix, risk_metrics)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                run_id,
                datetime.now(),
                json.dumps(analysis_results.get('symbols_analyzed', [])),
                json.dumps({k: v for k, v in analysis_results.items() if 'analysis' in k}),
                json.dumps(analysis_results.get('multi_asset_analysis', {}).get('correlation_analysis', {})),
                json.dumps(analysis_results.get('multi_asset_analysis', {}).get('risk_analysis', {}))
            ))

            self.db_connection.commit()
            self.logger.info(f"Stored analysis results with run_id: {run_id}")

        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error storing analysis results: {e}")
        finally:
            cursor.close()

    async def cleanup(self):
        """Cleanup resources"""
        if self.db_connection:
            self.db_connection.close()
            self.logger.info("Database connection closed")