#!/usr/bin/env python3
"""
AstroA Paper Trading Engine
Integrates with live market data for realistic strategy validation
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import json
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from config.paper_trading_config import paper_config
from config.settings import Config
from agents.trading_strategy.trading_strategy_agent import TradingStrategyAgent
from shared.types.strategy_types import TradingSignal, Position, Order

class PaperTradingEngine:
    """
    Paper trading engine that connects AstroA strategies to simulated market data
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Validate configuration
        try:
            paper_config.validate()
        except ValueError as e:
            self.logger.error(f"Configuration error: {e}")
            raise

        # Initialize Alpaca API if credentials are provided
        self.alpaca_api = None
        if paper_config.api_key and paper_config.api_key != "demo_key":
            try:
                import alpaca_trade_api as tradeapi
                self.alpaca_api = tradeapi.REST(
                    paper_config.api_key,
                    paper_config.secret_key,
                    paper_config.base_url,
                    api_version='v2'
                )
                self.logger.info("Alpaca API initialized for live paper trading")
            except Exception as e:
                self.logger.warning(f"Alpaca API initialization failed: {e}")
                self.alpaca_api = None

        # Initialize AstroA trading strategy agent
        self.strategy_agent = TradingStrategyAgent()

        # Paper trading state
        self.is_running = False
        self.positions = {}
        self.portfolio_value = paper_config.initial_cash
        self.available_cash = paper_config.initial_cash
        self.trade_history = []
        self.performance_metrics = {}
        self.daily_pnl = 0.0
        self.total_pnl = 0.0

        # Market data simulation (using existing data collection)
        self.market_data_cache = {}
        self.last_update = None

        self.logger.info("Paper Trading Engine initialized")

    async def _get_binance_data(self) -> Dict[str, Any]:
        """Get live crypto data from Binance API with rate limiting"""
        try:
            import requests
            market_data = {}
            request_count = 0

            # Binance symbol mapping
            binance_symbols = {
                'BTCUSD': 'BTCUSDT',
                'ETHUSD': 'ETHUSDT',
                'ADAUSD': 'ADAUSDT',
                'SOLUSD': 'SOLUSDT',
                'DOTUSD': 'DOTUSDT',
                'LINKUSD': 'LINKUSDT',
                'AVAXUSD': 'AVAXUSDT',
                'MATICUSD': 'MATICUSDT'
            }

            # Rate limiting: Max 10 requests per second (well under 20/sec limit)
            for symbol in paper_config.tradable_symbols:
                if symbol in binance_symbols:
                    binance_symbol = binance_symbols[symbol]

                    # Rate limiting check
                    if request_count >= 10:
                        self.logger.info("⏱️  Binance rate limit: pausing 1 second")
                        await asyncio.sleep(1)
                        request_count = 0

                    # Get 24hr ticker data
                    url = f"https://api.binance.com/api/v3/ticker/24hr?symbol={binance_symbol}"
                    response = requests.get(url, timeout=10)
                    request_count += 1

                    if response.status_code == 200:
                        data = response.json()

                        # Small delay between requests (100ms)
                        await asyncio.sleep(0.1)

                        # Get kline data for OHLCV
                        kline_url = f"https://api.binance.com/api/v3/klines?symbol={binance_symbol}&interval=1m&limit=100"
                        kline_response = requests.get(kline_url, timeout=10)
                        request_count += 1

                        if kline_response.status_code == 200:
                            klines = kline_response.json()

                            df_data = []
                            for kline in klines:
                                df_data.append({
                                    'symbol': symbol,
                                    'close_price': float(kline[4]),
                                    'high_price': float(kline[2]),
                                    'low_price': float(kline[3]),
                                    'volume': float(kline[5]),
                                    'timestamp': pd.to_datetime(kline[0], unit='ms')
                                })

                            if df_data:
                                df = pd.DataFrame(df_data)
                                market_data[symbol] = df
                                self.logger.info(f"📈 Binance: {symbol} = ${float(data['lastPrice']):.2f}")

                    elif response.status_code == 429:
                        self.logger.warning("🚨 Binance rate limit hit! Backing off...")
                        await asyncio.sleep(5)
                        break

                    # Small delay between symbols
                    await asyncio.sleep(0.1)

            self.logger.info(f"✅ Binance: Made {request_count} API calls")
            return market_data

        except Exception as e:
            self.logger.warning(f"Binance API error: {e}")
            return {}

    async def _get_alpha_vantage_data(self) -> Dict[str, Any]:
        """Get market data from Alpha Vantage API"""
        try:
            import requests
            from config.settings import Config

            api_key = Config.ALPHA_VANTAGE_API_KEY
            if not api_key:
                return {}

            market_data = {}

            # Alpha Vantage crypto symbols
            av_symbols = {
                'BTCUSD': 'BTC',
                'ETHUSD': 'ETH',
                'ADAUSD': 'ADA',
                'SOLUSD': 'SOL',
                'DOTUSD': 'DOT'
            }

            for symbol in paper_config.tradable_symbols:
                if symbol in av_symbols:
                    crypto_symbol = av_symbols[symbol]

                    url = f"https://www.alphavantage.co/query"
                    params = {
                        'function': 'DIGITAL_CURRENCY_INTRADAY',
                        'symbol': crypto_symbol,
                        'market': 'USD',
                        'interval': '1min',
                        'apikey': api_key
                    }

                    response = requests.get(url, params=params, timeout=15)

                    if response.status_code == 200:
                        data = response.json()

                        if 'Time Series (Digital Currency Intraday)' in data:
                            time_series = data['Time Series (Digital Currency Intraday)']

                            df_data = []
                            for timestamp, values in list(time_series.items())[:100]:
                                df_data.append({
                                    'symbol': symbol,
                                    'close_price': float(values['4. close (USD)']),
                                    'high_price': float(values['2. high (USD)']),
                                    'low_price': float(values['3. low (USD)']),
                                    'volume': float(values['5. volume']),
                                    'timestamp': pd.to_datetime(timestamp)
                                })

                            if df_data:
                                df = pd.DataFrame(df_data)
                                market_data[symbol] = df
                                self.logger.info(f"📊 Alpha Vantage: {symbol} data retrieved")

                    # Rate limiting
                    await asyncio.sleep(12)  # Alpha Vantage: 5 calls per minute

            return market_data

        except Exception as e:
            self.logger.warning(f"Alpha Vantage API error: {e}")
            return {}

    async def _get_alpaca_data(self) -> Dict[str, Any]:
        """Get market data from Alpaca API"""
        try:
            market_data = {}

            for symbol in paper_config.tradable_symbols:
                try:
                    from alpaca_trade_api.rest import TimeFrame
                    bars = self.alpaca_api.get_bars(
                        symbol,
                        TimeFrame.Minute,
                        limit=100,
                        adjustment='raw'
                    )

                    if bars:
                        df_data = []
                        for bar in bars:
                            df_data.append({
                                'symbol': symbol,
                                'close_price': float(bar.c),
                                'high_price': float(bar.h),
                                'low_price': float(bar.l),
                                'volume': float(bar.v),
                                'timestamp': bar.t
                            })

                        if df_data:
                            df = pd.DataFrame(df_data)
                            market_data[symbol] = df

                except Exception as e:
                    self.logger.warning(f"Alpaca data error for {symbol}: {e}")
                    continue

            return market_data

        except Exception as e:
            self.logger.warning(f"Alpaca API error: {e}")
            return {}

    async def _get_database_data(self) -> Dict[str, Any]:
        """Get market data from database"""
        try:
            import psycopg2
            market_data = {}

            conn = psycopg2.connect(Config.DATABASE_URL)
            cursor = conn.cursor()

            for symbol in paper_config.tradable_symbols:
                # Convert symbol format (BTCUSD -> BTC/USDT for database)
                db_symbol = symbol.replace('USD', '/USDT') if 'USD' in symbol else symbol

                cursor.execute("""
                    SELECT symbol, close_price, high_price, low_price, volume, timestamp
                    FROM market_data
                    WHERE symbol = %s
                    ORDER BY timestamp DESC
                    LIMIT 100
                """, (db_symbol,))

                rows = cursor.fetchall()
                if rows:
                    df = pd.DataFrame(rows, columns=['symbol', 'close_price', 'high_price', 'low_price', 'volume', 'timestamp'])
                    market_data[symbol] = df

            cursor.close()
            conn.close()

            return market_data

        except Exception as e:
            self.logger.warning(f"Database error: {e}")
            return {}

    async def _enhance_with_ai_analysis(self, market_data: Dict[str, Any]):
        """Enhance market data with DeepSeek AI analysis"""
        try:
            import requests
            from config.settings import Config

            api_key = Config.DEEPSEEK_API_KEY
            if not api_key or not market_data:
                return

            # Prepare market summary for AI analysis
            market_summary = []
            for symbol, df in market_data.items():
                if not df.empty:
                    latest = df.iloc[-1]
                    prev = df.iloc[-2] if len(df) > 1 else latest

                    change = ((latest['close_price'] - prev['close_price']) / prev['close_price']) * 100

                    market_summary.append(f"{symbol}: ${latest['close_price']:.2f} ({change:+.2f}%)")

            if not market_summary:
                return

            # Call DeepSeek AI for market analysis
            prompt = f"""
            Current crypto market data:
            {chr(10).join(market_summary)}

            Provide a brief market sentiment analysis (bullish/bearish/neutral) and key insights for algorithmic trading. Keep response under 200 words.
            """

            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            payload = {
                'model': 'deepseek-chat',
                'messages': [{'role': 'user', 'content': prompt}],
                'max_tokens': 200,
                'temperature': 0.3
            }

            response = requests.post(
                'https://api.deepseek.com/chat/completions',
                headers=headers,
                json=payload,
                timeout=10
            )

            if response.status_code == 200:
                ai_response = response.json()
                analysis = ai_response['choices'][0]['message']['content']

                self.logger.info(f"🤖 AI Market Analysis: {analysis[:100]}...")

                # Store AI analysis for strategy use
                self.ai_market_analysis = {
                    'timestamp': datetime.now(),
                    'analysis': analysis,
                    'market_summary': market_summary
                }

        except Exception as e:
            self.logger.warning(f"AI analysis error: {e}")

    async def start_paper_trading(self):
        """Start the paper trading session"""
        self.logger.info("Starting paper trading session...")

        try:
            # Test Alpaca connection if available
            if self.alpaca_api:
                account = self.alpaca_api.get_account()
                self.logger.info(f"Alpaca account status: {account.status}")
                self.logger.info(f"Portfolio value: ${float(account.portfolio_value):,.2f}")

                # Update initial values from Alpaca
                self.portfolio_value = float(account.portfolio_value)
                self.available_cash = float(account.cash)

            # Initialize strategy agent
            await self.strategy_agent.initialize()
            self.logger.info("Strategy agent initialized")

            # Start trading loop
            self.is_running = True
            await self._trading_loop()

        except Exception as e:
            self.logger.error(f"Failed to start paper trading: {str(e)}")
            raise

    async def stop_paper_trading(self):
        """Stop the paper trading session"""
        self.logger.info("Stopping paper trading session...")
        self.is_running = False

        # Close all positions
        await self._close_all_positions()

        # Generate final report
        self._generate_session_report()

    async def _trading_loop(self):
        """Main trading loop"""
        iteration = 0
        
        while self.is_running:
            try:
                iteration += 1
                self.logger.info(f"Trading iteration {iteration}")

                # Get latest market data through existing data collection
                market_data = await self._get_market_data()
                
                if market_data:
                    # Process data through AstroA strategy agent
                    portfolio_state = self._get_portfolio_state()
                    
                    # Execute strategy agent
                    strategy_result = await self.strategy_agent.execute_strategies()
                    
                    if hasattr(strategy_result, 'data') and strategy_result.data:
                        # Process any trading signals
                        signals = strategy_result.data.get('signals', [])
                        if signals:
                            await self._process_trading_signals(signals)

                # Update performance metrics
                self._update_performance_metrics()

                # Log current status
                self._log_status()

                # Wait for next iteration
                await asyncio.sleep(paper_config.data_update_interval)

            except Exception as e:
                self.logger.error(f"Error in trading loop: {str(e)}")
                await asyncio.sleep(5)  # Short pause before retry

    async def _get_market_data(self) -> Dict[str, Any]:
        """Get market data using multiple live APIs with intelligent fallback"""
        market_data = {}

        try:
            # Method 1: Try Binance API for crypto data (most reliable for crypto)
            market_data = await self._get_binance_data()
            if market_data:
                self.logger.info(f"✅ Got live data from Binance for {len(market_data)} symbols")

                # Enhance with AI analysis
                await self._enhance_with_ai_analysis(market_data)

                self.market_data_cache = market_data
                self.last_update = datetime.now()
                return market_data

            # Method 2: Try Alpha Vantage API
            market_data = await self._get_alpha_vantage_data()
            if market_data:
                self.logger.info(f"✅ Got live data from Alpha Vantage for {len(market_data)} symbols")
                self.market_data_cache = market_data
                self.last_update = datetime.now()
                return market_data

            # Method 3: Try Alpaca API (limited crypto support)
            if self.alpaca_api:
                self.logger.info("Trying Alpaca API for market data...")
                market_data = await self._get_alpaca_data()
                if market_data:
                    self.logger.info(f"✅ Got live data from Alpaca for {len(market_data)} symbols")
                    self.market_data_cache = market_data
                    self.last_update = datetime.now()
                    return market_data

            # Method 4: Database fallback
            self.logger.info("📊 Using database market data as fallback...")
            market_data = await self._get_database_data()

            self.market_data_cache = market_data
            self.last_update = datetime.now()

            self.logger.info(f"Retrieved market data for {len(market_data)} symbols")
            return market_data

        except Exception as e:
            self.logger.error(f"Error getting market data: {e}")
            # Return cached data if available
            return self.market_data_cache

    def _get_portfolio_state(self) -> Dict[str, Any]:
        """Get current portfolio state"""
        return {
            'portfolio_value': self.portfolio_value,
            'available_cash': self.available_cash,
            'positions': self.positions,
            'daily_pnl': self.daily_pnl,
            'total_pnl': self.total_pnl
        }

    async def _process_trading_signals(self, signals: List[TradingSignal]):
        """Process trading signals from strategy agent"""
        for signal in signals:
            try:
                await self._execute_signal(signal)
            except Exception as e:
                self.logger.error(f"Error executing signal for {signal.symbol}: {e}")

    async def _execute_signal(self, signal: TradingSignal):
        """Execute a trading signal"""
        symbol = signal.symbol
        
        # Get current price from market data
        current_price = self._get_current_price(symbol)
        if not current_price:
            self.logger.warning(f"No price data for {symbol}")
            return

        # Calculate position size based on risk management
        position_size = self._calculate_position_size(signal, current_price)
        
        if position_size <= 0:
            return

        # Execute trade based on signal type
        if signal.signal_type.value == 'BUY':
            await self._execute_buy(symbol, position_size, current_price, signal)
        elif signal.signal_type.value == 'SELL':
            await self._execute_sell(symbol, position_size, current_price, signal)

    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for symbol"""
        if symbol in self.market_data_cache:
            df = self.market_data_cache[symbol]
            if not df.empty:
                return float(df.iloc[0]['close_price'])
        return None

    def _calculate_position_size(self, signal: TradingSignal, price: float) -> float:
        """Calculate position size based on risk management"""
        # Maximum position value
        max_position_value = self.portfolio_value * paper_config.max_position_size
        
        # Available cash constraint
        available_for_position = min(max_position_value, self.available_cash * 0.95)
        
        # Calculate shares/units
        position_size = available_for_position / price
        
        return position_size

    async def _execute_buy(self, symbol: str, quantity: float, price: float, signal: TradingSignal):
        """Execute buy order"""
        trade_value = quantity * price
        
        if trade_value > self.available_cash:
            self.logger.warning(f"Insufficient cash for {symbol} buy: need ${trade_value:.2f}, have ${self.available_cash:.2f}")
            return

        # Execute trade
        self.available_cash -= trade_value
        
        if symbol in self.positions:
            self.positions[symbol]['quantity'] += quantity
            self.positions[symbol]['avg_price'] = (
                (self.positions[symbol]['avg_price'] * self.positions[symbol]['quantity'] + price * quantity) /
                (self.positions[symbol]['quantity'] + quantity)
            )
        else:
            self.positions[symbol] = {
                'quantity': quantity,
                'avg_price': price,
                'entry_time': datetime.now()
            }

        # Record trade
        trade = {
            'timestamp': datetime.now(),
            'symbol': symbol,
            'action': 'BUY',
            'quantity': quantity,
            'price': price,
            'value': trade_value,
            'signal_confidence': signal.confidence
        }
        self.trade_history.append(trade)
        
        self.logger.info(f"BUY {quantity:.4f} {symbol} @ ${price:.2f} (${trade_value:.2f})")

    async def _execute_sell(self, symbol: str, quantity: float, price: float, signal: TradingSignal):
        """Execute sell order"""
        if symbol not in self.positions:
            self.logger.warning(f"No position to sell for {symbol}")
            return

        current_position = self.positions[symbol]['quantity']
        sell_quantity = min(quantity, current_position)
        
        if sell_quantity <= 0:
            return

        # Execute trade
        trade_value = sell_quantity * price
        self.available_cash += trade_value
        
        # Update position
        self.positions[symbol]['quantity'] -= sell_quantity
        
        if self.positions[symbol]['quantity'] <= 0.001:  # Close position if very small
            del self.positions[symbol]

        # Record trade
        trade = {
            'timestamp': datetime.now(),
            'symbol': symbol,
            'action': 'SELL',
            'quantity': sell_quantity,
            'price': price,
            'value': trade_value,
            'signal_confidence': signal.confidence
        }
        self.trade_history.append(trade)
        
        self.logger.info(f"SELL {sell_quantity:.4f} {symbol} @ ${price:.2f} (${trade_value:.2f})")

    async def _close_all_positions(self):
        """Close all open positions"""
        for symbol in list(self.positions.keys()):
            current_price = self._get_current_price(symbol)
            if current_price:
                quantity = self.positions[symbol]['quantity']
                # Create sell signal
                from shared.types.strategy_types import SignalType
                sell_signal = TradingSignal(
                    symbol=symbol,
                    signal_type=SignalType.SELL,
                    confidence=1.0,
                    price=current_price,
                    timestamp=datetime.now()
                )
                await self._execute_sell(symbol, quantity, current_price, sell_signal)

    def _update_performance_metrics(self):
        """Update performance metrics"""
        # Calculate current portfolio value
        position_value = 0
        for symbol, position in self.positions.items():
            current_price = self._get_current_price(symbol)
            if current_price:
                position_value += position['quantity'] * current_price

        self.portfolio_value = self.available_cash + position_value
        self.total_pnl = self.portfolio_value - paper_config.initial_cash
        
        # Update metrics
        self.performance_metrics = {
            'portfolio_value': self.portfolio_value,
            'available_cash': self.available_cash,
            'position_value': position_value,
            'total_pnl': self.total_pnl,
            'total_pnl_pct': (self.total_pnl / paper_config.initial_cash) * 100,
            'total_trades': len(self.trade_history),
            'open_positions': len(self.positions),
            'last_update': datetime.now()
        }

    def _log_status(self):
        """Log current status"""
        self.logger.info(f"Portfolio: ${self.portfolio_value:.2f} | P&L: ${self.total_pnl:.2f} ({self.total_pnl/paper_config.initial_cash*100:.2f}%) | Positions: {len(self.positions)} | Trades: {len(self.trade_history)}")

    def _generate_session_report(self):
        """Generate final session report"""
        report = {
            'session_summary': self.performance_metrics,
            'trade_history': [
                {**trade, 'timestamp': trade['timestamp'].isoformat()} 
                for trade in self.trade_history
            ],
            'final_positions': self.positions,
            'final_portfolio_value': self.portfolio_value,
            'total_return': self.total_pnl,
            'total_return_pct': (self.total_pnl / paper_config.initial_cash) * 100,
            'total_trades': len(self.trade_history)
        }

        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"data/paper_trading_report_{timestamp}.json"

        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        self.logger.info(f"Session report saved to {report_file}")
        print(f"\n📊 Paper Trading Session Complete!")
        print(f"📈 Final Portfolio Value: ${self.portfolio_value:,.2f}")
        print(f"💰 Total P&L: ${self.total_pnl:,.2f} ({self.total_pnl/paper_config.initial_cash*100:.2f}%)")
        print(f"🔄 Total Trades: {len(self.trade_history)}")
        print(f"📄 Report saved: {report_file}")

# Usage
async def main():
    engine = PaperTradingEngine()
    await engine.start_paper_trading()

if __name__ == "__main__":
    asyncio.run(main())
