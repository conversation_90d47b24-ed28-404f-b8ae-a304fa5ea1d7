"""
Base Agent Class for AstroA
Provides common functionality for all agents using DeepSeek AI
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime
from config.deepseek_client import get_deepseek_client, ChatMessage

@dataclass
class AgentResult:
    """Standardized agent result format"""
    agent_name: str
    status: str  # 'success', 'failed', 'partial'
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    error_message: Optional[str] = None

class BaseAgent(ABC):
    """
    Base class for all AstroA agents
    Provides DeepSeek AI integration and common utilities
    """

    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.deepseek_client = get_deepseek_client()
        self.logger = logging.getLogger(f"AstroA.{name}")
        self.is_running = False
        self.last_execution = None

    @abstractmethod
    async def execute(self, *args, **kwargs) -> AgentResult:
        """
        Main execution method - must be implemented by subclasses
        """
        pass

    async def run(self, *args, **kwargs) -> AgentResult:
        """
        Wrapper method that handles execution with error handling and timing
        """
        start_time = asyncio.get_event_loop().time()
        self.is_running = True

        try:
            self.logger.info(f"Starting execution of {self.name}")
            result = await self.execute(*args, **kwargs)
            result.execution_time = asyncio.get_event_loop().time() - start_time
            self.logger.info(f"Completed {self.name} in {result.execution_time:.2f}s")
            return result

        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            self.logger.error(f"Error in {self.name}: {str(e)}")
            return AgentResult(
                agent_name=self.name,
                status='failed',
                execution_time=execution_time,
                error_message=str(e)
            )
        finally:
            self.is_running = False
            self.last_execution = datetime.now()

    async def query_deepseek(
        self,
        prompt: str,
        system_prompt: str = None,
        temperature: float = None,
        max_tokens: int = None
    ) -> str:
        """
        Query DeepSeek AI with a prompt

        Args:
            prompt: User prompt
            system_prompt: System prompt (optional)
            temperature: Sampling temperature
            max_tokens: Maximum tokens

        Returns:
            AI response as string
        """
        messages = []

        if system_prompt:
            messages.append(ChatMessage("system", system_prompt))

        messages.append(ChatMessage("user", prompt))

        try:
            response = await self.deepseek_client.chat_completion(
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response['choices'][0]['message']['content']

        except Exception as e:
            self.logger.error(f"DeepSeek query failed: {str(e)}")
            raise

    async def analyze_with_ai(self, data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """
        Generic AI analysis method

        Args:
            data: Data to analyze
            analysis_type: Type of analysis to perform

        Returns:
            Analysis results
        """
        system_prompt = f"""You are AstroA's {self.name} agent, specializing in {analysis_type}.
        Analyze the provided data and return actionable insights in JSON format.
        Be precise, data-driven, and focus on cryptocurrency market analysis."""

        user_prompt = f"""Perform {analysis_type} on this data:

{data}

Return your analysis as valid JSON with clear structure and actionable insights."""

        try:
            response = await self.query_deepseek(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.1
            )

            # Try to parse as JSON, fallback to text if needed
            try:
                import json
                return json.loads(response)
            except json.JSONDecodeError:
                return {"analysis": response, "format": "text"}

        except Exception as e:
            return {"error": str(e), "status": "failed"}

    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "name": self.name,
            "description": self.description,
            "is_running": self.is_running,
            "last_execution": self.last_execution.isoformat() if self.last_execution else None
        }

    def __repr__(self):
        return f"<{self.__class__.__name__}(name='{self.name}')>"

class AgentOrchestrator:
    """
    Orchestrates multiple agents and manages their execution
    """

    def __init__(self):
        self.agents: List[BaseAgent] = []
        self.results: List[AgentResult] = []
        self.logger = logging.getLogger("AstroA.Orchestrator")

    def register_agent(self, agent: BaseAgent):
        """Register an agent with the orchestrator"""
        self.agents.append(agent)
        self.logger.info(f"Registered agent: {agent.name}")

    async def run_agent(self, agent_name: str, *args, **kwargs) -> AgentResult:
        """Run a specific agent by name"""
        agent = self._get_agent(agent_name)
        if not agent:
            raise ValueError(f"Agent '{agent_name}' not found")

        result = await agent.run(*args, **kwargs)
        self.results.append(result)
        return result

    async def run_agents_parallel(self, agent_names: List[str], *args, **kwargs) -> List[AgentResult]:
        """Run multiple agents in parallel"""
        tasks = []
        for name in agent_names:
            agent = self._get_agent(name)
            if agent:
                tasks.append(agent.run(*args, **kwargs))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Convert exceptions to failed results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(AgentResult(
                    agent_name=agent_names[i],
                    status='failed',
                    error_message=str(result)
                ))
            else:
                processed_results.append(result)

        self.results.extend(processed_results)
        return processed_results

    async def run_agents_sequential(self, agent_names: List[str], *args, **kwargs) -> List[AgentResult]:
        """Run multiple agents sequentially"""
        results = []
        for name in agent_names:
            result = await self.run_agent(name, *args, **kwargs)
            results.append(result)

        return results

    def _get_agent(self, name: str) -> Optional[BaseAgent]:
        """Get agent by name"""
        for agent in self.agents:
            if agent.name == name:
                return agent
        return None

    def get_agent_status(self) -> Dict[str, Dict]:
        """Get status of all registered agents"""
        return {agent.name: agent.get_status() for agent in self.agents}

    def get_recent_results(self, limit: int = 10) -> List[AgentResult]:
        """Get recent execution results"""
        return sorted(self.results, key=lambda x: x.timestamp, reverse=True)[:limit]