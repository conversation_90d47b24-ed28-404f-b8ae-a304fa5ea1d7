"""
Momentum Trading Strategy
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Any
from datetime import datetime
from decimal import Decimal

from shared.utils.base_strategy import BaseStrategy
from shared.types.strategy_types import (
    StrategyType, TradingSignal, SignalType, Position
)

class MomentumStrategy(BaseStrategy):
    """Momentum strategy using trend and momentum indicators"""

    def __init__(self, strategy_id: str, symbols: List[str]):
        default_params = {
            'short_ma_period': 10,
            'long_ma_period': 30,
            'rsi_period': 14,
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'momentum_threshold': 0.02,  # 2% momentum threshold
            'min_confidence': 0.65,
            'max_holding_period': 15  # days
        }

        super().__init__(
            strategy_id=strategy_id,
            strategy_type=StrategyType.MOMENTUM,
            symbols=symbols,
            parameters=default_params
        )

    async def generate_signals(self,
                             market_data: pd.DataFrame,
                             analysis_results: Dict[str, Any]) -> List[TradingSignal]:
        """Generate momentum-based signals"""
        signals = []

        for symbol in self.symbols:
            try:
                # Filter data for this symbol
                symbol_data = market_data[market_data['symbol'] == symbol].copy()
                if len(symbol_data) < max(self.parameters['long_ma_period'], self.parameters['rsi_period']):
                    continue

                # Sort by timestamp
                symbol_data = symbol_data.sort_values('timestamp')

                # Convert Decimal to float for calculations
                symbol_data['close_price'] = symbol_data['close_price'].astype(float)
                symbol_data['high_price'] = symbol_data['high_price'].astype(float)
                symbol_data['low_price'] = symbol_data['low_price'].astype(float)

                close_prices = symbol_data['close_price']

                # Calculate moving averages
                short_ma = close_prices.rolling(window=self.parameters['short_ma_period']).mean()
                long_ma = close_prices.rolling(window=self.parameters['long_ma_period']).mean()

                # Calculate RSI
                rsi = self._calculate_rsi(close_prices, self.parameters['rsi_period'])

                # Calculate momentum
                momentum = close_prices.pct_change(periods=5).iloc[-1]  # 5-period momentum

                # Get current values
                current_price = close_prices.iloc[-1]
                current_short_ma = short_ma.iloc[-1]
                current_long_ma = long_ma.iloc[-1]
                current_rsi = rsi.iloc[-1]

                # Generate signals
                signal = None
                confidence = 0.0

                # Bullish momentum conditions
                if (current_short_ma > current_long_ma and  # Uptrend
                    momentum > self.parameters['momentum_threshold'] and  # Strong momentum
                    current_rsi > 50 and current_rsi < self.parameters['rsi_overbought']):  # Not overbought

                    signal = SignalType.BUY

                    # Calculate confidence based on multiple factors
                    ma_spread = (current_short_ma - current_long_ma) / current_long_ma
                    momentum_strength = min(momentum / 0.05, 1.0)  # Normalize to 0-1
                    rsi_strength = (current_rsi - 50) / 50 if current_rsi > 50 else 0

                    confidence = (ma_spread * 0.3 + momentum_strength * 0.4 + rsi_strength * 0.3)
                    confidence = min(max(confidence, 0), 1)

                # Bearish momentum conditions
                elif (current_short_ma < current_long_ma and  # Downtrend
                      momentum < -self.parameters['momentum_threshold'] and  # Strong negative momentum
                      current_rsi < 50 and current_rsi > self.parameters['rsi_oversold']):  # Not oversold

                    signal = SignalType.SELL

                    # Calculate confidence for short signal
                    ma_spread = (current_long_ma - current_short_ma) / current_long_ma
                    momentum_strength = min(abs(momentum) / 0.05, 1.0)
                    rsi_strength = (50 - current_rsi) / 50 if current_rsi < 50 else 0

                    confidence = (ma_spread * 0.3 + momentum_strength * 0.4 + rsi_strength * 0.3)
                    confidence = min(max(confidence, 0), 1)

                # Only generate signal if confidence is sufficient
                if signal and confidence >= self.parameters['min_confidence']:

                    # Get volatility from analysis results
                    volatility = 0.025  # Default
                    if symbol in analysis_results and 'return_stats' in analysis_results[symbol]:
                        volatility = analysis_results[symbol]['return_stats'].get('annualized_volatility', 0.025)

                    # Calculate technical levels
                    atr = self._calculate_atr(symbol_data)
                    stop_loss_price = self._calculate_stop_loss(current_price, signal, atr)
                    take_profit_price = self._calculate_take_profit(current_price, stop_loss_price, signal)

                    trading_signal = TradingSignal(
                        symbol=symbol,
                        signal_type=signal,
                        confidence=confidence,
                        timestamp=datetime.now(),
                        price=current_price,
                        strategy_id=self.strategy_id,
                        metadata={
                            'short_ma': current_short_ma,
                            'long_ma': current_long_ma,
                            'rsi': current_rsi,
                            'momentum': momentum,
                            'volatility': volatility,
                            'stop_loss_price': stop_loss_price,
                            'take_profit_price': take_profit_price,
                            'atr': atr,
                            'ma_spread': (current_short_ma - current_long_ma) / current_long_ma
                        }
                    )

                    signals.append(trading_signal)
                    self.logger.info(f"Generated {signal.value} momentum signal for {symbol} (Confidence: {confidence:.2f})")

            except Exception as e:
                self.logger.error(f"Error generating momentum signal for {symbol}: {e}")
                continue

        return signals

    async def calculate_position_size(self,
                                    signal: TradingSignal,
                                    portfolio_value: float,
                                    risk_budget: float) -> float:
        """Calculate position size for momentum strategy"""

        # Base risk amount
        base_risk_amount = portfolio_value * risk_budget

        # Adjust for signal confidence and momentum strength
        momentum = signal.metadata.get('momentum', 0)
        momentum_multiplier = 1.0 + min(abs(momentum) * 10, 0.5)  # Increase size for strong momentum

        confidence_adjusted_risk = base_risk_amount * signal.confidence * momentum_multiplier

        # Calculate position size based on stop loss
        if signal.metadata and 'stop_loss_price' in signal.metadata:
            stop_loss_price = signal.metadata['stop_loss_price']
            risk_per_share = abs(signal.price - stop_loss_price)

            if risk_per_share > 0:
                position_size = confidence_adjusted_risk / risk_per_share
            else:
                position_size = 0
        else:
            volatility = signal.metadata.get('volatility', 0.025)
            position_size = confidence_adjusted_risk / (signal.price * volatility * 2)

        return max(0, position_size)

    async def should_exit_position(self,
                                 position: Position,
                                 current_data: Dict[str, Any]) -> bool:
        """Determine exit conditions for momentum positions"""

        try:
            current_price = current_data.get('current_price', position.current_price)

            # Check if momentum has reversed
            if 'momentum' in current_data:
                current_momentum = current_data['momentum']

                # Exit long positions if momentum turns negative
                if position.position_type == 'long' and current_momentum < -0.01:
                    self.logger.info(f"Exiting long position for {position.symbol} - momentum reversed")
                    return True

                # Exit short positions if momentum turns positive
                if position.position_type == 'short' and current_momentum > 0.01:
                    self.logger.info(f"Exiting short position for {position.symbol} - momentum reversed")
                    return True

            # Check moving average crossover reversal
            if 'short_ma' in current_data and 'long_ma' in current_data:
                short_ma = current_data['short_ma']
                long_ma = current_data['long_ma']

                # Exit long if short MA crosses below long MA
                if position.position_type == 'long' and short_ma < long_ma:
                    self.logger.info(f"Exiting long position for {position.symbol} - MA crossover reversal")
                    return True

                # Exit short if short MA crosses above long MA
                if position.position_type == 'short' and short_ma > long_ma:
                    self.logger.info(f"Exiting short position for {position.symbol} - MA crossover reversal")
                    return True

            # Maximum holding period
            days_held = (datetime.now() - position.entry_time).days
            if days_held >= self.parameters['max_holding_period']:
                self.logger.info(f"Exiting position for {position.symbol} - maximum holding period reached")
                return True

            # Stop loss and take profit
            if position.position_type == 'long':
                if (position.stop_loss and current_price <= position.stop_loss) or \
                   (position.take_profit and current_price >= position.take_profit):
                    return True
            else:
                if (position.stop_loss and current_price >= position.stop_loss) or \
                   (position.take_profit and current_price <= position.take_profit):
                    return True

        except Exception as e:
            self.logger.error(f"Error checking momentum exit condition for {position.symbol}: {e}")

        return False

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        try:
            high = data['high_price']
            low = data['low_price']
            close = data['close_price']

            tr1 = high - low
            tr2 = np.abs(high - close.shift(1))
            tr3 = np.abs(low - close.shift(1))

            true_range = np.maximum(tr1, np.maximum(tr2, tr3))
            atr = true_range.rolling(window=period).mean().iloc[-1]

            return atr if not np.isnan(atr) else 0.025 * close.iloc[-1]
        except:
            return 0.025 * data['close_price'].iloc[-1]

    def _calculate_stop_loss(self, entry_price: float, signal: SignalType, atr: float) -> float:
        """Calculate stop loss for momentum strategy"""
        stop_distance = atr * 2.0  # 2x ATR stop for momentum

        if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            return entry_price - stop_distance
        else:
            return entry_price + stop_distance

    def _calculate_take_profit(self, entry_price: float, stop_loss: float, signal: SignalType) -> float:
        """Calculate take profit with 3:1 risk-reward for momentum"""
        risk = abs(entry_price - stop_loss)
        reward = risk * 3.0  # 3:1 risk-reward for momentum

        if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            return entry_price + reward
        else:
            return entry_price - reward

    def get_strategy_parameters(self) -> Dict[str, Any]:
        """Get current strategy parameters"""
        return self.parameters.copy()