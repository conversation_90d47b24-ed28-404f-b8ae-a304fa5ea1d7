"""
Market Data Collector for Cryptocurrency and Traditional Markets
"""
import asyncio
import ccxt
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

from shared.types.agent_types import MarketData

class MarketDataCollector:
    """Collects market data from various sources"""

    def __init__(self):
        self.logger = logging.getLogger("market_collector")
        self.exchanges = self._initialize_exchanges()

        # Will be populated dynamically with top 100 assets
        self.top_assets = []
        self.crypto_symbols = []
        self.stock_symbols = []

        # Cache for volume rankings
        self.volume_rankings_cache = {}
        self.last_ranking_update = None

    def _initialize_exchanges(self) -> Dict:
        """Initialize cryptocurrency exchanges"""
        exchanges = {}

        try:
            # Binance (most liquid crypto exchange)
            exchanges['binance'] = ccxt.binance({
                'apiKey': '',  # Add from .env if needed
                'secret': '',  # Add from .env if needed
                'sandbox': False,
                'rateLimit': 1200,  # Binance rate limit
            })

            # Coinbase Pro - Note: coinbasepro was renamed to coinbase
            try:
                exchanges['coinbase'] = ccxt.coinbase({
                    'apiKey': '',
                    'secret': '',
                    'passphrase': '',
                    'sandbox': False,
                    'rateLimit': 1000,
                })
            except AttributeError:
                # Fallback if coinbase is not available
                self.logger.warning("Coinbase exchange not available in this ccxt version")

        except Exception as e:
            self.logger.error(f"Error initializing exchanges: {e}")

        return exchanges

    async def collect_crypto_data(self, timeframe: str = '1h', limit: int = 100) -> List[MarketData]:
        """Collect cryptocurrency market data"""
        market_data = []

        for exchange_name, exchange in self.exchanges.items():
            for symbol in self.crypto_symbols:
                try:
                    # Fetch OHLCV data
                    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)

                    for candle in ohlcv:
                        data = MarketData(
                            symbol=symbol.replace('/', ''),
                            timestamp=datetime.fromtimestamp(candle[0] / 1000),
                            open_price=float(candle[1]) if candle[1] is not None else 0.0,
                            high_price=float(candle[2]) if candle[2] is not None else 0.0,
                            low_price=float(candle[3]) if candle[3] is not None else 0.0,
                            close_price=float(candle[4]) if candle[4] is not None else 0.0,
                            volume=float(candle[5]) if candle[5] is not None else 0.0,
                            exchange=exchange_name,
                            timeframe=timeframe
                        )
                        market_data.append(data)

                    self.logger.info(f"Collected {len(ohlcv)} candles for {symbol} from {exchange_name}")

                except Exception as e:
                    self.logger.error(f"Error collecting {symbol} from {exchange_name}: {e}")
                    continue

        return market_data

    async def collect_stock_data(self, period: str = '1d', interval: str = '1h') -> List[MarketData]:
        """Collect traditional stock market data"""
        market_data = []

        for symbol in self.stock_symbols:
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period=period, interval=interval)

                for timestamp, row in hist.iterrows():
                    data = MarketData(
                        symbol=symbol,
                        timestamp=timestamp.to_pydatetime(),
                        open_price=float(row['Open']),
                        high_price=float(row['High']),
                        low_price=float(row['Low']),
                        close_price=float(row['Close']),
                        volume=float(row['Volume']),
                        exchange='yahoo_finance',
                        timeframe=interval
                    )
                    market_data.append(data)

                self.logger.info(f"Collected {len(hist)} data points for {symbol}")

            except Exception as e:
                self.logger.error(f"Error collecting {symbol}: {e}")
                continue

        return market_data

    async def collect_all_market_data(self) -> List[MarketData]:
        """Collect data from all sources"""
        self.logger.info("Starting market data collection...")

        # Collect crypto and stock data concurrently
        crypto_task = asyncio.create_task(self.collect_crypto_data())
        stock_task = asyncio.create_task(self.collect_stock_data())

        crypto_data, stock_data = await asyncio.gather(crypto_task, stock_task)

        all_data = crypto_data + stock_data
        self.logger.info(f"Collected total {len(all_data)} market data points")

        return all_data

    async def discover_top_100_assets(self) -> List[Dict]:
        """Discover the 100 most traded assets across crypto and traditional markets"""
        self.logger.info("Discovering top 100 most traded assets...")

        try:
            # Get top crypto assets by volume
            crypto_rankings = await self._get_top_crypto_by_volume(50)  # Top 50 crypto

            # Get top stock assets by volume
            stock_rankings = await self._get_top_stocks_by_volume(50)   # Top 50 stocks

            # Combine and sort by total volume
            all_assets = crypto_rankings + stock_rankings
            all_assets.sort(key=lambda x: x['volume_24h'], reverse=True)

            # Take top 100
            top_100 = all_assets[:100]

            # Update internal lists
            self.top_assets = top_100
            self.crypto_symbols = [asset['symbol'] for asset in top_100 if asset['type'] == 'crypto']
            self.stock_symbols = [asset['symbol'] for asset in top_100 if asset['type'] == 'stock']

            # Cache results
            self.volume_rankings_cache = {
                'timestamp': datetime.now(),
                'top_100': top_100,
                'crypto_count': len(self.crypto_symbols),
                'stock_count': len(self.stock_symbols)
            }
            self.last_ranking_update = datetime.now()

            self.logger.info(f"Discovered top 100 assets: {len(self.crypto_symbols)} crypto, {len(self.stock_symbols)} stocks")
            return top_100

        except Exception as e:
            self.logger.error(f"Error discovering top assets: {e}")
            # Fallback to default symbols
            await self._use_fallback_symbols()
            return self.top_assets

    async def _get_top_crypto_by_volume(self, limit: int = 50) -> List[Dict]:
        """Get top crypto assets by 24h volume from Binance"""
        try:
            import requests

            # Get 24hr ticker statistics from Binance
            url = "https://api.binance.com/api/v3/ticker/24hr"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                tickers = response.json()

                # Filter USDT pairs and sort by volume
                usdt_pairs = [
                    {
                        'symbol': ticker['symbol'].replace('USDT', '/USDT'),
                        'base_symbol': ticker['symbol'].replace('USDT', ''),
                        'volume_24h': float(ticker['quoteVolume']),  # Volume in USDT
                        'price': float(ticker['lastPrice']),
                        'change_24h': float(ticker['priceChangePercent']),
                        'type': 'crypto',
                        'exchange': 'binance'
                    }
                    for ticker in tickers
                    if ticker['symbol'].endswith('USDT') and float(ticker['quoteVolume']) > 1000000  # Min $1M volume
                ]

                # Sort by volume and take top N
                usdt_pairs.sort(key=lambda x: x['volume_24h'], reverse=True)
                top_crypto = usdt_pairs[:limit]

                self.logger.info(f"Found {len(top_crypto)} top crypto assets from Binance")
                return top_crypto

            else:
                self.logger.warning(f"Binance API error: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"Error getting top crypto assets: {e}")
            return []

    async def _get_top_stocks_by_volume(self, limit: int = 50) -> List[Dict]:
        """Get top stock assets by volume"""
        try:
            # Use a predefined list of high-volume stocks
            # In production, you'd use APIs like Alpha Vantage, IEX, or Yahoo Finance
            high_volume_stocks = [
                'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
                'SPY', 'QQQ', 'TQQQ', 'SQQQ', 'IWM', 'EEM', 'GLD', 'SLV',
                'AMD', 'INTC', 'CRM', 'ORCL', 'ADBE', 'PYPL', 'NFLX', 'DIS',
                'V', 'MA', 'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C',
                'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'DHR', 'ABT',
                'XOM', 'CVX', 'COP', 'SLB', 'HAL', 'OXY', 'MPC', 'VLO',
                'WMT', 'HD', 'PG', 'KO', 'PEP', 'MCD', 'NKE', 'SBUX'
            ]

            # Simulate volume data (in production, fetch real data)
            import random
            stock_data = []

            for i, symbol in enumerate(high_volume_stocks[:limit]):
                # Simulate decreasing volume for ranking
                base_volume = 1000000000 - (i * 10000000)  # $1B decreasing
                volume_24h = base_volume + random.randint(-50000000, 50000000)

                stock_data.append({
                    'symbol': symbol,
                    'base_symbol': symbol,
                    'volume_24h': max(volume_24h, 10000000),  # Min $10M
                    'price': random.uniform(50, 500),  # Simulated price
                    'change_24h': random.uniform(-5, 5),  # Simulated change
                    'type': 'stock',
                    'exchange': 'nasdaq'
                })

            self.logger.info(f"Generated {len(stock_data)} top stock assets")
            return stock_data

        except Exception as e:
            self.logger.error(f"Error getting top stock assets: {e}")
            return []

    async def _use_fallback_symbols(self):
        """Use fallback symbols if discovery fails"""
        self.crypto_symbols = [
            'BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT',
            'LINK/USDT', 'SOL/USDT', 'MATIC/USDT', 'AVAX/USDT'
        ]
        self.stock_symbols = [
            'AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA',
            'META', 'AMZN', 'NFLX', 'SPY', 'QQQ'
        ]

        # Create fallback top_assets list
        self.top_assets = []
        for symbol in self.crypto_symbols:
            self.top_assets.append({
                'symbol': symbol,
                'base_symbol': symbol.split('/')[0],
                'volume_24h': 100000000,  # Placeholder
                'type': 'crypto',
                'exchange': 'binance'
            })

        for symbol in self.stock_symbols:
            self.top_assets.append({
                'symbol': symbol,
                'base_symbol': symbol,
                'volume_24h': 50000000,  # Placeholder
                'type': 'stock',
                'exchange': 'nasdaq'
            })

        self.logger.info("Using fallback symbols for asset discovery")