"""
Complete News Data Collector
Integrates multiple news sources for comprehensive financial news coverage
"""

import asyncio
import aiohttp
import feedparser
import yfinance as yf
from newsapi import NewsApiClient
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import os
import re
from dataclasses import dataclass
from textblob import TextBlob
import requests
from bs4 import BeautifulSoup

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class NewsArticle:
    title: str
    content: str
    url: str
    source: str
    published_at: datetime
    sentiment_score: float
    relevance_score: float
    symbols: List[str]
    category: Optional[str] = None
    author: Optional[str] = None

class ComprehensiveNewsCollector:
    """Comprehensive news collector from multiple sources"""

    def __init__(self, news_api_key: str = None):
        self.news_api_key = news_api_key or os.getenv('NEWS_API_KEY')
        self.session = None

        # Initialize News API client if key is available
        self.news_api_client = None
        if self.news_api_key:
            try:
                self.news_api_client = NewsApiClient(api_key=self.news_api_key)
                logger.info("NewsAPI client initialized")
            except Exception as e:
                logger.warning(f"Could not initialize NewsAPI: {e}")

        # RSS feeds for financial news
        self.rss_feeds = {
            'yahoo_finance': 'https://feeds.finance.yahoo.com/rss/2.0/headline',
            'marketwatch': 'https://feeds.marketwatch.com/marketwatch/topstories/',
            'reuters_business': 'http://feeds.reuters.com/reuters/businessNews',
            'cnbc': 'https://search.cnbc.com/rs/search/combinedcms/view.xml?partnerId=wrss01&id=100003114',
            'bloomberg': 'https://feeds.bloomberg.com/markets/news.rss',
            'seeking_alpha': 'https://seekingalpha.com/api/sa/combined/LT.xml',
            'benzinga': 'https://feeds.benzinga.com/benzinga',
            'finviz': 'https://finviz.com/news.ashx?v=3'
        }

        # Financial keywords for relevance scoring
        self.financial_keywords = [
            'earnings', 'revenue', 'profit', 'loss', 'stock', 'share', 'trading',
            'market', 'investor', 'investment', 'portfolio', 'dividend', 'merger',
            'acquisition', 'ipo', 'sec', 'fed', 'interest rate', 'inflation',
            'gdp', 'unemployment', 'economic', 'financial', 'quarterly', 'annual'
        ]

        # Stock symbol patterns
        self.symbol_pattern = re.compile(r'\b[A-Z]{1,5}\b')

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def collect_financial_news(self, symbols: List[str] = None, hours_back: int = 24) -> List[NewsArticle]:
        """Collect financial news from multiple sources"""
        all_articles = []

        # Collect from different sources
        tasks = [
            self._collect_from_newsapi(symbols, hours_back),
            self._collect_from_rss_feeds(hours_back),
            self._collect_from_yahoo_finance(symbols or []),
            self._collect_from_finviz(symbols or [])
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        for result in results:
            if isinstance(result, list):
                all_articles.extend(result)
            elif isinstance(result, Exception):
                logger.warning(f"News collection error: {result}")

        # Remove duplicates based on URL
        unique_articles = self._remove_duplicates(all_articles)

        # Sort by published date (newest first)
        unique_articles.sort(key=lambda x: x.published_at, reverse=True)

        logger.info(f"Collected {len(unique_articles)} unique news articles")
        return unique_articles

    async def _collect_from_newsapi(self, symbols: List[str], hours_back: int) -> List[NewsArticle]:
        """Collect news from NewsAPI"""
        articles = []

        if not self.news_api_client:
            logger.warning("NewsAPI client not available")
            return articles

        try:
            # Calculate date range
            to_date = datetime.now()
            from_date = to_date - timedelta(hours=hours_back)

            # Financial news query
            financial_queries = [
                'stock market',
                'earnings report',
                'financial news',
                'trading',
                'investment'
            ]

            # Add specific symbol queries if provided
            if symbols:
                financial_queries.extend(symbols[:10])  # Limit to avoid API limits

            for query in financial_queries:
                try:
                    response = self.news_api_client.get_everything(
                        q=query,
                        sources='reuters,bloomberg,financial-times,the-wall-street-journal',
                        from_param=from_date.isoformat(),
                        to=to_date.isoformat(),
                        language='en',
                        sort_by='publishedAt',
                        page_size=20
                    )

                    if response['status'] == 'ok':
                        for article in response['articles']:
                            news_article = self._parse_newsapi_article(article)
                            if news_article:
                                articles.append(news_article)

                    # Add delay between requests
                    await asyncio.sleep(1)

                except Exception as e:
                    logger.warning(f"Error fetching NewsAPI for query '{query}': {e}")

        except Exception as e:
            logger.error(f"Error in NewsAPI collection: {e}")

        return articles

    async def _collect_from_rss_feeds(self, hours_back: int) -> List[NewsArticle]:
        """Collect news from RSS feeds"""
        articles = []

        if not self.session:
            self.session = aiohttp.ClientSession()

        for source, url in self.rss_feeds.items():
            try:
                async with self.session.get(url, timeout=30) as response:
                    if response.status == 200:
                        rss_content = await response.text()
                        feed = feedparser.parse(rss_content)

                        for entry in feed.entries:
                            news_article = self._parse_rss_entry(entry, source)
                            if news_article and self._is_recent(news_article.published_at, hours_back):
                                articles.append(news_article)

            except Exception as e:
                logger.warning(f"Error fetching RSS from {source}: {e}")

        return articles

    async def _collect_from_yahoo_finance(self, symbols: List[str]) -> List[NewsArticle]:
        """Collect news from Yahoo Finance for specific symbols"""
        articles = []

        for symbol in symbols[:20]:  # Limit to avoid rate limits
            try:
                ticker = yf.Ticker(symbol)
                news = ticker.news

                for news_item in news:
                    news_article = self._parse_yahoo_news(news_item, symbol)
                    if news_article:
                        articles.append(news_article)

                # Add delay between requests
                await asyncio.sleep(0.5)

            except Exception as e:
                logger.warning(f"Error fetching Yahoo news for {symbol}: {e}")

        return articles

    async def _collect_from_finviz(self, symbols: List[str]) -> List[NewsArticle]:
        """Collect news from Finviz"""
        articles = []

        if not self.session:
            self.session = aiohttp.ClientSession()

        try:
            # Get general financial news from Finviz
            url = "https://finviz.com/news.ashx"

            async with self.session.get(url, timeout=30) as response:
                if response.status == 200:
                    html_content = await response.text()
                    soup = BeautifulSoup(html_content, 'html.parser')

                    # Parse Finviz news table
                    news_table = soup.find('table', {'class': 'table-fixed'})
                    if news_table:
                        rows = news_table.find_all('tr')

                        for row in rows[1:]:  # Skip header
                            try:
                                cells = row.find_all('td')
                                if len(cells) >= 3:
                                    time_cell = cells[0].text.strip()
                                    title_cell = cells[1].find('a')
                                    source_cell = cells[2].text.strip()

                                    if title_cell:
                                        title = title_cell.text.strip()
                                        url = title_cell.get('href')

                                        # Parse time
                                        published_at = self._parse_finviz_time(time_cell)

                                        # Extract symbols from title
                                        symbols_in_title = self._extract_symbols_from_text(title)

                                        news_article = NewsArticle(
                                            title=title,
                                            content=title,  # Finviz doesn't provide full content
                                            url=url,
                                            source=f"Finviz_{source_cell}",
                                            published_at=published_at,
                                            sentiment_score=self._calculate_sentiment(title),
                                            relevance_score=self._calculate_relevance(title),
                                            symbols=symbols_in_title
                                        )

                                        articles.append(news_article)

                            except Exception as e:
                                logger.warning(f"Error parsing Finviz row: {e}")

        except Exception as e:
            logger.warning(f"Error fetching Finviz news: {e}")

        return articles

    def _parse_newsapi_article(self, article: Dict) -> Optional[NewsArticle]:
        """Parse NewsAPI article format"""
        try:
            title = article.get('title', '')
            content = article.get('content') or article.get('description', '')
            url = article.get('url', '')
            source = article.get('source', {}).get('name', 'Unknown')
            published_at = datetime.fromisoformat(article.get('publishedAt', '').replace('Z', '+00:00'))

            # Extract symbols from title and content
            symbols = self._extract_symbols_from_text(f"{title} {content}")

            return NewsArticle(
                title=title,
                content=content,
                url=url,
                source=f"NewsAPI_{source}",
                published_at=published_at,
                sentiment_score=self._calculate_sentiment(f"{title} {content}"),
                relevance_score=self._calculate_relevance(f"{title} {content}"),
                symbols=symbols,
                author=article.get('author')
            )

        except Exception as e:
            logger.warning(f"Error parsing NewsAPI article: {e}")
            return None

    def _parse_rss_entry(self, entry, source: str) -> Optional[NewsArticle]:
        """Parse RSS feed entry"""
        try:
            title = entry.get('title', '')
            content = entry.get('summary') or entry.get('description', '')
            url = entry.get('link', '')
            published_at = datetime(*entry.get('published_parsed')[:6])

            # Extract symbols
            symbols = self._extract_symbols_from_text(f"{title} {content}")

            return NewsArticle(
                title=title,
                content=content,
                url=url,
                source=f"RSS_{source}",
                published_at=published_at,
                sentiment_score=self._calculate_sentiment(f"{title} {content}"),
                relevance_score=self._calculate_relevance(f"{title} {content}"),
                symbols=symbols
            )

        except Exception as e:
            logger.warning(f"Error parsing RSS entry: {e}")
            return None

    def _parse_yahoo_news(self, news_item: Dict, symbol: str) -> Optional[NewsArticle]:
        """Parse Yahoo Finance news item"""
        try:
            title = news_item.get('title', '')
            url = news_item.get('link', '')
            published_at = datetime.fromtimestamp(news_item.get('providerPublishTime', 0))

            # Get content from summary or use title
            content = news_item.get('summary', title)

            return NewsArticle(
                title=title,
                content=content,
                url=url,
                source="Yahoo_Finance",
                published_at=published_at,
                sentiment_score=self._calculate_sentiment(f"{title} {content}"),
                relevance_score=self._calculate_relevance(f"{title} {content}"),
                symbols=[symbol] + self._extract_symbols_from_text(f"{title} {content}")
            )

        except Exception as e:
            logger.warning(f"Error parsing Yahoo news: {e}")
            return None

    def _extract_symbols_from_text(self, text: str) -> List[str]:
        """Extract stock symbols from text"""
        symbols = []

        # Find potential symbols using regex
        potential_symbols = self.symbol_pattern.findall(text.upper())

        # Filter out common English words and keep likely stock symbols
        common_words = {'THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HER', 'WAS', 'ONE', 'OUR', 'HAD', 'BY', 'UP', 'USE', 'HOW', 'NEW', 'GET', 'TOP', 'CEO', 'CFO', 'USD', 'EUR', 'GBP', 'JPY', 'IPO', 'ETF', 'SEC', 'FDA', 'NYSE', 'NASDAQ'}

        for symbol in potential_symbols:
            if len(symbol) >= 2 and symbol not in common_words:
                symbols.append(symbol)

        return list(set(symbols))  # Remove duplicates

    def _calculate_sentiment(self, text: str) -> float:
        """Calculate sentiment score using TextBlob"""
        try:
            blob = TextBlob(text)
            # Convert polarity from [-1, 1] to [0, 1]
            sentiment = (blob.sentiment.polarity + 1) / 2
            return sentiment
        except Exception:
            return 0.5  # Neutral sentiment as fallback

    def _calculate_relevance(self, text: str) -> float:
        """Calculate relevance score based on financial keywords"""
        try:
            text_lower = text.lower()
            keyword_count = sum(1 for keyword in self.financial_keywords if keyword in text_lower)
            # Normalize to 0-1 scale
            relevance = min(keyword_count / 5.0, 1.0)  # Max relevance at 5+ keywords
            return relevance
        except Exception:
            return 0.5  # Default relevance

    def _parse_finviz_time(self, time_str: str) -> datetime:
        """Parse Finviz time format"""
        try:
            # Finviz uses formats like "09:30AM", "Nov-27", etc.
            now = datetime.now()

            if 'AM' in time_str or 'PM' in time_str:
                # Today's time
                time_obj = datetime.strptime(time_str, '%I:%M%p')
                return now.replace(hour=time_obj.hour, minute=time_obj.minute, second=0, microsecond=0)
            else:
                # Date format
                try:
                    date_obj = datetime.strptime(f"{time_str}-{now.year}", '%b-%d-%Y')
                    return date_obj
                except:
                    return now

        except Exception:
            return datetime.now()

    def _is_recent(self, published_at: datetime, hours_back: int) -> bool:
        """Check if article is within the specified time window"""
        now = datetime.now()
        cutoff = now - timedelta(hours=hours_back)
        return published_at >= cutoff

    def _remove_duplicates(self, articles: List[NewsArticle]) -> List[NewsArticle]:
        """Remove duplicate articles based on URL and title similarity"""
        unique_articles = []
        seen_urls = set()
        seen_titles = set()

        for article in articles:
            # Check URL duplicates
            if article.url in seen_urls:
                continue

            # Check title similarity (simple approach)
            title_key = article.title[:50].lower().strip()
            if title_key in seen_titles:
                continue

            seen_urls.add(article.url)
            seen_titles.add(title_key)
            unique_articles.append(article)

        return unique_articles

class NewsDataCollector:
    """Main news data collector interface"""

    def __init__(self, news_api_key: str = None):
        self.comprehensive_collector = ComprehensiveNewsCollector(news_api_key)
        self.logger = logging.getLogger(__name__)

    async def collect_financial_news(self, symbols: List[str] = None, hours_back: int = 24) -> List[NewsArticle]:
        """Collect financial news from all sources"""
        async with self.comprehensive_collector:
            return await self.comprehensive_collector.collect_financial_news(symbols, hours_back)

    async def collect_news(self, symbols: List[str] = None) -> List[NewsArticle]:
        """Collect news for specific symbols"""
        return await self.collect_financial_news(symbols, 24)

# Test function
async def test_news_collector():
    """Test the news collector"""
    collector = NewsDataCollector()

    print("Testing News Collector...")

    # Test with some popular symbols
    test_symbols = ['AAPL', 'TSLA', 'MSFT', 'GOOGL']

    news_articles = await collector.collect_financial_news(test_symbols, hours_back=48)

    print(f"Collected {len(news_articles)} news articles")

    for i, article in enumerate(news_articles[:5]):
        print(f"\n{i+1}. {article.title}")
        print(f"   Source: {article.source}")
        print(f"   Published: {article.published_at}")
        print(f"   Sentiment: {article.sentiment_score:.2f}")
        print(f"   Relevance: {article.relevance_score:.2f}")
        print(f"   Symbols: {article.symbols}")

if __name__ == "__main__":
    asyncio.run(test_news_collector())