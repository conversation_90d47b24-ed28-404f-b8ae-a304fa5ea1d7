# 🚀 Multi-Agent Crypto Analysis System – Step-by-Step Manual  

## Part 3 – Data Cleaning, Storage & Preprocessing  

Once we fetch data (crypto prices, news, cross-assets), we need to:  
1. **Clean** the datasets (remove duplicates, handle missing values).  
2. **Normalize** the formats (timestamps, numerical scales).  
3. **Store** them in an accessible way for later analysis.  

---

### 1. Cleaning Data
Example for cleaning OHLCV crypto data:  
```python
import pandas as pd

def clean_crypto_data(df: pd.DataFrame) -> pd.DataFrame:
    df = df.drop_duplicates()
    df = df.dropna()
    df["timestamp"] = pd.to_datetime(df["timestamp"], errors="coerce")
    df = df.set_index("timestamp")
    return df
```

Example for cleaning news articles:  
```python
def clean_news_data(articles: list) -> pd.DataFrame:
    records = []
    for art in articles:
        records.append({
            "title": art.get("title"),
            "description": art.get("description"),
            "date": art.get("publishedAt")
        })
    df = pd.DataFrame(records)
    df["date"] = pd.to_datetime(df["date"], errors="coerce")
    return df.dropna()
```

---

### 2. Normalizing Data
We want consistent scales & aligned timestamps.  

```python
from sklearn.preprocessing import MinMaxScaler

def normalize_series(series):
    scaler = MinMaxScaler()
    return scaler.fit_transform(series.values.reshape(-1,1))
```

This is useful for comparing assets with different price scales (e.g., BTC vs. Gold).  

---

### 3. Storing Data
We will start simple with **CSV/Parquet files**, later upgrade to **SQLite/Postgres**.  

```python
# Save cleaned crypto data
df.to_csv("data/crypto/btc_usdt_1h.csv")

# Save news
news_df.to_csv("data/news/bitcoin_news.csv")

# Save correlations
correlation_df.to_csv("data/correlations/crypto_vs_gold.csv")
```

Later, switch to **SQLite**:  
```python
import sqlite3

conn = sqlite3.connect("data/crypto_agents.db")
df.to_sql("crypto_prices", conn, if_exists="append", index=False)
```

---

### 4. Preprocessing for Analysis
Before analysis, we should:  
- **Resample** to consistent intervals (`1m`, `5m`, `1h`, `1d`).  
- **Fill missing values** (`ffill`, interpolation).  
- **Align datasets** (crypto + news + cross-asset).  

```python
# Resample crypto prices to hourly
hourly_df = df.resample("1H").ffill()
```

---

✅ That’s **Part 3 (Data Cleaning, Storage & Preprocessing)**.  
