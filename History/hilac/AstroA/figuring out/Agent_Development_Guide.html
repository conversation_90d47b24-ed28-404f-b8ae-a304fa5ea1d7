<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Development Guide - Mathematical Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .agent-bg {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }
        .step-card {
            border-left: 4px solid #8b5cf6;
            transition: all 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .command-block {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Ubuntu Mono', 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 16px 0;
            position: relative;
            overflow-x: auto;
            border: 1px solid #8b5cf6;
        }
        .env-indicator {
            position: absolute;
            top: 5px;
            right: 60px;
            background: rgba(139, 92, 246, 0.8);
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(139, 92, 246, 0.2);
            border: 1px solid #8b5cf6;
            color: #8b5cf6;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            font-family: 'Ubuntu', sans-serif;
        }
        .copy-button:hover {
            background: rgba(139, 92, 246, 0.3);
        }
        .step-number {
            width: 50px;
            height: 50px;
            background: #8b5cf6;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 20px;
            margin-right: 20px;
            flex-shrink: 0;
            box-shadow: 0 4px 10px rgba(139, 92, 246, 0.3);
        }
        .warning-box {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .success-box {
            background: #dcfce7;
            border: 2px solid #16a34a;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .info-box {
            background: #dbeafe;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .agent-card {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #8b5cf6;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
        }
        .terminal-prompt {
            color: #8b5cf6;
            font-weight: bold;
        }
        .verification-checklist {
            background: #faf5ff;
            border: 2px solid #8b5cf6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .checklist-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
            transition: background 0.2s;
        }
        .checklist-item:hover {
            background: #f3e8ff;
        }
        .file-tree {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Ubuntu Mono', monospace;
            font-size: 14px;
            margin: 15px 0;
        }
        .agent-type-badge {
            display: inline-block;
            background: #8b5cf6;
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin: 3px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="agent-bg text-white p-4 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <i data-lucide="cpu" class="w-8 h-8"></i>
                <div>
                    <h1 class="text-xl font-bold">Agent Development Guide</h1>
                    <p class="text-sm opacity-90">Stage 2: Building the AI Trading Agents</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <i data-lucide="check-circle" class="w-5 h-5"></i>
                    <span class="text-sm">Stage 1 Complete</span>
                </div>
                <a href="#quick-start" class="bg-white/20 px-4 py-2 rounded hover:bg-white/30 transition">Start Building</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="agent-bg text-white py-20">
        <div class="max-w-7xl mx-auto text-center px-4">
            <h1 class="text-5xl font-bold mb-6">Agent Development Guide</h1>
            <p class="text-xl mb-8">Build Multi-Agent AI Trading System - Ubuntu 25.x Ready</p>
            <div class="flex justify-center space-x-8">
                <div class="bg-white/20 rounded-lg p-6">
                    <i data-lucide="brain" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Data Collection Agent</h3>
                    <p class="text-sm">Market data & news aggregation</p>
                </div>
                <div class="bg-white/20 rounded-lg p-6">
                    <i data-lucide="calculator" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Mathematical Agent</h3>
                    <p class="text-sm">Advanced mathematical analysis</p>
                </div>
                <div class="bg-white/20 rounded-lg p-6">
                    <i data-lucide="trending-up" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Trading Agent</h3>
                    <p class="text-sm">Strategy execution & risk management</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Prerequisites Check -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Pre-Development Verification</h2>

            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h3 class="text-xl font-bold mb-6">🔍 Ensure Previous Stage Complete</h3>

                <div class="command-block">
                    <div class="env-indicator">AstroA/</div>
                    <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Navigate to your project directory
cd ~/axmadcodes/AstroA

<span class="terminal-prompt">$</span> # Activate virtual environment
source venv/bin/activate

<span class="terminal-prompt">$</span> # Verify installation
python test_installation.py
                </div>

                <div class="warning-box">
                    <strong>⚠️ Important:</strong> All tests from the Linux Installation Guide must pass before proceeding. If any test fails, return to the previous stage first.
                </div>
            </div>
        </div>
    </section>

    <!-- Agent Architecture Overview -->
    <section id="quick-start" class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Multi-Agent System Architecture</h2>

            <div class="grid md:grid-cols-3 gap-8 mb-12">
                <div class="agent-card">
                    <span class="agent-type-badge">Core Agent</span>
                    <h3 class="text-xl font-bold mb-4 text-purple-700">🔍 Data Collection Agent</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Cryptocurrency market data (CCXT)</li>
                        <li>• Traditional market data (yfinance)</li>
                        <li>• News sentiment analysis</li>
                        <li>• Social media monitoring</li>
                        <li>• Real-time data streaming</li>
                    </ul>
                </div>
                <div class="agent-card">
                    <span class="agent-type-badge">Analysis Agent</span>
                    <h3 class="text-xl font-bold mb-4 text-purple-700">🧮 Mathematical Engine Agent</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Statistical analysis (SciPy)</li>
                        <li>• Pattern recognition</li>
                        <li>• Correlation analysis</li>
                        <li>• Risk calculations</li>
                        <li>• Mathematical modeling</li>
                    </ul>
                </div>
                <div class="agent-card">
                    <span class="agent-type-badge">Strategy Agent</span>
                    <h3 class="text-xl font-bold mb-4 text-purple-700">📈 Trading Strategy Agent</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Portfolio optimization</li>
                        <li>• Risk management</li>
                        <li>• Position sizing</li>
                        <li>• Trade execution logic</li>
                        <li>• Performance monitoring</li>
                    </ul>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <h3 class="text-xl font-bold mb-6">🏗️ Agent Communication Flow</h3>
                <div class="file-tree">
Data Collection Agent → Redis Cache → Mathematical Engine Agent
                          ↓                           ↓
                    PostgreSQL Database ← Trading Strategy Agent
                          ↓                           ↓
                    Web Dashboard ← Performance Reports ←
                </div>
            </div>
        </div>
    </section>

    <!-- Step 1: Project Structure Setup -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Agent Development Steps</h2>

            <!-- Step 1: Enhanced Project Structure -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">1</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Create Agent-Specific Project Structure</h3>

                        <div class="command-block">
                            <div class="env-indicator">AstroA/</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Create detailed agent directories
mkdir -p agents/{data_collector,mathematical_engine,trading_strategy,communication}
mkdir -p agents/data_collector/{collectors,processors,validators}
mkdir -p agents/mathematical_engine/{analyzers,calculators,models}
mkdir -p agents/trading_strategy/{strategies,risk_management,portfolio}
mkdir -p agents/communication/{protocols,messaging,coordination}

<span class="terminal-prompt">$</span> # Create shared utilities
mkdir -p shared/{utils,config,exceptions,types}

<span class="terminal-prompt">$</span> # Create agent-specific tests
mkdir -p tests/agents/{data_collector,mathematical_engine,trading_strategy}

<span class="terminal-prompt">$</span> # Create monitoring and logging
mkdir -p monitoring/{agents,performance,alerts}
mkdir -p logs/{agents,system,errors}
                        </div>

                        <div class="command-block">
                            <div class="env-indicator">AstroA/</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Create Python package files
find agents/ -type d -exec touch {}/__init__.py \;
find shared/ -type d -exec touch {}/__init__.py \;
find monitoring/ -type d -exec touch {}/__init__.py \;

<span class="terminal-prompt">$</span> # Display the enhanced structure
tree agents/ shared/ monitoring/ -I '__pycache__|*.pyc'
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All agent directories created</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Python package files in place</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Project structure displays correctly</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Base Agent Framework -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">2</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Build Base Agent Framework</h3>

                        <h4 class="font-semibold mb-3">Create Base Agent Class</h4>
                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create base agent framework
cat > shared/types/agent_types.py << 'EOF'
"""
Base types and enums for the Multi-Agent Trading System
"""
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import uuid

class AgentType(Enum):
    DATA_COLLECTOR = "data_collector"
    MATHEMATICAL_ENGINE = "mathematical_engine"
    TRADING_STRATEGY = "trading_strategy"
    COMMUNICATION = "communication"

class AgentStatus(Enum):
    IDLE = "idle"
    RUNNING = "running"
    ERROR = "error"
    STOPPED = "stopped"

class MessageType(Enum):
    DATA_REQUEST = "data_request"
    DATA_RESPONSE = "data_response"
    ANALYSIS_REQUEST = "analysis_request"
    ANALYSIS_RESPONSE = "analysis_response"
    TRADE_SIGNAL = "trade_signal"
    ALERT = "alert"
    HEARTBEAT = "heartbeat"

@dataclass
class AgentMessage:
    id: str
    sender_id: str
    receiver_id: str
    message_type: MessageType
    payload: Dict[str, Any]
    timestamp: datetime
    priority: int = 1

@dataclass
class MarketData:
    symbol: str
    timestamp: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    exchange: str
    timeframe: str

@dataclass
class NewsData:
    title: str
    content: str
    url: str
    source: str
    published_at: datetime
    sentiment_score: Optional[float] = None
    relevance_score: Optional[float] = None
    symbols: List[str] = None

@dataclass
class AnalysisResult:
    symbol: str
    timestamp: datetime
    analysis_type: str
    result: Dict[str, Any]
    confidence: float
    metadata: Dict[str, Any]
EOF
                        </div>

                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create base agent class
cat > shared/utils/base_agent.py << 'EOF'
"""
Base Agent class for the Multi-Agent Trading System
"""
import asyncio
import logging
import redis
import json
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime
import uuid

from shared.types.agent_types import (
    AgentType, AgentStatus, MessageType, AgentMessage
)

class BaseAgent(ABC):
    """Base class for all trading system agents"""

    def __init__(
        self,
        agent_id: str,
        agent_type: AgentType,
        redis_host: str = "localhost",
        redis_port: int = 6379,
        redis_db: int = 0
    ):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.status = AgentStatus.IDLE
        self.created_at = datetime.now()
        self.last_heartbeat = datetime.now()

        # Setup logging
        self.logger = self._setup_logging()

        # Setup Redis connection for inter-agent communication
        self.redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            decode_responses=True
        )

        # Message handlers
        self.message_handlers: Dict[MessageType, Callable] = {}
        self._register_default_handlers()

        # Running flag
        self.running = False

        self.logger.info(f"Agent {self.agent_id} ({self.agent_type.value}) initialized")

    def _setup_logging(self) -> logging.Logger:
        """Setup agent-specific logging"""
        logger = logging.getLogger(f"agent.{self.agent_id}")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.FileHandler(f"logs/agents/{self.agent_id}.log")
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _register_default_handlers(self):
        """Register default message handlers"""
        self.message_handlers[MessageType.HEARTBEAT] = self._handle_heartbeat

    async def _handle_heartbeat(self, message: AgentMessage):
        """Handle heartbeat messages"""
        self.last_heartbeat = datetime.now()
        self.logger.debug(f"Heartbeat received from {message.sender_id}")

    def register_handler(self, message_type: MessageType, handler: Callable):
        """Register a message handler for a specific message type"""
        self.message_handlers[message_type] = handler
        self.logger.info(f"Registered handler for {message_type.value}")

    async def send_message(self, receiver_id: str, message_type: MessageType, payload: Dict[str, Any], priority: int = 1):
        """Send a message to another agent"""
        message = AgentMessage(
            id=str(uuid.uuid4()),
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            payload=payload,
            timestamp=datetime.now(),
            priority=priority
        )

        channel = f"agent:{receiver_id}:messages"
        message_data = {
            "id": message.id,
            "sender_id": message.sender_id,
            "receiver_id": message.receiver_id,
            "message_type": message.message_type.value,
            "payload": message.payload,
            "timestamp": message.timestamp.isoformat(),
            "priority": message.priority
        }

        self.redis_client.lpush(channel, json.dumps(message_data))
        self.logger.debug(f"Sent {message_type.value} message to {receiver_id}")

    async def receive_messages(self) -> List[AgentMessage]:
        """Receive messages from the message queue"""
        channel = f"agent:{self.agent_id}:messages"
        messages = []

        while True:
            message_data = self.redis_client.brpop(channel, timeout=1)
            if not message_data:
                break

            try:
                data = json.loads(message_data[1])
                message = AgentMessage(
                    id=data["id"],
                    sender_id=data["sender_id"],
                    receiver_id=data["receiver_id"],
                    message_type=MessageType(data["message_type"]),
                    payload=data["payload"],
                    timestamp=datetime.fromisoformat(data["timestamp"]),
                    priority=data["priority"]
                )
                messages.append(message)
            except Exception as e:
                self.logger.error(f"Error parsing message: {e}")

        return messages

    async def process_messages(self):
        """Process incoming messages"""
        messages = await self.receive_messages()

        for message in messages:
            if message.message_type in self.message_handlers:
                try:
                    await self.message_handlers[message.message_type](message)
                except Exception as e:
                    self.logger.error(f"Error handling {message.message_type.value}: {e}")
            else:
                self.logger.warning(f"No handler for message type: {message.message_type.value}")

    async def start(self):
        """Start the agent"""
        self.running = True
        self.status = AgentStatus.RUNNING
        self.logger.info(f"Agent {self.agent_id} starting...")

        try:
            await self.initialize()

            while self.running:
                await self.process_messages()
                await self.execute_main_logic()
                await asyncio.sleep(1)  # Prevent tight loop

        except Exception as e:
            self.logger.error(f"Agent error: {e}")
            self.status = AgentStatus.ERROR
        finally:
            await self.cleanup()
            self.status = AgentStatus.STOPPED
            self.logger.info(f"Agent {self.agent_id} stopped")

    async def stop(self):
        """Stop the agent"""
        self.logger.info(f"Agent {self.agent_id} stopping...")
        self.running = False

    @abstractmethod
    async def initialize(self):
        """Initialize agent-specific resources"""
        pass

    @abstractmethod
    async def execute_main_logic(self):
        """Execute the main agent logic"""
        pass

    @abstractmethod
    async def cleanup(self):
        """Cleanup agent-specific resources"""
        pass

    def get_status(self) -> Dict[str, Any]:
        """Get agent status information"""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type.value,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "last_heartbeat": self.last_heartbeat.isoformat(),
            "running": self.running
        }
EOF
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Base agent types created</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Base agent class implemented</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>No syntax errors in Python files</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Data Collection Agent -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">3</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Build Data Collection Agent</h3>

                        <div class="info-box">
                            <strong>📋 Purpose:</strong> This agent collects market data from multiple sources (cryptocurrency exchanges, traditional markets, news) and stores it in the database for analysis.
                        </div>

                        <h4 class="font-semibold mb-3">Market Data Collector</h4>
                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create market data collector
cat > agents/data_collector/collectors/market_collector.py << 'EOF'
"""
Market Data Collector for Cryptocurrency and Traditional Markets
"""
import asyncio
import ccxt
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

from shared.types.agent_types import MarketData

class MarketDataCollector:
    """Collects market data from various sources"""

    def __init__(self):
        self.logger = logging.getLogger("market_collector")
        self.exchanges = self._initialize_exchanges()
        self.crypto_symbols = [
            'BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT',
            'LINK/USDT', 'SOL/USDT', 'MATIC/USDT', 'AVAX/USDT'
        ]
        self.stock_symbols = [
            'AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA',
            'META', 'AMZN', 'NFLX', 'SPY', 'QQQ'
        ]

    def _initialize_exchanges(self) -> Dict:
        """Initialize cryptocurrency exchanges"""
        exchanges = {}

        try:
            # Binance (most liquid crypto exchange)
            exchanges['binance'] = ccxt.binance({
                'apiKey': '',  # Add from .env if needed
                'secret': '',  # Add from .env if needed
                'sandbox': False,
                'rateLimit': 1200,  # Binance rate limit
            })

            # Coinbase Pro
            exchanges['coinbasepro'] = ccxt.coinbasepro({
                'apiKey': '',
                'secret': '',
                'passphrase': '',
                'sandbox': False,
                'rateLimit': 1000,
            })

        except Exception as e:
            self.logger.error(f"Error initializing exchanges: {e}")

        return exchanges

    async def collect_crypto_data(self, timeframe: str = '1h', limit: int = 100) -> List[MarketData]:
        """Collect cryptocurrency market data"""
        market_data = []

        for exchange_name, exchange in self.exchanges.items():
            for symbol in self.crypto_symbols:
                try:
                    # Fetch OHLCV data
                    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)

                    for candle in ohlcv:
                        data = MarketData(
                            symbol=symbol.replace('/', ''),
                            timestamp=datetime.fromtimestamp(candle[0] / 1000),
                            open_price=candle[1],
                            high_price=candle[2],
                            low_price=candle[3],
                            close_price=candle[4],
                            volume=candle[5],
                            exchange=exchange_name,
                            timeframe=timeframe
                        )
                        market_data.append(data)

                    self.logger.info(f"Collected {len(ohlcv)} candles for {symbol} from {exchange_name}")

                except Exception as e:
                    self.logger.error(f"Error collecting {symbol} from {exchange_name}: {e}")
                    continue

        return market_data

    async def collect_stock_data(self, period: str = '1d', interval: str = '1h') -> List[MarketData]:
        """Collect traditional stock market data"""
        market_data = []

        for symbol in self.stock_symbols:
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period=period, interval=interval)

                for timestamp, row in hist.iterrows():
                    data = MarketData(
                        symbol=symbol,
                        timestamp=timestamp.to_pydatetime(),
                        open_price=row['Open'],
                        high_price=row['High'],
                        low_price=row['Low'],
                        close_price=row['Close'],
                        volume=row['Volume'],
                        exchange='yahoo_finance',
                        timeframe=interval
                    )
                    market_data.append(data)

                self.logger.info(f"Collected {len(hist)} data points for {symbol}")

            except Exception as e:
                self.logger.error(f"Error collecting {symbol}: {e}")
                continue

        return market_data

    async def collect_all_market_data(self) -> List[MarketData]:
        """Collect data from all sources"""
        self.logger.info("Starting market data collection...")

        # Collect crypto and stock data concurrently
        crypto_task = asyncio.create_task(self.collect_crypto_data())
        stock_task = asyncio.create_task(self.collect_stock_data())

        crypto_data, stock_data = await asyncio.gather(crypto_task, stock_task)

        all_data = crypto_data + stock_data
        self.logger.info(f"Collected total {len(all_data)} market data points")

        return all_data
EOF
                        </div>

                        <h4 class="font-semibold mb-3 mt-6">News Data Collector</h4>
                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create news data collector
cat > agents/data_collector/collectors/news_collector.py << 'EOF'
"""
News Data Collector for Market Sentiment Analysis
"""
import requests
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
from textblob import TextBlob

from shared.types.agent_types import NewsData

class NewsDataCollector:
    """Collects financial news from various sources"""

    def __init__(self, news_api_key: Optional[str] = None):
        self.logger = logging.getLogger("news_collector")
        self.news_api_key = news_api_key
        self.base_url = "https://newsapi.org/v2"

        # Financial keywords for relevance scoring
        self.financial_keywords = [
            'cryptocurrency', 'bitcoin', 'ethereum', 'trading', 'market',
            'stock', 'investment', 'finance', 'economy', 'monetary',
            'federal reserve', 'inflation', 'recession', 'bull market',
            'bear market', 'volatility', 'portfolio', 'dividend'
        ]

    def calculate_sentiment(self, text: str) -> float:
        """Calculate sentiment score using TextBlob"""
        try:
            blob = TextBlob(text)
            return blob.sentiment.polarity  # Returns -1 to 1
        except Exception as e:
            self.logger.error(f"Error calculating sentiment: {e}")
            return 0.0

    def calculate_relevance(self, text: str) -> float:
        """Calculate relevance score based on financial keywords"""
        text_lower = text.lower()
        matches = sum(1 for keyword in self.financial_keywords if keyword in text_lower)
        return min(matches / len(self.financial_keywords), 1.0)

    async def collect_financial_news(self, days_back: int = 1, page_size: int = 50) -> List[NewsData]:
        """Collect financial news from NewsAPI"""
        if not self.news_api_key:
            self.logger.warning("No NewsAPI key provided, skipping news collection")
            return []

        news_data = []
        from_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')

        queries = [
            'cryptocurrency AND trading',
            'bitcoin OR ethereum',
            'stock market',
            'financial markets',
            'trading signals'
        ]

        for query in queries:
            try:
                url = f"{self.base_url}/everything"
                params = {
                    'q': query,
                    'from': from_date,
                    'sortBy': 'publishedAt',
                    'pageSize': page_size,
                    'language': 'en',
                    'apiKey': self.news_api_key
                }

                response = requests.get(url, params=params)
                response.raise_for_status()

                data = response.json()
                articles = data.get('articles', [])

                for article in articles:
                    title = article.get('title', '')
                    content = article.get('content', '') or article.get('description', '')

                    if not title or not content:
                        continue

                    # Calculate sentiment and relevance
                    full_text = f"{title} {content}"
                    sentiment_score = self.calculate_sentiment(full_text)
                    relevance_score = self.calculate_relevance(full_text)

                    # Extract potential symbols (simple pattern matching)
                    symbols = self._extract_symbols(full_text)

                    news_item = NewsData(
                        title=title,
                        content=content,
                        url=article.get('url', ''),
                        source=article.get('source', {}).get('name', 'Unknown'),
                        published_at=datetime.fromisoformat(
                            article.get('publishedAt', '').replace('Z', '+00:00')
                        ),
                        sentiment_score=sentiment_score,
                        relevance_score=relevance_score,
                        symbols=symbols
                    )

                    news_data.append(news_item)

                self.logger.info(f"Collected {len(articles)} articles for query: {query}")

            except Exception as e:
                self.logger.error(f"Error collecting news for query '{query}': {e}")
                continue

        # Remove duplicates based on URL
        unique_news = {item.url: item for item in news_data}.values()

        self.logger.info(f"Collected {len(unique_news)} unique news articles")
        return list(unique_news)

    def _extract_symbols(self, text: str) -> List[str]:
        """Extract potential trading symbols from text"""
        text_upper = text.upper()
        symbols = []

        # Common crypto symbols
        crypto_symbols = ['BTC', 'ETH', 'ADA', 'DOT', 'LINK', 'SOL', 'MATIC', 'AVAX']
        # Common stock symbols
        stock_symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA', 'META', 'AMZN', 'NFLX']

        all_symbols = crypto_symbols + stock_symbols

        for symbol in all_symbols:
            if symbol in text_upper:
                symbols.append(symbol)

        return list(set(symbols))  # Remove duplicates
EOF
                        </div>

                        <h4 class="font-semibold mb-3 mt-6">Data Collection Agent Implementation</h4>
                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create the main data collection agent
cat > agents/data_collector/data_collection_agent.py << 'EOF'
"""
Data Collection Agent - Main coordinator for data gathering
"""
import asyncio
import psycopg2
from datetime import datetime
from typing import List, Dict, Any
import os
from dotenv import load_dotenv

from shared.utils.base_agent import BaseAgent
from shared.types.agent_types import (
    AgentType, MessageType, MarketData, NewsData
)
from agents.data_collector.collectors.market_collector import MarketDataCollector
from agents.data_collector.collectors.news_collector import NewsDataCollector

load_dotenv()

class DataCollectionAgent(BaseAgent):
    """Agent responsible for collecting market data and news"""

    def __init__(self, agent_id: str = "data_collector_001"):
        super().__init__(agent_id, AgentType.DATA_COLLECTOR)

        # Initialize collectors
        self.market_collector = MarketDataCollector()
        self.news_collector = NewsDataCollector(
            news_api_key=os.getenv('NEWS_API_KEY')
        )

        # Database connection
        self.db_connection = None

        # Collection intervals (in seconds)
        self.market_data_interval = 3600  # 1 hour
        self.news_data_interval = 7200   # 2 hours

        # Last collection times
        self.last_market_collection = None
        self.last_news_collection = None

    async def initialize(self):
        """Initialize database connection and collectors"""
        self.logger.info("Initializing Data Collection Agent...")

        # Setup database connection
        try:
            self.db_connection = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'mathematical_trading'),
                user=os.getenv('DB_USER', 'trading_user'),
                password=os.getenv('DB_PASSWORD', 'secure_password_123')
            )
            self.logger.info("Database connection established")
        except Exception as e:
            self.logger.error(f"Failed to connect to database: {e}")
            raise

        self.logger.info("Data Collection Agent initialized successfully")

    async def execute_main_logic(self):
        """Main execution logic for data collection"""
        current_time = datetime.now()

        # Check if it's time to collect market data
        if (self.last_market_collection is None or
            (current_time - self.last_market_collection).seconds >= self.market_data_interval):

            await self.collect_and_store_market_data()
            self.last_market_collection = current_time

        # Check if it's time to collect news data
        if (self.last_news_collection is None or
            (current_time - self.last_news_collection).seconds >= self.news_data_interval):

            await self.collect_and_store_news_data()
            self.last_news_collection = current_time

    async def collect_and_store_market_data(self):
        """Collect and store market data"""
        try:
            self.logger.info("Starting market data collection...")
            market_data = await self.market_collector.collect_all_market_data()

            if market_data:
                await self.store_market_data(market_data)

                # Notify other agents about new data
                await self.send_message(
                    receiver_id="mathematical_engine_001",
                    message_type=MessageType.DATA_RESPONSE,
                    payload={
                        "data_type": "market_data",
                        "count": len(market_data),
                        "timestamp": datetime.now().isoformat(),
                        "symbols": list(set([data.symbol for data in market_data]))
                    }
                )

                self.logger.info(f"Collected and stored {len(market_data)} market data points")

        except Exception as e:
            self.logger.error(f"Error in market data collection: {e}")

    async def collect_and_store_news_data(self):
        """Collect and store news data"""
        try:
            self.logger.info("Starting news data collection...")
            news_data = await self.news_collector.collect_financial_news()

            if news_data:
                await self.store_news_data(news_data)

                # Notify other agents about new news
                await self.send_message(
                    receiver_id="mathematical_engine_001",
                    message_type=MessageType.DATA_RESPONSE,
                    payload={
                        "data_type": "news_data",
                        "count": len(news_data),
                        "timestamp": datetime.now().isoformat(),
                        "average_sentiment": sum([n.sentiment_score or 0 for n in news_data]) / len(news_data)
                    }
                )

                self.logger.info(f"Collected and stored {len(news_data)} news articles")

        except Exception as e:
            self.logger.error(f"Error in news data collection: {e}")

    async def store_market_data(self, market_data: List[MarketData]):
        """Store market data in database"""
        cursor = self.db_connection.cursor()

        try:
            for data in market_data:
                cursor.execute("""
                    INSERT INTO market_data
                    (symbol, timestamp, open_price, high_price, low_price, close_price, volume, exchange, timeframe)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (symbol, timestamp, exchange, timeframe) DO NOTHING
                """, (
                    data.symbol, data.timestamp, data.open_price, data.high_price,
                    data.low_price, data.close_price, data.volume, data.exchange, data.timeframe
                ))

            self.db_connection.commit()
            self.logger.info(f"Stored {len(market_data)} market data records")

        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error storing market data: {e}")
            raise
        finally:
            cursor.close()

    async def store_news_data(self, news_data: List[NewsData]):
        """Store news data in database"""
        cursor = self.db_connection.cursor()

        try:
            for data in news_data:
                # Convert symbols list to JSON string
                symbols_json = ','.join(data.symbols) if data.symbols else None

                cursor.execute("""
                    INSERT INTO news_data
                    (title, content, url, source, published_at, sentiment_score, relevance_score, symbol)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (url) DO NOTHING
                """, (
                    data.title, data.content, data.url, data.source,
                    data.published_at, data.sentiment_score, data.relevance_score, symbols_json
                ))

            self.db_connection.commit()
            self.logger.info(f"Stored {len(news_data)} news records")

        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error storing news data: {e}")
            raise
        finally:
            cursor.close()

    async def cleanup(self):
        """Cleanup resources"""
        if self.db_connection:
            self.db_connection.close()
            self.logger.info("Database connection closed")
EOF
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Market data collector created</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>News data collector created</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Main data collection agent implemented</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Test Data Collection Agent -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">4</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Test Data Collection Agent</h3>

                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create test script for data collection agent
cat > test_data_collection.py << 'EOF'
#!/usr/bin/env python3
"""
Test script for Data Collection Agent
"""
import asyncio
import sys
import os
sys.path.append(os.getcwd())

from agents.data_collector.data_collection_agent import DataCollectionAgent

async def test_data_collection():
    """Test the data collection agent"""
    print("🚀 Testing Data Collection Agent")
    print("=" * 50)

    # Create agent
    agent = DataCollectionAgent()

    try:
        # Initialize agent
        print("📋 Initializing agent...")
        await agent.initialize()
        print("✅ Agent initialized successfully")

        # Test market data collection
        print("\n📊 Testing market data collection...")
        await agent.collect_and_store_market_data()
        print("✅ Market data collection completed")

        # Test news data collection (only if API key is available)
        print("\n📰 Testing news data collection...")
        if os.getenv('NEWS_API_KEY'):
            await agent.collect_and_store_news_data()
            print("✅ News data collection completed")
        else:
            print("⚠️ NEWS_API_KEY not found, skipping news collection")

        # Check agent status
        status = agent.get_status()
        print(f"\n📊 Agent Status:")
        print(f"  Agent ID: {status['agent_id']}")
        print(f"  Type: {status['agent_type']}")
        print(f"  Status: {status['status']}")
        print(f"  Running: {status['running']}")

        print("\n🎉 Data Collection Agent test completed successfully!")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False
    finally:
        await agent.cleanup()

    return True

if __name__ == "__main__":
    result = asyncio.run(test_data_collection())
    sys.exit(0 if result else 1)
EOF

<span class="terminal-prompt">(venv) $</span> # Make test script executable
chmod +x test_data_collection.py

<span class="terminal-prompt">(venv) $</span> # Run the test
python test_data_collection.py
                        </div>

                        <div class="success-box">
                            <h4 class="font-bold text-green-800 mb-2">🎯 Expected Output</h4>
                            <p class="text-green-700">The test should successfully collect market data from crypto exchanges and stock markets, store them in PostgreSQL, and show agent status information.</p>
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Agent initializes without errors</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Market data collection works</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Data is stored in database</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Agent status shows correctly</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Next Steps -->
    <section class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Next Development Phase</h2>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <h3 class="text-xl font-bold mb-6">🧮 Coming Next: Mathematical Engine Agent</h3>

                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h4 class="font-semibold mb-4">Mathematical Engine Agent Features:</h4>
                        <ul class="space-y-2 text-sm">
                            <li>• Statistical analysis of market data</li>
                            <li>• Correlation analysis between assets</li>
                            <li>• Pattern recognition algorithms</li>
                            <li>• Risk calculation engines</li>
                            <li>• Mathematical modeling systems</li>
                            <li>• Feature engineering pipelines</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-4">Current Completion Status:</h4>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <span class="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
                                <span class="text-sm">✅ System Setup Complete</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
                                <span class="text-sm">✅ Base Agent Framework</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
                                <span class="text-sm">✅ Data Collection Agent</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-4 h-4 bg-blue-500 rounded-full mr-3"></span>
                                <span class="text-sm">🔄 Mathematical Engine Agent (Next)</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-4 h-4 bg-gray-400 rounded-full mr-3"></span>
                                <span class="text-sm">⏳ Trading Strategy Agent</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-8">
                    <h4 class="font-semibold mb-3">🚀 Ready to Continue?</h4>
                    <p class="text-gray-600 mb-4">Once your Data Collection Agent is working correctly, you can proceed to build the Mathematical Engine Agent that will analyze the collected data and generate trading insights.</p>

                    <div class="command-block text-sm">
<span class="terminal-prompt">$</span> # Verify data collection is working
python test_data_collection.py

<span class="terminal-prompt">$</span> # Check database has data
psql -h localhost -U trading_user -d mathematical_trading -c "SELECT COUNT(*) FROM market_data;"

<span class="terminal-prompt">$</span> # Ready for next stage!
echo "Data Collection Agent Complete - Ready for Mathematical Engine!"
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="agent-bg text-white py-8">
        <div class="max-w-7xl mx-auto text-center px-4">
            <h3 class="text-xl font-bold mb-4">🎯 Data Collection Agent Complete!</h3>
            <p class="mb-6">Your data collection infrastructure is ready. Time to build the Mathematical Engine!</p>
            <div class="flex justify-center space-x-4">
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="brain" class="w-4 h-4 inline mr-2"></i>
                    Next: Mathematical Engine
                </button>
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="database" class="w-4 h-4 inline mr-2"></i>
                    Check Database
                </button>
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="activity" class="w-4 h-4 inline mr-2"></i>
                    Monitor Agents
                </button>
            </div>
            <p class="text-sm mt-6 opacity-75">© 2025 Mathematical Trading System - Agent Development Guide</p>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Copy to clipboard function
        function copyToClipboard(button) {
            const codeBlock = button.parentElement;
            const code = codeBlock.textContent.replace('Copy', '').trim();

            navigator.clipboard.writeText(code).then(() => {
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = 'rgba(139, 92, 246, 0.4)';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'rgba(139, 92, 246, 0.2)';
                }, 2000);
            });
        }

        // Progress tracking
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateProgress();
            });
        });

        function updateProgress() {
            const totalCheckboxes = document.querySelectorAll('input[type="checkbox"]').length;
            const checkedBoxes = document.querySelectorAll('input[type="checkbox"]:checked').length;
            const progress = (checkedBoxes / totalCheckboxes) * 100;

            document.title = `Agent Development Guide - ${progress.toFixed(0)}% Complete`;
        }

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        updateProgress();
    </script>
</body>
</html>