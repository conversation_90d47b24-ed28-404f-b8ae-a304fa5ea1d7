<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Brokers API Guide - AstroA Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .code-block {
            background-color: #1a1a1a;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
        }
        .step-number {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white py-8">
        <div class="container mx-auto px-6">
            <div class="flex items-center space-x-4">
                <i data-lucide="building" class="w-8 h-8"></i>
                <div>
                    <h1 class="text-3xl font-bold">Interactive Brokers API Guide</h1>
                    <p class="text-blue-100 mt-2">Professional Trading Infrastructure & Advanced Order Management for AstroA</p>
                </div>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-6 py-8">
        <!-- Overview -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <i data-lucide="info" class="w-6 h-6 mr-2 text-blue-600"></i>
                Interactive Brokers Integration Overview
            </h2>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold mb-2">Professional Features:</h3>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-500"></i>Direct market access (DMA)</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-500"></i>Global markets (stocks, forex, futures, options)</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-500"></i>Advanced order types</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-500"></i>Real-time market data</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-500"></i>Portfolio margin</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-500"></i>Institutional-grade execution</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">API Capabilities:</h3>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center"><i data-lucide="zap" class="w-4 h-4 mr-2 text-blue-500"></i>TWS API & IB Gateway</li>
                        <li class="flex items-center"><i data-lucide="database" class="w-4 h-4 mr-2 text-blue-500"></i>Real-time & historical data</li>
                        <li class="flex items-center"><i data-lucide="cpu" class="w-4 h-4 mr-2 text-blue-500"></i>Algorithmic trading support</li>
                        <li class="flex items-center"><i data-lucide="shield" class="w-4 h-4 mr-2 text-blue-500"></i>Risk management tools</li>
                        <li class="flex items-center"><i data-lucide="activity" class="w-4 h-4 mr-2 text-blue-500"></i>Portfolio analytics</li>
                        <li class="flex items-center"><i data-lucide="globe" class="w-4 h-4 mr-2 text-blue-500"></i>Multi-currency support</li>
                    </ul>
                </div>
            </div>
            <div class="bg-blue-50 p-4 rounded-lg mt-4">
                <p class="text-blue-800"><strong>Note:</strong> Interactive Brokers requires minimum account balance ($10,000 for margin accounts) and charges $10/month if account value is below $100,000. Professional API access provides institutional-grade trading capabilities.</p>
            </div>
        </div>

        <!-- Step 1: Account Setup -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">1</div>
                <h2 class="text-xl font-bold">Interactive Brokers Account Setup</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-2">Account Requirements:</h3>
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <ul class="text-yellow-800 space-y-1">
                            <li>• <strong>Individual/Entity:</strong> Personal or business account</li>
                            <li>• <strong>Minimum Deposit:</strong> $10,000 for margin accounts</li>
                            <li>• <strong>Trading Permissions:</strong> Enable API trading</li>
                            <li>• <strong>Market Data:</strong> Subscribe to required exchanges</li>
                        </ul>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Enable API Trading:</h3>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>Log into your Interactive Brokers account portal</li>
                        <li>Go to <strong>Settings → API → Settings</strong></li>
                        <li>Enable "Enable ActiveX and Socket Clients"</li>
                        <li>Set "Socket Port" to 7497 (Paper) or 7496 (Live)</li>
                        <li>Enable "Download open orders on connection"</li>
                        <li>Set "Master API client ID" (optional)</li>
                        <li>Save settings</li>
                    </ol>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Download TWS/IB Gateway:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Download IB Gateway (headless) - recommended for servers
cd /tmp
wget https://download2.interactivebrokers.com/installers/ibgateway/latest-standalone/ibgateway-latest-standalone-linux-x64.sh

# Make executable and install
chmod +x ibgateway-latest-standalone-linux-x64.sh
sudo ./ibgateway-latest-standalone-linux-x64.sh

# Or download TWS (full client)
# wget https://download2.interactivebrokers.com/installers/tws/latest-standalone/tws-latest-standalone-linux-x64.sh</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 2: Python API Setup -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">2</div>
                <h2 class="text-xl font-bold">Install & Configure IB Python API</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-3">Install IB API:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Install IB API from PyPI
pip install ibapi

# Or install from source for latest features
cd /tmp
git clone https://github.com/InteractiveBrokers/tws-api.git
cd tws-api/source/pythonclient
python setup.py install

# Install additional dependencies
pip install pandas numpy threading queue datetime</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Create IB API Wrapper for AstroA:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create IB integration directory
mkdir -p integrations/interactive_brokers
cd integrations/interactive_brokers

# Create base IB wrapper
cat > ib_wrapper.py << 'EOF'
import sys
import threading
import time
from datetime import datetime, timedelta
from queue import Queue
import logging

sys.path.append('/home/<USER>/axmadcodes/AstroA')

from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.order import Order
from ibapi.common import TickerId, OrderId
from ibapi.ticktype import TickType

class AstroAIBWrapper(EWrapper):
    """IB API Wrapper customized for AstroA trading system"""

    def __init__(self):
        EWrapper.__init__(self)
        self.data_queue = Queue()
        self.order_queue = Queue()
        self.position_queue = Queue()
        self.account_queue = Queue()
        self.next_order_id = None
        self.positions = {}
        self.account_data = {}
        self.market_data = {}

    def error(self, reqId: TickerId, errorCode: int, errorString: str, advancedOrderReject=""):
        """Handle API errors"""
        error_msg = f"Error {errorCode}: {errorString}"
        if reqId > -1:
            error_msg = f"Request {reqId} - {error_msg}"

        logging.error(error_msg)

        # Critical errors that should stop trading
        critical_errors = [502, 503, 504, 1100, 2104, 2106, 2108]
        if errorCode in critical_errors:
            self.data_queue.put(('error', {'critical': True, 'code': errorCode, 'message': errorString}))
        else:
            self.data_queue.put(('error', {'critical': False, 'code': errorCode, 'message': errorString}))

    def nextValidId(self, orderId: OrderId):
        """Receive next valid order ID"""
        self.next_order_id = orderId
        logging.info(f"Next valid order ID: {orderId}")

    def orderStatus(self, orderId: OrderId, status: str, filled: float, remaining: float,
                    avgFillPrice: float, permId: int, parentId: int, lastFillPrice: float,
                    clientId: int, whyHeld: str, mktCapPrice: float):
        """Order status updates"""
        order_data = {
            'order_id': orderId,
            'status': status,
            'filled': filled,
            'remaining': remaining,
            'avg_fill_price': avgFillPrice,
            'last_fill_price': lastFillPrice,
            'timestamp': datetime.now()
        }

        self.order_queue.put(order_data)
        logging.info(f"Order {orderId} status: {status}, filled: {filled}")

    def openOrder(self, orderId: OrderId, contract: Contract, order: Order, orderState):
        """Open order details"""
        order_data = {
            'order_id': orderId,
            'symbol': contract.symbol,
            'action': order.action,
            'quantity': order.totalQuantity,
            'order_type': order.orderType,
            'limit_price': order.lmtPrice,
            'aux_price': order.auxPrice
        }

        self.order_queue.put(order_data)

    def position(self, account: str, contract: Contract, position: float, avgCost: float):
        """Position updates"""
        symbol = contract.symbol
        self.positions[symbol] = {
            'position': position,
            'avg_cost': avgCost,
            'market_value': position * avgCost,
            'contract': contract,
            'timestamp': datetime.now()
        }

        self.position_queue.put(self.positions[symbol])

    def accountSummary(self, reqId: int, account: str, tag: str, value: str, currency: str):
        """Account summary data"""
        self.account_data[tag] = {
            'value': value,
            'currency': currency,
            'timestamp': datetime.now()
        }

        self.account_queue.put({tag: self.account_data[tag]})

    def tickPrice(self, reqId: TickerId, tickType: TickType, price: float, attrib):
        """Market data price ticks"""
        if reqId not in self.market_data:
            self.market_data[reqId] = {}

        if tickType == TickType.LAST:
            self.market_data[reqId]['last_price'] = price
        elif tickType == TickType.BID:
            self.market_data[reqId]['bid_price'] = price
        elif tickType == TickType.ASK:
            self.market_data[reqId]['ask_price'] = price
        elif tickType == TickType.HIGH:
            self.market_data[reqId]['high'] = price
        elif tickType == TickType.LOW:
            self.market_data[reqId]['low'] = price
        elif tickType == TickType.CLOSE:
            self.market_data[reqId]['close'] = price

        self.market_data[reqId]['timestamp'] = datetime.now()
        self.data_queue.put(('tick_price', {
            'req_id': reqId,
            'tick_type': tickType,
            'price': price,
            'timestamp': datetime.now()
        }))

    def tickSize(self, reqId: TickerId, tickType: TickType, size: int):
        """Market data size ticks"""
        if reqId not in self.market_data:
            self.market_data[reqId] = {}

        if tickType == TickType.VOLUME:
            self.market_data[reqId]['volume'] = size
        elif tickType == TickType.BID_SIZE:
            self.market_data[reqId]['bid_size'] = size
        elif tickType == TickType.ASK_SIZE:
            self.market_data[reqId]['ask_size'] = size

        self.data_queue.put(('tick_size', {
            'req_id': reqId,
            'tick_type': tickType,
            'size': size,
            'timestamp': datetime.now()
        }))

class AstroAIBClient(EClient):
    """IB API Client for AstroA system"""

    def __init__(self, wrapper):
        EClient.__init__(self, wrapper)
        self.wrapper = wrapper

class AstroAIBApp(AstroAIBWrapper, AstroAIBClient):
    """Combined IB API application for AstroA"""

    def __init__(self):
        AstroAIBWrapper.__init__(self)
        AstroAIBClient.__init__(self, wrapper=self)
        self.connected = False
        self.req_id_counter = 1000

    def get_next_req_id(self):
        """Get next request ID"""
        self.req_id_counter += 1
        return self.req_id_counter

    def connect_to_ib(self, host='127.0.0.1', port=7497, client_id=1):
        """Connect to IB Gateway/TWS"""
        try:
            self.connect(host, port, client_id)

            # Start API thread
            api_thread = threading.Thread(target=self.run, daemon=True)
            api_thread.start()

            # Wait for connection
            timeout = 10
            start_time = time.time()

            while not self.isConnected() and (time.time() - start_time) < timeout:
                time.sleep(0.1)

            if self.isConnected():
                self.connected = True
                logging.info("Successfully connected to Interactive Brokers")

                # Request next valid order ID
                self.reqIds(-1)

                return True
            else:
                logging.error("Failed to connect to Interactive Brokers")
                return False

        except Exception as e:
            logging.error(f"Connection error: {str(e)}")
            return False

    def disconnect_from_ib(self):
        """Disconnect from IB"""
        if self.isConnected():
            self.disconnect()
            self.connected = False
            logging.info("Disconnected from Interactive Brokers")

EOF</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 3: Trading Implementation -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">3</div>
                <h2 class="text-xl font-bold">Implement Trading Functions</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-3">Create Trading Manager:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create IB trading manager
cat > ib_trading_manager.py << 'EOF'
import sys
import time
import logging
from datetime import datetime
from typing import Dict, Optional, List

sys.path.append('/home/<USER>/axmadcodes/AstroA')

from ib_wrapper import AstroAIBApp
from ibapi.contract import Contract
from ibapi.order import Order
from agents.risk_manager.risk_management_agent import RiskManagementAgent
from agents.portfolio_manager.portfolio_management_agent import PortfolioManagementAgent

class IBTradingManager:
    """Interactive Brokers Trading Manager for AstroA"""

    def __init__(self, host='127.0.0.1', port=7497, client_id=1):
        self.ib_app = AstroAIBApp()
        self.host = host
        self.port = port
        self.client_id = client_id
        self.risk_manager = RiskManagementAgent()
        self.portfolio_manager = PortfolioManagementAgent()
        self.connected = False
        self.req_id_map = {}  # Map request IDs to symbols

    def connect(self):
        """Connect to Interactive Brokers"""
        return self.ib_app.connect_to_ib(self.host, self.port, self.client_id)

    def disconnect(self):
        """Disconnect from Interactive Brokers"""
        self.ib_app.disconnect_from_ib()

    def create_stock_contract(self, symbol: str, exchange: str = "SMART") -> Contract:
        """Create stock contract"""
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "STK"
        contract.exchange = exchange
        contract.currency = "USD"
        return contract

    def create_forex_contract(self, symbol: str) -> Contract:
        """Create forex contract (e.g., EURUSD)"""
        contract = Contract()
        contract.symbol = symbol[:3]
        contract.secType = "CASH"
        contract.exchange = "IDEALPRO"
        contract.currency = symbol[3:]
        return contract

    def create_market_order(self, action: str, quantity: float) -> Order:
        """Create market order"""
        order = Order()
        order.action = action.upper()
        order.orderType = "MKT"
        order.totalQuantity = quantity
        return order

    def create_limit_order(self, action: str, quantity: float, limit_price: float) -> Order:
        """Create limit order"""
        order = Order()
        order.action = action.upper()
        order.orderType = "LMT"
        order.totalQuantity = quantity
        order.lmtPrice = limit_price
        return order

    def create_stop_loss_order(self, action: str, quantity: float, stop_price: float) -> Order:
        """Create stop-loss order"""
        order = Order()
        order.action = action.upper()
        order.orderType = "STP"
        order.totalQuantity = quantity
        order.auxPrice = stop_price
        return order

    def place_order(self, symbol: str, action: str, quantity: float,
                   order_type: str = "MARKET", limit_price: float = None,
                   stop_price: float = None) -> Optional[int]:
        """Place order with AstroA integration"""
        try:
            # Risk assessment
            trade_data = {
                'symbol': symbol,
                'action': action,
                'quantity': quantity,
                'order_type': order_type,
                'limit_price': limit_price,
                'stop_price': stop_price,
                'broker': 'interactive_brokers'
            }

            risk_analysis = self.risk_manager.assess_trade_risk(trade_data)

            if not risk_analysis.get('approved', False):
                logging.warning(f"Trade rejected by risk management: {risk_analysis.get('reason')}")
                return None

            # Create contract
            if '/' in symbol or len(symbol) == 6:  # Forex pair
                contract = self.create_forex_contract(symbol)
            else:  # Stock
                contract = self.create_stock_contract(symbol)

            # Create order
            if order_type.upper() == "MARKET":
                order = self.create_market_order(action, quantity)
            elif order_type.upper() == "LIMIT":
                if limit_price is None:
                    raise ValueError("Limit price required for limit orders")
                order = self.create_limit_order(action, quantity, limit_price)
            elif order_type.upper() == "STOP":
                if stop_price is None:
                    raise ValueError("Stop price required for stop orders")
                order = self.create_stop_loss_order(action, quantity, stop_price)
            else:
                raise ValueError(f"Unsupported order type: {order_type}")

            # Wait for next valid order ID
            timeout = 5
            start_time = time.time()
            while self.ib_app.next_order_id is None and (time.time() - start_time) < timeout:
                time.sleep(0.1)

            if self.ib_app.next_order_id is None:
                logging.error("Failed to get valid order ID")
                return None

            order_id = self.ib_app.next_order_id
            self.ib_app.next_order_id += 1

            # Place order
            self.ib_app.placeOrder(order_id, contract, order)

            # Log trade in portfolio manager
            self.portfolio_manager.log_trade({
                'order_id': order_id,
                'symbol': symbol,
                'action': action,
                'quantity': quantity,
                'order_type': order_type,
                'limit_price': limit_price,
                'stop_price': stop_price,
                'broker': 'interactive_brokers',
                'timestamp': datetime.now(),
                'risk_approved': True
            })

            logging.info(f"Order placed: {order_id} - {action} {quantity} {symbol}")
            return order_id

        except Exception as e:
            logging.error(f"Error placing order: {str(e)}")
            return None

    def cancel_order(self, order_id: int):
        """Cancel order"""
        try:
            self.ib_app.cancelOrder(order_id)
            logging.info(f"Cancelled order: {order_id}")
        except Exception as e:
            logging.error(f"Error cancelling order {order_id}: {str(e)}")

    def get_market_data(self, symbol: str, data_type: str = "REALTIME") -> Optional[int]:
        """Request market data"""
        try:
            # Create contract
            if '/' in symbol or len(symbol) == 6:  # Forex
                contract = self.create_forex_contract(symbol)
            else:  # Stock
                contract = self.create_stock_contract(symbol)

            req_id = self.ib_app.get_next_req_id()
            self.req_id_map[req_id] = symbol

            # Request tick data
            tick_types = "233"  # RTVolume for real-time data
            if data_type == "DELAYED":
                tick_types = "221"

            self.ib_app.reqMktData(req_id, contract, tick_types, False, False, [])

            logging.info(f"Requested market data for {symbol} (req_id: {req_id})")
            return req_id

        except Exception as e:
            logging.error(f"Error requesting market data for {symbol}: {str(e)}")
            return None

    def get_account_summary(self):
        """Request account summary"""
        try:
            req_id = self.ib_app.get_next_req_id()
            tags = "TotalCashValue,NetLiquidation,UnrealizedPnL,RealizedPnL,BuyingPower"

            self.ib_app.reqAccountSummary(req_id, "All", tags)
            logging.info("Requested account summary")

        except Exception as e:
            logging.error(f"Error requesting account summary: {str(e)}")

    def get_positions(self):
        """Request current positions"""
        try:
            self.ib_app.reqPositions()
            logging.info("Requested positions")

        except Exception as e:
            logging.error(f"Error requesting positions: {str(e)}")

    def get_open_orders(self):
        """Request open orders"""
        try:
            self.ib_app.reqOpenOrders()
            logging.info("Requested open orders")

        except Exception as e:
            logging.error(f"Error requesting open orders: {str(e)}")

    def execute_astro_signal(self, signal_data: Dict) -> bool:
        """Execute AstroA trading signal through IB"""
        try:
            symbol = signal_data['symbol']
            action = signal_data['action']
            quantity = signal_data.get('quantity', 1.0)
            order_type = signal_data.get('order_type', 'MARKET')
            limit_price = signal_data.get('limit_price')
            stop_price = signal_data.get('stop_price')

            order_id = self.place_order(
                symbol=symbol,
                action=action,
                quantity=quantity,
                order_type=order_type,
                limit_price=limit_price,
                stop_price=stop_price
            )

            return order_id is not None

        except Exception as e:
            logging.error(f"Error executing AstroA signal: {str(e)}")
            return False

EOF</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Test Trading Connection:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create test script
cat > test_ib_connection.py << 'EOF'
import logging
import time
from ib_trading_manager import IBTradingManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_ib_connection():
    """Test IB connection and basic functions"""

    # Initialize trading manager (use paper trading port 7497)
    trader = IBTradingManager(host='127.0.0.1', port=7497, client_id=1)

    try:
        # Connect to IB
        if not trader.connect():
            logging.error("Failed to connect to Interactive Brokers")
            return False

        logging.info("Connected to Interactive Brokers successfully")

        # Wait for connection to stabilize
        time.sleep(2)

        # Test account summary
        trader.get_account_summary()
        time.sleep(1)

        # Test positions
        trader.get_positions()
        time.sleep(1)

        # Test market data request
        req_id = trader.get_market_data("AAPL")
        if req_id:
            logging.info(f"Market data requested for AAPL (req_id: {req_id})")

        # Wait for data
        time.sleep(5)

        # Check received data
        if trader.ib_app.market_data:
            logging.info(f"Received market data: {trader.ib_app.market_data}")

        if trader.ib_app.account_data:
            logging.info(f"Account data: {trader.ib_app.account_data}")

        return True

    except Exception as e:
        logging.error(f"Test failed: {str(e)}")
        return False

    finally:
        trader.disconnect()

if __name__ == "__main__":
    test_ib_connection()
EOF

# Run test (make sure IB Gateway is running first)
python test_ib_connection.py</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 4: Market Data Integration -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">4</div>
                <h2 class="text-xl font-bold">Real-Time Market Data Integration</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-3">Create Market Data Manager:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create market data manager
cat > ib_market_data_manager.py << 'EOF'
import sys
import threading
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional

sys.path.append('/home/<USER>/axmadcodes/AstroA')

from ib_trading_manager import IBTradingManager
from agents.data_collector.data_collection_agent import DataCollectionAgent
from database.database_manager import DatabaseManager

class IBMarketDataManager:
    """Real-time market data manager for Interactive Brokers"""

    def __init__(self, symbols: List[str], host='127.0.0.1', port=7497):
        self.trader = IBTradingManager(host, port, client_id=2)  # Different client ID
        self.data_collector = DataCollectionAgent()
        self.db = DatabaseManager()
        self.symbols = symbols
        self.subscriptions = {}
        self.running = False
        self.data_thread = None

    def connect(self) -> bool:
        """Connect to IB for market data"""
        return self.trader.connect()

    def disconnect(self):
        """Disconnect from IB"""
        self.stop_data_collection()
        self.trader.disconnect()

    def subscribe_to_symbol(self, symbol: str) -> bool:
        """Subscribe to real-time data for a symbol"""
        try:
            req_id = self.trader.get_market_data(symbol, "REALTIME")
            if req_id:
                self.subscriptions[symbol] = req_id
                logging.info(f"Subscribed to {symbol} data (req_id: {req_id})")
                return True
            return False
        except Exception as e:
            logging.error(f"Error subscribing to {symbol}: {str(e)}")
            return False

    def subscribe_to_all_symbols(self):
        """Subscribe to all configured symbols"""
        for symbol in self.symbols:
            self.subscribe_to_symbol(symbol)
            time.sleep(0.1)  # Small delay between subscriptions

    def process_market_data(self):
        """Process incoming market data"""
        while self.running:
            try:
                # Check for new data
                if not self.trader.ib_app.data_queue.empty():
                    data_type, data = self.trader.ib_app.data_queue.get()

                    if data_type == 'tick_price':
                        self.handle_price_tick(data)
                    elif data_type == 'tick_size':
                        self.handle_size_tick(data)
                    elif data_type == 'error':
                        self.handle_error(data)

                time.sleep(0.01)  # Small delay to prevent high CPU usage

            except Exception as e:
                logging.error(f"Error processing market data: {str(e)}")
                time.sleep(1)

    def handle_price_tick(self, tick_data: Dict):
        """Handle price tick data"""
        try:
            req_id = tick_data['req_id']
            symbol = self.get_symbol_from_req_id(req_id)

            if symbol:
                # Get current market data for symbol
                market_data = self.trader.ib_app.market_data.get(req_id, {})

                # Prepare data for AstroA system
                processed_data = {
                    'symbol': symbol,
                    'timestamp': tick_data['timestamp'],
                    'price': tick_data['price'],
                    'tick_type': tick_data['tick_type'],
                    'source': 'interactive_brokers',
                    'data_type': 'tick_price'
                }

                # Send to data collector
                self.data_collector.process_real_time_data(processed_data)

                # Store in database if it's a significant tick
                if tick_data['tick_type'] in [1, 2, 4]:  # BID, ASK, LAST
                    self.store_tick_data(processed_data)

        except Exception as e:
            logging.error(f"Error handling price tick: {str(e)}")

    def handle_size_tick(self, tick_data: Dict):
        """Handle size/volume tick data"""
        try:
            req_id = tick_data['req_id']
            symbol = self.get_symbol_from_req_id(req_id)

            if symbol:
                processed_data = {
                    'symbol': symbol,
                    'timestamp': tick_data['timestamp'],
                    'size': tick_data['size'],
                    'tick_type': tick_data['tick_type'],
                    'source': 'interactive_brokers',
                    'data_type': 'tick_size'
                }

                self.data_collector.process_real_time_data(processed_data)

        except Exception as e:
            logging.error(f"Error handling size tick: {str(e)}")

    def handle_error(self, error_data: Dict):
        """Handle API errors"""
        if error_data.get('critical', False):
            logging.critical(f"Critical IB API error: {error_data['message']}")
            # Implement reconnection logic if needed
        else:
            logging.warning(f"IB API warning: {error_data['message']}")

    def get_symbol_from_req_id(self, req_id: int) -> Optional[str]:
        """Get symbol from request ID"""
        for symbol, sub_req_id in self.subscriptions.items():
            if sub_req_id == req_id:
                return symbol
        return None

    def store_tick_data(self, tick_data: Dict):
        """Store tick data in database"""
        try:
            query = """
            INSERT INTO market_data_realtime
            (symbol, timestamp, price, tick_type, source)
            VALUES (%s, %s, %s, %s, %s)
            """

            self.db.execute_query(query, (
                tick_data['symbol'],
                tick_data['timestamp'],
                tick_data['price'],
                tick_data['tick_type'],
                tick_data['source']
            ))

        except Exception as e:
            logging.error(f"Error storing tick data: {str(e)}")

    def start_data_collection(self):
        """Start real-time data collection"""
        if not self.running:
            self.running = True

            # Subscribe to all symbols
            self.subscribe_to_all_symbols()

            # Start data processing thread
            self.data_thread = threading.Thread(target=self.process_market_data, daemon=True)
            self.data_thread.start()

            logging.info("Started real-time data collection")

    def stop_data_collection(self):
        """Stop real-time data collection"""
        if self.running:
            self.running = False

            # Cancel all market data subscriptions
            for symbol, req_id in self.subscriptions.items():
                try:
                    self.trader.ib_app.cancelMktData(req_id)
                except:
                    pass

            self.subscriptions.clear()

            if self.data_thread and self.data_thread.is_alive():
                self.data_thread.join(timeout=5)

            logging.info("Stopped real-time data collection")

    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for symbol"""
        req_id = self.subscriptions.get(symbol)
        if req_id and req_id in self.trader.ib_app.market_data:
            market_data = self.trader.ib_app.market_data[req_id]
            return market_data.get('last_price') or market_data.get('bid_price')
        return None

EOF</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Create Database Schema for Real-Time Data:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create database tables for IB data
cat > setup_ib_tables.sql << 'EOF'
-- Real-time market data table
CREATE TABLE IF NOT EXISTS market_data_realtime (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    price DECIMAL(12, 4),
    tick_type INTEGER,
    size INTEGER,
    source VARCHAR(50) DEFAULT 'interactive_brokers',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- IB specific order tracking
CREATE TABLE IF NOT EXISTS ib_orders (
    order_id INTEGER PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    action VARCHAR(10) NOT NULL,
    quantity DECIMAL(12, 4) NOT NULL,
    order_type VARCHAR(20) NOT NULL,
    limit_price DECIMAL(12, 4),
    stop_price DECIMAL(12, 4),
    status VARCHAR(20),
    filled_quantity DECIMAL(12, 4) DEFAULT 0,
    avg_fill_price DECIMAL(12, 4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- IB account data
CREATE TABLE IF NOT EXISTS ib_account_data (
    id SERIAL PRIMARY KEY,
    account_id VARCHAR(50),
    tag VARCHAR(50) NOT NULL,
    value DECIMAL(15, 2),
    currency VARCHAR(10),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- IB positions
CREATE TABLE IF NOT EXISTS ib_positions (
    id SERIAL PRIMARY KEY,
    account_id VARCHAR(50),
    symbol VARCHAR(20) NOT NULL,
    position DECIMAL(12, 4) NOT NULL,
    avg_cost DECIMAL(12, 4),
    market_value DECIMAL(15, 2),
    unrealized_pnl DECIMAL(15, 2),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(account_id, symbol, timestamp)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_market_data_realtime_symbol_timestamp ON market_data_realtime(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_ib_orders_symbol_status ON ib_orders(symbol, status);
CREATE INDEX IF NOT EXISTS idx_ib_positions_symbol ON ib_positions(symbol);

EOF

# Execute the SQL
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -f setup_ib_tables.sql</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 5: Integration with AstroA -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">5</div>
                <h2 class="text-xl font-bold">Complete AstroA System Integration</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-3">Create IB Broker Agent:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create IB broker agent for AstroA
mkdir -p /home/<USER>/axmadcodes/AstroA/agents/ib_broker
cat > /home/<USER>/axmadcodes/AstroA/agents/ib_broker/ib_broker_agent.py << 'EOF'
import sys
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

sys.path.append('/home/<USER>/axmadcodes/AstroA')
sys.path.append('/home/<USER>/axmadcodes/AstroA/integrations/interactive_brokers')

from ib_trading_manager import IBTradingManager
from ib_market_data_manager import IBMarketDataManager
from agents.base_agent import BaseAgent

class IBBrokerAgent(BaseAgent):
    """Interactive Brokers Broker Agent for AstroA System"""

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("IBBrokerAgent", config)

        # Configuration
        self.host = config.get('host', '127.0.0.1') if config else '127.0.0.1'
        self.port = config.get('port', 7497) if config else 7497  # Paper trading port
        self.live_port = config.get('live_port', 7496) if config else 7496
        self.client_id = config.get('client_id', 1) if config else 1
        self.symbols = config.get('symbols', ['AAPL', 'GOOGL', 'MSFT']) if config else ['AAPL', 'GOOGL', 'MSFT']

        # Trading manager
        self.trading_manager = IBTradingManager(self.host, self.port, self.client_id)

        # Market data manager
        self.market_data_manager = IBMarketDataManager(self.symbols, self.host, self.port)

        # State
        self.connected = False
        self.live_trading = False

    def initialize(self) -> bool:
        """Initialize IB connection"""
        try:
            self.log_info("Initializing Interactive Brokers connection...")

            # Connect trading manager
            if not self.trading_manager.connect():
                self.log_error("Failed to connect trading manager to IB")
                return False

            # Connect market data manager
            if not self.market_data_manager.connect():
                self.log_error("Failed to connect market data manager to IB")
                return False

            self.connected = True
            self.log_info("Successfully connected to Interactive Brokers")

            # Start market data collection
            self.market_data_manager.start_data_collection()

            return True

        except Exception as e:
            self.log_error(f"Error initializing IB connection: {str(e)}")
            return False

    def shutdown(self):
        """Shutdown IB connections"""
        try:
            if self.market_data_manager:
                self.market_data_manager.disconnect()

            if self.trading_manager:
                self.trading_manager.disconnect()

            self.connected = False
            self.log_info("Disconnected from Interactive Brokers")

        except Exception as e:
            self.log_error(f"Error during shutdown: {str(e)}")

    def execute_trade(self, trade_data: Dict) -> Dict:
        """Execute trade through Interactive Brokers"""
        try:
            if not self.connected:
                return {'success': False, 'error': 'Not connected to IB'}

            symbol = trade_data['symbol']
            action = trade_data['action']
            quantity = trade_data.get('quantity', 1.0)
            order_type = trade_data.get('order_type', 'MARKET')
            limit_price = trade_data.get('limit_price')
            stop_price = trade_data.get('stop_price')

            # Execute trade
            order_id = self.trading_manager.place_order(
                symbol=symbol,
                action=action,
                quantity=quantity,
                order_type=order_type,
                limit_price=limit_price,
                stop_price=stop_price
            )

            if order_id:
                result = {
                    'success': True,
                    'order_id': order_id,
                    'broker': 'interactive_brokers',
                    'timestamp': datetime.now().isoformat()
                }

                self.log_info(f"Trade executed: {action} {quantity} {symbol} (Order ID: {order_id})")
                return result
            else:
                return {'success': False, 'error': 'Failed to place order'}

        except Exception as e:
            self.log_error(f"Error executing trade: {str(e)}")
            return {'success': False, 'error': str(e)}

    def cancel_order(self, order_id: int) -> Dict:
        """Cancel order"""
        try:
            if not self.connected:
                return {'success': False, 'error': 'Not connected to IB'}

            self.trading_manager.cancel_order(order_id)

            return {
                'success': True,
                'order_id': order_id,
                'action': 'cancelled',
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.log_error(f"Error cancelling order {order_id}: {str(e)}")
            return {'success': False, 'error': str(e)}

    def get_account_info(self) -> Dict:
        """Get account information"""
        try:
            if not self.connected:
                return {'success': False, 'error': 'Not connected to IB'}

            # Request account summary
            self.trading_manager.get_account_summary()

            # Return cached account data
            account_data = self.trading_manager.ib_app.account_data.copy()

            return {
                'success': True,
                'account_data': account_data,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.log_error(f"Error getting account info: {str(e)}")
            return {'success': False, 'error': str(e)}

    def get_positions(self) -> Dict:
        """Get current positions"""
        try:
            if not self.connected:
                return {'success': False, 'error': 'Not connected to IB'}

            # Request positions
            self.trading_manager.get_positions()

            # Return cached positions
            positions = self.trading_manager.ib_app.positions.copy()

            return {
                'success': True,
                'positions': positions,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.log_error(f"Error getting positions: {str(e)}")
            return {'success': False, 'error': str(e)}

    def get_market_data(self, symbol: str) -> Dict:
        """Get current market data for symbol"""
        try:
            if not self.connected:
                return {'success': False, 'error': 'Not connected to IB'}

            price = self.market_data_manager.get_current_price(symbol)

            if price:
                return {
                    'success': True,
                    'symbol': symbol,
                    'price': price,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {'success': False, 'error': 'No market data available'}

        except Exception as e:
            self.log_error(f"Error getting market data for {symbol}: {str(e)}")
            return {'success': False, 'error': str(e)}

    def switch_to_live_trading(self) -> bool:
        """Switch from paper to live trading"""
        try:
            if self.live_trading:
                self.log_warning("Already in live trading mode")
                return True

            self.log_info("Switching to live trading mode...")

            # Disconnect current connections
            self.shutdown()

            # Reconnect with live port
            self.trading_manager = IBTradingManager(self.host, self.live_port, self.client_id)
            self.market_data_manager = IBMarketDataManager(self.symbols, self.host, self.live_port)

            # Initialize with live connection
            if self.initialize():
                self.live_trading = True
                self.log_info("Successfully switched to live trading")
                return True
            else:
                self.log_error("Failed to switch to live trading")
                return False

        except Exception as e:
            self.log_error(f"Error switching to live trading: {str(e)}")
            return False

    def get_status(self) -> Dict:
        """Get broker agent status"""
        return {
            'connected': self.connected,
            'live_trading': self.live_trading,
            'port': self.port,
            'symbols_subscribed': len(self.market_data_manager.subscriptions),
            'timestamp': datetime.now().isoformat()
        }

EOF</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Update Main Trading System:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Update main trading coordinator to include IB
cat >> /home/<USER>/axmadcodes/AstroA/main_trading_system.py << 'EOF'

# Add Interactive Brokers integration
from agents.ib_broker.ib_broker_agent import IBBrokerAgent

class AstroATradingSystem:
    def __init__(self):
        # ... existing code ...

        # Add IB Broker Agent
        self.ib_broker = IBBrokerAgent({
            'host': '127.0.0.1',
            'port': 7497,  # Paper trading
            'live_port': 7496,  # Live trading
            'client_id': 1,
            'symbols': ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA']
        })

    def initialize_system(self):
        """Initialize all system components"""
        # ... existing initialization code ...

        # Initialize IB broker
        if not self.ib_broker.initialize():
            self.logger.error("Failed to initialize Interactive Brokers")
            return False

        self.logger.info("Interactive Brokers initialized successfully")
        return True

    def execute_trade_through_ib(self, signal_data):
        """Execute trade through Interactive Brokers"""
        try:
            # Convert AstroA signal to IB trade format
            ib_trade_data = {
                'symbol': signal_data['symbol'],
                'action': signal_data['action'],
                'quantity': signal_data.get('quantity', 1.0),
                'order_type': signal_data.get('order_type', 'MARKET'),
                'limit_price': signal_data.get('limit_price'),
                'stop_price': signal_data.get('stop_price')
            }

            # Execute through IB
            result = self.ib_broker.execute_trade(ib_trade_data)

            if result['success']:
                self.logger.info(f"IB trade executed: {result['order_id']}")

                # Update portfolio manager
                self.portfolio_manager.update_position_from_trade(result)

                return result
            else:
                self.logger.error(f"IB trade failed: {result.get('error')}")
                return result

        except Exception as e:
            self.logger.error(f"Error executing IB trade: {str(e)}")
            return {'success': False, 'error': str(e)}

    def shutdown_system(self):
        """Shutdown all system components"""
        # ... existing shutdown code ...

        # Shutdown IB broker
        if self.ib_broker:
            self.ib_broker.shutdown()

EOF</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 6: Production & Monitoring -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">6</div>
                <h2 class="text-xl font-bold">Production Setup & Monitoring</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-3">Create IB Gateway Auto-Start Script:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create IB Gateway startup script
cat > start_ib_gateway.sh << 'EOF'
#!/bin/bash

# IB Gateway Auto-Start Script for AstroA
# This script starts IB Gateway with the required configuration

IB_GATEWAY_DIR="/opt/ibgateway"
LOG_DIR="/home/<USER>/axmadcodes/AstroA/logs"
CONFIG_DIR="/home/<USER>/.ibgateway"

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Create IB Gateway configuration
mkdir -p "$CONFIG_DIR"

# Create jts.ini configuration file
cat > "$CONFIG_DIR/jts.ini" << 'CONFIG_EOF'
[IBGateway]
ApiOnly=true
LocalServerPort=7497
LogLevel=Error
MaxDaysOfLogFiles=30
ReadOnlyApi=false
PrecautionaryConstraints=true
CONFIG_EOF

# Create Xvfb display for headless operation
export DISPLAY=:1
Xvfb :1 -screen 0 1024x768x24 -ac &
XVFB_PID=$!

# Function to cleanup on exit
cleanup() {
    echo "Shutting down IB Gateway..."
    kill $XVFB_PID 2>/dev/null
    pkill -f "ibgateway"
    exit 0
}

trap cleanup SIGTERM SIGINT

# Start IB Gateway
echo "Starting IB Gateway..."
cd "$IB_GATEWAY_DIR"

# Start IB Gateway with configuration
./ibgateway \
    -Djava.awt.headless=false \
    -Xmx512m \
    -Dswing.defaultlaf=com.sun.java.swing.plaf.gtk.GTKLookAndFeel \
    >> "$LOG_DIR/ibgateway.log" 2>&1 &

IB_PID=$!

echo "IB Gateway started with PID: $IB_PID"
echo "Log file: $LOG_DIR/ibgateway.log"

# Wait for IB Gateway to be ready
sleep 30

# Test connection
python3 -c "
import socket
import sys
try:
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', 7497))
    sock.close()
    if result == 0:
        print('IB Gateway is ready for connections')
        sys.exit(0)
    else:
        print('IB Gateway connection failed')
        sys.exit(1)
except Exception as e:
    print(f'Connection test error: {e}')
    sys.exit(1)
"

if [ $? -eq 0 ]; then
    echo "IB Gateway is ready!"
else
    echo "IB Gateway failed to start properly"
    cleanup
fi

# Keep script running
wait $IB_PID

EOF

chmod +x start_ib_gateway.sh</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Create Monitoring Dashboard:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create IB monitoring dashboard
cat > ib_monitor_dashboard.py << 'EOF'
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import sys
import time

sys.path.append('/home/<USER>/axmadcodes/AstroA')
sys.path.append('/home/<USER>/axmadcodes/AstroA/integrations/interactive_brokers')

from database.database_manager import DatabaseManager
from ib_trading_manager import IBTradingManager

class IBMonitorDashboard:
    def __init__(self):
        self.db = DatabaseManager()

    def get_ib_orders(self, days_back=7):
        """Get IB orders from database"""
        query = """
        SELECT order_id, symbol, action, quantity, order_type,
               status, filled_quantity, avg_fill_price, created_at
        FROM ib_orders
        WHERE created_at >= %s
        ORDER BY created_at DESC
        """

        start_date = datetime.now() - timedelta(days=days_back)
        return pd.read_sql(query, self.db.connection, params=[start_date])

    def get_ib_positions(self):
        """Get current IB positions"""
        query = """
        SELECT DISTINCT ON (symbol) symbol, position, avg_cost,
               market_value, unrealized_pnl, timestamp
        FROM ib_positions
        ORDER BY symbol, timestamp DESC
        """

        return pd.read_sql(query, self.db.connection)

    def get_account_summary(self):
        """Get latest account summary"""
        query = """
        SELECT DISTINCT ON (tag) tag, value, currency, timestamp
        FROM ib_account_data
        ORDER BY tag, timestamp DESC
        """

        return pd.read_sql(query, self.db.connection)

    def get_market_data_stats(self, hours_back=24):
        """Get market data statistics"""
        query = """
        SELECT symbol, COUNT(*) as tick_count,
               MIN(timestamp) as first_tick,
               MAX(timestamp) as last_tick
        FROM market_data_realtime
        WHERE timestamp >= %s
        GROUP BY symbol
        ORDER BY tick_count DESC
        """

        start_time = datetime.now() - timedelta(hours=hours_back)
        return pd.read_sql(query, self.db.connection, params=[start_time])

def main():
    st.set_page_config(
        page_title="Interactive Brokers Monitor",
        page_icon="🏦",
        layout="wide"
    )

    st.title("🏦 Interactive Brokers Integration Monitor")
    st.markdown("Real-time monitoring of IB trading and market data")

    monitor = IBMonitorDashboard()

    # Sidebar controls
    st.sidebar.header("⚙️ Controls")

    # Connection test
    if st.sidebar.button("Test IB Connection"):
        try:
            trader = IBTradingManager()
            if trader.connect():
                st.sidebar.success("✅ Connected to IB")
                trader.disconnect()
            else:
                st.sidebar.error("❌ Failed to connect to IB")
        except Exception as e:
            st.sidebar.error(f"❌ Connection error: {str(e)}")

    days_back = st.sidebar.slider("Days to show", 1, 30, 7)
    auto_refresh = st.sidebar.checkbox("Auto refresh (30s)")

    # Auto refresh
    if auto_refresh:
        time.sleep(30)
        st.experimental_rerun()

    # Main dashboard
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📊 Account Summary")
        account_df = monitor.get_account_summary()

        if not account_df.empty:
            # Display key metrics
            for _, row in account_df.iterrows():
                if row['tag'] in ['NetLiquidation', 'TotalCashValue', 'BuyingPower']:
                    value = f"{float(row['value']):,.2f}" if row['value'] else "N/A"
                    st.metric(row['tag'], f"{value} {row['currency']}")
        else:
            st.info("No account data available")

        st.subheader("📈 Current Positions")
        positions_df = monitor.get_ib_positions()

        if not positions_df.empty:
            st.dataframe(positions_df, use_container_width=True)

            # PnL Chart
            if 'unrealized_pnl' in positions_df.columns:
                fig = px.bar(positions_df, x='symbol', y='unrealized_pnl',
                           title="Unrealized PnL by Symbol")
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No positions found")

    with col2:
        st.subheader("📋 Recent Orders")
        orders_df = monitor.get_ib_orders(days_back)

        if not orders_df.empty:
            # Order status summary
            status_counts = orders_df['status'].value_counts()
            col2a, col2b, col2c = st.columns(3)

            col2a.metric("Total Orders", len(orders_df))
            col2b.metric("Filled", status_counts.get('Filled', 0))
            col2c.metric("Pending", status_counts.get('Submitted', 0) + status_counts.get('PreSubmitted', 0))

            # Orders table
            st.dataframe(orders_df, use_container_width=True)

            # Order status chart
            fig = px.pie(values=status_counts.values, names=status_counts.index,
                        title="Order Status Distribution")
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No orders found for the selected period")

    # Market data section
    st.subheader("📡 Market Data Feed Status")
    market_stats = monitor.get_market_data_stats()

    if not market_stats.empty:
        col3, col4 = st.columns(2)

        with col3:
            st.dataframe(market_stats, use_container_width=True)

        with col4:
            # Tick count chart
            fig = px.bar(market_stats.head(10), x='symbol', y='tick_count',
                        title="Tick Count by Symbol (Last 24h)")
            st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("No market data found")

    # System status
    st.subheader("🟢 System Status")
    col5, col6, col7, col8 = st.columns(4)

    col5.metric("IB Gateway", "🟢 Running")
    col6.metric("Market Data", "🟢 Active")
    col7.metric("Order Management", "🟢 Online")
    col8.metric("Risk Management", "🟢 Active")

    # Footer
    st.markdown("---")
    st.caption(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
EOF</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Start Complete IB Integration:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Start IB Gateway
./start_ib_gateway.sh &

# Wait for IB Gateway to be ready
sleep 30

# Test the complete integration
cat > test_complete_ib_integration.py << 'EOF'
import sys
import time
import logging

sys.path.append('/home/<USER>/axmadcodes/AstroA')

from agents.ib_broker.ib_broker_agent import IBBrokerAgent

# Configure logging
logging.basicConfig(level=logging.INFO)

def test_complete_integration():
    """Test complete IB integration with AstroA"""

    # Initialize IB broker agent
    config = {
        'host': '127.0.0.1',
        'port': 7497,  # Paper trading
        'client_id': 1,
        'symbols': ['AAPL', 'GOOGL', 'MSFT']
    }

    ib_agent = IBBrokerAgent(config)

    try:
        # Initialize connection
        if not ib_agent.initialize():
            print("❌ Failed to initialize IB agent")
            return False

        print("✅ IB agent initialized successfully")

        # Wait for market data
        time.sleep(5)

        # Test account info
        account_info = ib_agent.get_account_info()
        if account_info['success']:
            print("✅ Account info retrieved")
        else:
            print("❌ Failed to get account info")

        # Test market data
        market_data = ib_agent.get_market_data('AAPL')
        if market_data['success']:
            print(f"✅ Market data for AAPL: ${market_data['price']}")
        else:
            print("❌ Failed to get market data")

        # Test positions
        positions = ib_agent.get_positions()
        if positions['success']:
            print("✅ Positions retrieved")
        else:
            print("❌ Failed to get positions")

        print("🎉 Complete IB integration test successful!")
        return True

    except Exception as e:
        print(f"❌ Integration test failed: {str(e)}")
        return False

    finally:
        ib_agent.shutdown()

if __name__ == "__main__":
    test_complete_integration()
EOF

# Run the integration test
python test_complete_ib_integration.py

# Start monitoring dashboard
streamlit run ib_monitor_dashboard.py --server.port 8502 &

echo "🎉 Interactive Brokers integration is ready!"
echo "📊 Monitoring dashboard: http://localhost:8502"</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testing & Validation -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <i data-lucide="check-circle" class="w-6 h-6 mr-2 text-green-600"></i>
                Testing & Validation
            </h2>

            <div class="space-y-4">
                <div>
                    <h3 class="font-semibold mb-2">✅ Integration Checklist:</h3>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> IB Gateway/TWS installed and configured
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Python API dependencies installed
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> API trading enabled in IB account
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Database tables created
                            </label>
                        </div>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Trading manager connecting successfully
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Market data streaming properly
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Order placement working
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Monitoring dashboard operational
                            </label>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-2">🔍 Performance Validation:</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <ul class="space-y-1 text-sm text-gray-600">
                            <li>• <strong>Connection Latency:</strong> &lt; 100ms to IB servers</li>
                            <li>• <strong>Order Execution:</strong> &lt; 1 second for market orders</li>
                            <li>• <strong>Market Data:</strong> Real-time tick updates (&lt; 50ms delay)</li>
                            <li>• <strong>Risk Integration:</strong> All trades validated by AstroA risk management</li>
                            <li>• <strong>Data Persistence:</strong> All trades and market data stored in database</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Support & Troubleshooting -->
        <div class="bg-blue-50 rounded-lg p-6">
            <h2 class="text-xl font-bold mb-4 flex items-center text-blue-800">
                <i data-lucide="help-circle" class="w-6 h-6 mr-2"></i>
                Support & Troubleshooting
            </h2>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold mb-2 text-blue-800">Common Issues:</h3>
                    <ul class="space-y-1 text-blue-700 text-sm">
                        <li>• Connection timeout → Check IB Gateway status</li>
                        <li>• API permissions → Verify account settings</li>
                        <li>• Market data errors → Check exchange subscriptions</li>
                        <li>• Order rejections → Verify account funds and risk settings</li>
                        <li>• Socket port conflicts → Use different client IDs</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold mb-2 text-blue-800">Important Notes:</h3>
                    <ul class="space-y-1 text-blue-700 text-sm">
                        <li>• Use paper trading (port 7497) for testing</li>
                        <li>• Live trading requires port 7496</li>
                        <li>• Market data requires separate subscriptions</li>
                        <li>• Account minimum $10,000 for margin trading</li>
                        <li>• Professional API access recommended for algorithmic trading</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        lucide.createIcons();
    </script>
</body>
</html>