<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mathematical Engine Guide - Mathematical Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .math-bg {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .step-card {
            border-left: 4px solid #f093fb;
            transition: all 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .command-block {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Ubuntu Mono', 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 16px 0;
            position: relative;
            overflow-x: auto;
            border: 1px solid #f093fb;
        }
        .env-indicator {
            position: absolute;
            top: 5px;
            right: 60px;
            background: rgba(240, 147, 251, 0.8);
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(240, 147, 251, 0.2);
            border: 1px solid #f093fb;
            color: #f093fb;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            font-family: 'Ubuntu', sans-serif;
        }
        .copy-button:hover {
            background: rgba(240, 147, 251, 0.3);
        }
        .step-number {
            width: 50px;
            height: 50px;
            background: #f093fb;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 20px;
            margin-right: 20px;
            flex-shrink: 0;
            box-shadow: 0 4px 10px rgba(240, 147, 251, 0.3);
        }
        .warning-box {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .success-box {
            background: #dcfce7;
            border: 2px solid #16a34a;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .info-box {
            background: #dbeafe;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .math-box {
            background: #fdf2f8;
            border: 2px solid #f093fb;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .formula-card {
            background: linear-gradient(135deg, #fef7ff 0%, #fdf2f8 100%);
            border: 2px solid #f093fb;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
        }
        .terminal-prompt {
            color: #f093fb;
            font-weight: bold;
        }
        .verification-checklist {
            background: #fef7ff;
            border: 2px solid #f093fb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .checklist-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
            transition: background 0.2s;
        }
        .checklist-item:hover {
            background: #fdf2f8;
        }
        .formula-display {
            background: #1a1a1a;
            color: #f093fb;
            font-family: 'Ubuntu Mono', monospace;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
            border: 1px solid #f093fb;
        }
        .module-badge {
            display: inline-block;
            background: #f093fb;
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin: 3px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="math-bg text-white p-4 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <i data-lucide="calculator" class="w-8 h-8"></i>
                <div>
                    <h1 class="text-xl font-bold">Mathematical Engine Guide</h1>
                    <p class="text-sm opacity-90">Stage 3: Advanced Mathematical Analysis</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <i data-lucide="check-circle" class="w-5 h-5"></i>
                    <span class="text-sm">Data Collection Complete</span>
                </div>
                <a href="#quick-start" class="bg-white/20 px-4 py-2 rounded hover:bg-white/30 transition">Start Building</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="math-bg text-white py-20">
        <div class="max-w-7xl mx-auto text-center px-4">
            <h1 class="text-5xl font-bold mb-6">Mathematical Engine Development</h1>
            <p class="text-xl mb-8">Advanced Mathematical Analysis for Trading Intelligence</p>
            <div class="flex justify-center space-x-8">
                <div class="bg-white/20 rounded-lg p-6">
                    <i data-lucide="trending-up" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Statistical Analysis</h3>
                    <p class="text-sm">Advanced statistical methods</p>
                </div>
                <div class="bg-white/20 rounded-lg p-6">
                    <i data-lucide="git-merge" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Correlation Engine</h3>
                    <p class="text-sm">Multi-asset correlation analysis</p>
                </div>
                <div class="bg-white/20 rounded-lg p-6">
                    <i data-lucide="search" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Pattern Recognition</h3>
                    <p class="text-sm">Advanced pattern detection</p>
                </div>
                <div class="bg-white/20 rounded-lg p-6">
                    <i data-lucide="shield" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Risk Calculation</h3>
                    <p class="text-sm">Sophisticated risk metrics</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Prerequisites Check -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Pre-Development Verification</h2>

            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h3 class="text-xl font-bold mb-6">🔍 Verify Data Collection Agent Status</h3>

                <div class="command-block">
                    <div class="env-indicator">AstroA/</div>
                    <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Navigate to project and activate environment
cd ~/axmadcodes/AstroA && source venv/bin/activate

<span class="terminal-prompt">$</span> # Verify database has market data
PGPASSWORD='secure_password_123' psql -h localhost -U trading_user -d mathematical_trading -c "SELECT COUNT(*) as market_data_count FROM market_data;"

<span class="terminal-prompt">$</span> # Check data distribution by symbol
PGPASSWORD='secure_password_123' psql -h localhost -U trading_user -d mathematical_trading -c "SELECT symbol, COUNT(*) FROM market_data GROUP BY symbol ORDER BY COUNT(*) DESC LIMIT 10;"
                </div>

                <div class="warning-box">
                    <strong>⚠️ Requirements:</strong> Data Collection Agent must be working with 1000+ market data points in PostgreSQL before proceeding with Mathematical Engine development.
                </div>
            </div>
        </div>
    </section>

    <!-- Mathematical Formulas Overview -->
    <section id="quick-start" class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Mathematical Analysis Components</h2>

            <div class="grid md:grid-cols-2 gap-8 mb-12">
                <div class="formula-card">
                    <span class="module-badge">Statistical</span>
                    <h3 class="text-xl font-bold mb-4 text-pink-700">📊 Statistical Analysis Engine</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Moving averages (SMA, EMA, WMA)</li>
                        <li>• Standard deviation and variance</li>
                        <li>• Bollinger Bands calculation</li>
                        <li>• Z-score normalization</li>
                        <li>• Skewness and kurtosis</li>
                        <li>• Statistical significance testing</li>
                    </ul>
                </div>
                <div class="formula-card">
                    <span class="module-badge">Correlation</span>
                    <h3 class="text-xl font-bold mb-4 text-pink-700">🔗 Correlation Analysis Engine</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Pearson correlation coefficient</li>
                        <li>• Spearman rank correlation</li>
                        <li>• Rolling correlation windows</li>
                        <li>• Cross-correlation analysis</li>
                        <li>• Correlation matrix generation</li>
                        <li>• Cointegration testing</li>
                    </ul>
                </div>
                <div class="formula-card">
                    <span class="module-badge">Pattern</span>
                    <h3 class="text-xl font-bold mb-4 text-pink-700">🔍 Pattern Recognition Engine</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Candlestick pattern detection</li>
                        <li>• Support and resistance levels</li>
                        <li>• Trend line identification</li>
                        <li>• Chart pattern recognition</li>
                        <li>• Momentum oscillators</li>
                        <li>• Volume pattern analysis</li>
                    </ul>
                </div>
                <div class="formula-card">
                    <span class="module-badge">Risk</span>
                    <h3 class="text-xl font-bold mb-4 text-pink-700">🛡️ Risk Calculation Engine</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Value at Risk (VaR) calculation</li>
                        <li>• Conditional VaR (CVaR)</li>
                        <li>• Sharpe ratio optimization</li>
                        <li>• Maximum drawdown analysis</li>
                        <li>• Beta coefficient calculation</li>
                        <li>• Portfolio volatility metrics</li>
                    </ul>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <h3 class="text-xl font-bold mb-6">🧮 Mathematical Engine Architecture</h3>
                <div class="formula-display">
Database (Market Data) → Statistical Analyzer → Correlation Analyzer
                          ↓                        ↓
                    Pattern Recognizer ← Mathematical Engine ← Risk Calculator
                          ↓                        ↓
                    Feature Engineering → Trading Strategy Agent
                </div>
            </div>
        </div>
    </section>

    <!-- Implementation Steps -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Mathematical Engine Implementation</h2>

            <!-- Step 1: Read Mathematical Formulas -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">1</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Load and Review Mathematical Formulas</h3>

                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Review existing mathematical formulas
ls -la "figuring out/"

<span class="terminal-prompt">(venv) $</span> # Read mathematical formula files
cat "figuring out/math formula 1"
cat "figuring out/math formulas 2"
cat "figuring out/math formula 3"
                        </div>

                        <div class="info-box">
                            <strong>📋 Purpose:</strong> Review and integrate the existing mathematical formulas from your "figuring out" directory to build comprehensive analysis capabilities.
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Mathematical formula files located and readable</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Formulas understood and documented</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Create Mathematical Engine Structure -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">2</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Build Mathematical Engine Agent Structure</h3>

                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create mathematical engine directory structure
mkdir -p agents/mathematical_engine/analyzers/{statistical,correlation,pattern,risk}
mkdir -p agents/mathematical_engine/models/{time_series,regression,classification}
mkdir -p agents/mathematical_engine/calculators/{indicators,metrics,signals}
mkdir -p shared/mathematical/{formulas,utils,constants}

<span class="terminal-prompt">(venv) $</span> # Create Python package files
find agents/mathematical_engine/ -type d -exec touch {}/__init__.py \;
find shared/mathematical/ -type d -exec touch {}/__init__.py \;

<span class="terminal-prompt">(venv) $</span> # Display structure
tree agents/mathematical_engine/ shared/mathematical/ -I '__pycache__|*.pyc'
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Mathematical engine directories created</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Package structure established</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Implement Core Mathematical Libraries -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">3</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Implement Core Mathematical Components</h3>

                        <h4 class="font-semibold mb-3">Mathematical Constants and Utilities</h4>
                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create mathematical constants
cat > shared/mathematical/constants.py << 'EOF'
"""
Mathematical constants and configuration for trading analysis
"""
import numpy as np

# Statistical Constants
CONFIDENCE_LEVELS = {
    '90%': 1.645,
    '95%': 1.96,
    '99%': 2.576
}

# Technical Analysis Parameters
DEFAULT_PERIODS = {
    'short_ma': 9,
    'medium_ma': 21,
    'long_ma': 50,
    'bollinger_period': 20,
    'rsi_period': 14,
    'stochastic_period': 14
}

# Risk Management Constants
RISK_FREE_RATE = 0.02  # 2% annual risk-free rate
TRADING_DAYS_PER_YEAR = 252
HOURS_PER_DAY = 24

# Correlation Thresholds
CORRELATION_THRESHOLDS = {
    'strong_positive': 0.7,
    'moderate_positive': 0.3,
    'weak': 0.1,
    'moderate_negative': -0.3,
    'strong_negative': -0.7
}

# VaR Confidence Levels
VAR_CONFIDENCE_LEVELS = [0.90, 0.95, 0.99]

# Pattern Recognition Parameters
PATTERN_TOLERANCE = 0.02  # 2% tolerance for pattern matching
MIN_PATTERN_LENGTH = 5
MAX_PATTERN_LENGTH = 50

# Mathematical Constants
GOLDEN_RATIO = 1.618033988749
EULER_NUMBER = np.e
PI = np.pi
EOF
                        </div>

                        <h4 class="font-semibold mb-3 mt-6">Mathematical Utilities</h4>
                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create mathematical utilities
cat > shared/mathematical/utils.py << 'EOF'
"""
Mathematical utility functions for trading analysis
"""
import numpy as np
import pandas as pd
from typing import Union, List, Tuple, Optional
from scipy import stats
import warnings

def safe_divide(numerator: Union[float, np.ndarray],
                denominator: Union[float, np.ndarray],
                default: float = 0.0) -> Union[float, np.ndarray]:
    """Safely divide two numbers, handling division by zero"""
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        result = np.divide(numerator, denominator)
        if isinstance(result, np.ndarray):
            result[np.isnan(result)] = default
            result[np.isinf(result)] = default
        elif np.isnan(result) or np.isinf(result):
            result = default
    return result

def normalize_series(series: pd.Series, method: str = 'zscore') -> pd.Series:
    """Normalize a pandas series using various methods"""
    if method == 'zscore':
        return (series - series.mean()) / series.std()
    elif method == 'minmax':
        return (series - series.min()) / (series.max() - series.min())
    elif method == 'robust':
        median = series.median()
        mad = np.median(np.abs(series - median))
        return (series - median) / mad
    else:
        raise ValueError(f"Unknown normalization method: {method}")

def rolling_window(data: np.ndarray, window: int) -> np.ndarray:
    """Create rolling windows from array data"""
    shape = data.shape[:-1] + (data.shape[-1] - window + 1, window)
    strides = data.strides + (data.strides[-1],)
    return np.lib.stride_tricks.as_strided(data, shape=shape, strides=strides)

def calculate_returns(prices: pd.Series, method: str = 'simple') -> pd.Series:
    """Calculate returns from price series"""
    if method == 'simple':
        return prices.pct_change()
    elif method == 'log':
        return np.log(prices / prices.shift(1))
    else:
        raise ValueError(f"Unknown return calculation method: {method}")

def detect_outliers(data: pd.Series, method: str = 'iqr', threshold: float = 1.5) -> pd.Series:
    """Detect outliers in data using various methods"""
    if method == 'iqr':
        Q1 = data.quantile(0.25)
        Q3 = data.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - threshold * IQR
        upper_bound = Q3 + threshold * IQR
        return (data < lower_bound) | (data > upper_bound)
    elif method == 'zscore':
        z_scores = np.abs(stats.zscore(data))
        return z_scores > threshold
    else:
        raise ValueError(f"Unknown outlier detection method: {method}")

def smooth_series(series: pd.Series, method: str = 'ewm', alpha: float = 0.1) -> pd.Series:
    """Smooth a time series using various methods"""
    if method == 'ewm':
        return series.ewm(alpha=alpha).mean()
    elif method == 'sma':
        window = int(1 / alpha)
        return series.rolling(window=window).mean()
    elif method == 'savgol':
        from scipy.signal import savgol_filter
        window_length = min(len(series), 51)
        if window_length % 2 == 0:
            window_length -= 1
        return pd.Series(savgol_filter(series, window_length, 3), index=series.index)
    else:
        raise ValueError(f"Unknown smoothing method: {method}")

def calculate_confidence_interval(data: pd.Series, confidence: float = 0.95) -> Tuple[float, float]:
    """Calculate confidence interval for data"""
    mean = data.mean()
    sem = stats.sem(data)
    h = sem * stats.t.ppf((1 + confidence) / 2., len(data) - 1)
    return mean - h, mean + h

def validate_data_quality(data: pd.DataFrame, min_data_points: int = 100) -> dict:
    """Validate data quality for mathematical analysis"""
    results = {
        'total_points': len(data),
        'missing_values': data.isnull().sum().sum(),
        'duplicate_rows': data.duplicated().sum(),
        'data_quality_score': 0.0,
        'warnings': []
    }

    # Check minimum data points
    if results['total_points'] < min_data_points:
        results['warnings'].append(f"Insufficient data points: {results['total_points']} < {min_data_points}")

    # Check missing values
    missing_percentage = (results['missing_values'] / (len(data) * len(data.columns))) * 100
    if missing_percentage > 5:
        results['warnings'].append(f"High missing values: {missing_percentage:.1f}%")

    # Calculate data quality score
    quality_score = 100
    quality_score -= min(missing_percentage * 2, 50)  # Penalty for missing values
    quality_score -= min((results['duplicate_rows'] / len(data)) * 100, 20)  # Penalty for duplicates

    results['data_quality_score'] = max(quality_score, 0)

    return results
EOF
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Mathematical constants defined</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Utility functions implemented</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>No syntax errors in Python files</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Statistical Analysis Engine -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">4</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Build Statistical Analysis Engine</h3>

                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create statistical analysis engine
cat > agents/mathematical_engine/analyzers/statistical/statistical_analyzer.py << 'EOF'
"""
Statistical Analysis Engine for Market Data
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from scipy import stats
import logging

from shared.mathematical.utils import (
    safe_divide, normalize_series, calculate_returns,
    detect_outliers, validate_data_quality
)
from shared.mathematical.constants import (
    DEFAULT_PERIODS, CONFIDENCE_LEVELS, TRADING_DAYS_PER_YEAR
)

class StatisticalAnalyzer:
    """Advanced statistical analysis for market data"""

    def __init__(self):
        self.logger = logging.getLogger("statistical_analyzer")

    def calculate_moving_averages(self, data: pd.Series, periods: List[int] = None) -> pd.DataFrame:
        """Calculate multiple moving averages"""
        if periods is None:
            periods = [DEFAULT_PERIODS['short_ma'], DEFAULT_PERIODS['medium_ma'], DEFAULT_PERIODS['long_ma']]

        result = pd.DataFrame(index=data.index)

        for period in periods:
            # Simple Moving Average
            result[f'sma_{period}'] = data.rolling(window=period).mean()

            # Exponential Moving Average
            result[f'ema_{period}'] = data.ewm(span=period).mean()

            # Weighted Moving Average
            weights = np.arange(1, period + 1)
            result[f'wma_{period}'] = data.rolling(window=period).apply(
                lambda x: np.dot(x, weights) / weights.sum(), raw=True
            )

        return result

    def calculate_bollinger_bands(self, data: pd.Series, period: int = None,
                                 std_dev: float = 2.0) -> pd.DataFrame:
        """Calculate Bollinger Bands"""
        if period is None:
            period = DEFAULT_PERIODS['bollinger_period']

        sma = data.rolling(window=period).mean()
        std = data.rolling(window=period).std()

        return pd.DataFrame({
            'bb_middle': sma,
            'bb_upper': sma + (std * std_dev),
            'bb_lower': sma - (std * std_dev),
            'bb_width': (std * std_dev * 2) / sma,
            'bb_position': (data - sma) / (std * std_dev)
        }, index=data.index)

    def calculate_statistical_moments(self, data: pd.Series, window: int = 50) -> pd.DataFrame:
        """Calculate rolling statistical moments"""
        return pd.DataFrame({
            'mean': data.rolling(window=window).mean(),
            'std': data.rolling(window=window).std(),
            'variance': data.rolling(window=window).var(),
            'skewness': data.rolling(window=window).skew(),
            'kurtosis': data.rolling(window=window).kurt(),
            'median': data.rolling(window=window).median(),
            'quantile_25': data.rolling(window=window).quantile(0.25),
            'quantile_75': data.rolling(window=window).quantile(0.75)
        }, index=data.index)

    def calculate_z_scores(self, data: pd.Series, window: int = 50) -> pd.Series:
        """Calculate rolling Z-scores"""
        rolling_mean = data.rolling(window=window).mean()
        rolling_std = data.rolling(window=window).std()
        return safe_divide(data - rolling_mean, rolling_std)

    def perform_normality_tests(self, data: pd.Series) -> Dict[str, Dict]:
        """Perform various normality tests"""
        # Remove NaN values
        clean_data = data.dropna()

        if len(clean_data) < 8:
            return {"error": "Insufficient data for normality tests"}

        results = {}

        # Shapiro-Wilk test (for small samples)
        if len(clean_data) <= 5000:
            stat, p_value = stats.shapiro(clean_data)
            results['shapiro_wilk'] = {
                'statistic': stat,
                'p_value': p_value,
                'is_normal': p_value > 0.05
            }

        # Kolmogorov-Smirnov test
        stat, p_value = stats.kstest(clean_data, 'norm', args=(clean_data.mean(), clean_data.std()))
        results['kolmogorov_smirnov'] = {
            'statistic': stat,
            'p_value': p_value,
            'is_normal': p_value > 0.05
        }

        # Anderson-Darling test
        stat, critical_values, significance_levels = stats.anderson(clean_data, dist='norm')
        results['anderson_darling'] = {
            'statistic': stat,
            'critical_values': critical_values.tolist(),
            'significance_levels': significance_levels.tolist(),
            'is_normal': stat < critical_values[2]  # 5% significance level
        }

        return results

    def calculate_technical_indicators(self, ohlcv_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate comprehensive technical indicators"""
        close = ohlcv_data['close_price']
        high = ohlcv_data['high_price']
        low = ohlcv_data['low_price']
        volume = ohlcv_data['volume']

        indicators = pd.DataFrame(index=ohlcv_data.index)

        # RSI (Relative Strength Index)
        delta = close.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = safe_divide(gain, loss)
        indicators['rsi'] = 100 - (100 / (1 + rs))

        # MACD (Moving Average Convergence Divergence)
        ema_12 = close.ewm(span=12).mean()
        ema_26 = close.ewm(span=26).mean()
        indicators['macd'] = ema_12 - ema_26
        indicators['macd_signal'] = indicators['macd'].ewm(span=9).mean()
        indicators['macd_histogram'] = indicators['macd'] - indicators['macd_signal']

        # Stochastic Oscillator
        lowest_low = low.rolling(window=14).min()
        highest_high = high.rolling(window=14).max()
        indicators['stoch_k'] = 100 * safe_divide(
            close - lowest_low,
            highest_high - lowest_low
        )
        indicators['stoch_d'] = indicators['stoch_k'].rolling(window=3).mean()

        # Average True Range (ATR)
        tr1 = high - low
        tr2 = np.abs(high - close.shift(1))
        tr3 = np.abs(low - close.shift(1))
        true_range = np.maximum(tr1, np.maximum(tr2, tr3))
        indicators['atr'] = true_range.rolling(window=14).mean()

        # Volume indicators
        indicators['volume_sma'] = volume.rolling(window=20).mean()
        indicators['volume_ratio'] = safe_divide(volume, indicators['volume_sma'])

        return indicators

    def detect_regime_changes(self, data: pd.Series, window: int = 50) -> pd.DataFrame:
        """Detect market regime changes using statistical methods"""
        # Calculate rolling statistics
        rolling_mean = data.rolling(window=window).mean()
        rolling_std = data.rolling(window=window).std()
        rolling_vol = calculate_returns(data).rolling(window=window).std() * np.sqrt(TRADING_DAYS_PER_YEAR)

        # Regime indicators
        regime_data = pd.DataFrame({
            'price_trend': np.where(data > rolling_mean, 1, -1),
            'volatility_regime': np.where(rolling_vol > rolling_vol.median(), 1, 0),
            'momentum': data.pct_change(window),
            'mean_reversion_signal': self.calculate_z_scores(data, window)
        }, index=data.index)

        # Composite regime score
        regime_data['regime_score'] = (
            regime_data['price_trend'] * 0.4 +
            regime_data['volatility_regime'] * 0.3 +
            np.sign(regime_data['momentum']) * 0.3
        )

        return regime_data

    def perform_comprehensive_analysis(self, market_data: pd.DataFrame,
                                     symbol: str) -> Dict[str, Union[pd.DataFrame, Dict, float]]:
        """Perform comprehensive statistical analysis on market data"""
        self.logger.info(f"Starting comprehensive statistical analysis for {symbol}")

        # Data quality check
        data_quality = validate_data_quality(market_data)
        if data_quality['data_quality_score'] < 50:
            self.logger.warning(f"Low data quality score: {data_quality['data_quality_score']}")

        close_prices = market_data['close_price']
        returns = calculate_returns(close_prices)

        analysis_results = {
            'symbol': symbol,
            'data_quality': data_quality,
            'basic_stats': {
                'count': len(market_data),
                'mean_price': close_prices.mean(),
                'std_price': close_prices.std(),
                'min_price': close_prices.min(),
                'max_price': close_prices.max(),
                'current_price': close_prices.iloc[-1] if len(close_prices) > 0 else None
            },
            'return_stats': {
                'mean_return': returns.mean(),
                'std_return': returns.std(),
                'skewness': returns.skew(),
                'kurtosis': returns.kurt(),
                'annualized_volatility': returns.std() * np.sqrt(TRADING_DAYS_PER_YEAR)
            },
            'moving_averages': self.calculate_moving_averages(close_prices),
            'bollinger_bands': self.calculate_bollinger_bands(close_prices),
            'statistical_moments': self.calculate_statistical_moments(close_prices),
            'technical_indicators': self.calculate_technical_indicators(market_data),
            'regime_analysis': self.detect_regime_changes(close_prices),
            'normality_tests': self.perform_normality_tests(returns),
            'outlier_detection': detect_outliers(returns)
        }

        self.logger.info(f"Completed statistical analysis for {symbol}")
        return analysis_results
EOF
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Statistical analyzer implemented</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Technical indicators calculated</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Statistical tests included</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testing Section -->
    <section class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Mathematical Engine Testing</h2>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <h3 class="text-xl font-bold mb-6">🧪 Comprehensive Testing Framework</h3>

                <div class="command-block">
                    <div class="env-indicator">venv</div>
                    <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create mathematical engine test script
cat > test_mathematical_engine.py << 'EOF'
#!/usr/bin/env python3
"""
Test script for Mathematical Engine components
"""
import sys
import os
import asyncio
import pandas as pd
import psycopg2
from datetime import datetime

sys.path.append(os.getcwd())

from agents.mathematical_engine.analyzers.statistical.statistical_analyzer import StatisticalAnalyzer
from shared.mathematical.utils import validate_data_quality

async def test_mathematical_engine():
    """Test the mathematical engine components"""
    print("🚀 Testing Mathematical Engine")
    print("=" * 50)

    try:
        # Connect to database and fetch sample data
        print("📊 Connecting to database...")
        conn = psycopg2.connect(
            host='localhost',
            database='mathematical_trading',
            user='trading_user',
            password='secure_password_123'
        )

        # Fetch market data for testing
        query = """
        SELECT symbol, timestamp, open_price, high_price, low_price, close_price, volume
        FROM market_data
        WHERE symbol = 'BTCUSDT'
        ORDER BY timestamp DESC
        LIMIT 1000
        """

        market_data = pd.read_sql(query, conn, parse_dates=['timestamp'])
        print(f"✅ Loaded {len(market_data)} data points for testing")

        # Test Statistical Analyzer
        print("\n🧮 Testing Statistical Analyzer...")
        analyzer = StatisticalAnalyzer()

        if len(market_data) > 50:  # Ensure sufficient data
            analysis_results = analyzer.perform_comprehensive_analysis(market_data, 'BTCUSDT')

            print(f"✅ Basic stats calculated: Mean price = ${analysis_results['basic_stats']['mean_price']:.2f}")
            print(f"✅ Technical indicators: RSI range = {analysis_results['technical_indicators']['rsi'].min():.1f} - {analysis_results['technical_indicators']['rsi'].max():.1f}")
            print(f"✅ Bollinger Bands calculated with {len(analysis_results['bollinger_bands'])} periods")
            print(f"✅ Statistical moments calculated")
            print(f"✅ Normality tests completed: {list(analysis_results['normality_tests'].keys())}")

            # Data quality assessment
            quality_score = analysis_results['data_quality']['data_quality_score']
            print(f"✅ Data quality score: {quality_score:.1f}/100")

        else:
            print("⚠️ Insufficient data for comprehensive testing")

        conn.close()
        print("\n🎉 Mathematical Engine testing completed successfully!")
        return True

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_mathematical_engine())
    sys.exit(0 if result else 1)
EOF

<span class="terminal-prompt">(venv) $</span> # Make test executable and run
chmod +x test_mathematical_engine.py
python test_mathematical_engine.py
                </div>

                <div class="success-box">
                    <h4 class="font-bold text-green-800 mb-2">🎯 Expected Results</h4>
                    <p class="text-green-700">The test should successfully analyze market data, calculate statistical indicators, and provide comprehensive mathematical insights with high data quality scores.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Next Steps -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Remaining Components</h2>

            <div class="grid md:grid-cols-2 gap-8">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-bold mb-4">🔗 Next: Correlation Analysis Engine</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Multi-asset correlation matrices</li>
                        <li>• Rolling correlation windows</li>
                        <li>• Cross-correlation analysis</li>
                        <li>• Cointegration testing</li>
                    </ul>
                </div>
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-bold mb-4">🔍 Then: Pattern Recognition & Risk</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Chart pattern detection</li>
                        <li>• Candlestick patterns</li>
                        <li>• Value at Risk (VaR) calculation</li>
                        <li>• Portfolio risk metrics</li>
                    </ul>
                </div>
            </div>

            <div class="math-box mt-8">
                <h4 class="font-bold text-pink-800 mb-2">⚠️ API Key Notice</h4>
                <p class="text-pink-700">No external API keys required for mathematical analysis. All calculations use your existing market data from PostgreSQL.</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="math-bg text-white py-8">
        <div class="max-w-7xl mx-auto text-center px-4">
            <h3 class="text-xl font-bold mb-4">🧮 Mathematical Engine Foundation Complete!</h3>
            <p class="mb-6">Statistical analysis engine ready. Continue building correlation and pattern recognition!</p>
            <div class="flex justify-center space-x-4">
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="git-merge" class="w-4 h-4 inline mr-2"></i>
                    Build Correlation Engine
                </button>
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                    Pattern Recognition
                </button>
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="shield" class="w-4 h-4 inline mr-2"></i>
                    Risk Calculator
                </button>
            </div>
            <p class="text-sm mt-6 opacity-75">© 2025 Mathematical Trading System - Mathematical Engine Guide</p>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Copy to clipboard function
        function copyToClipboard(button) {
            const codeBlock = button.parentElement;
            const code = codeBlock.textContent.replace('Copy', '').trim();

            navigator.clipboard.writeText(code).then(() => {
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = 'rgba(240, 147, 251, 0.4)';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'rgba(240, 147, 251, 0.2)';
                }, 2000);
            });
        }

        // Progress tracking
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateProgress();
            });
        });

        function updateProgress() {
            const totalCheckboxes = document.querySelectorAll('input[type="checkbox"]').length;
            const checkedBoxes = document.querySelectorAll('input[type="checkbox"]:checked').length;
            const progress = (checkedBoxes / totalCheckboxes) * 100;

            document.title = `Mathematical Engine Guide - ${progress.toFixed(0)}% Complete`;
        }

        updateProgress();
    </script>
</body>
</html>