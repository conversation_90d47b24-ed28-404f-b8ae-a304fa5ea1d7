<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-Time Data Feeds Guide - AstroA Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .data-bg {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
        }
        .step-card {
            border-left: 4px solid #7c3aed;
            transition: all 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .command-block {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Ubuntu Mono', 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 16px 0;
            position: relative;
            overflow-x: auto;
            border: 1px solid #7c3aed;
        }
        .env-indicator {
            position: absolute;
            top: 5px;
            right: 60px;
            background: rgba(124, 58, 237, 0.8);
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(124, 58, 237, 0.2);
            border: 1px solid #7c3aed;
            color: #7c3aed;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        .copy-button:hover {
            background: rgba(124, 58, 237, 0.3);
        }
        .warning-box {
            background: linear-gradient(45deg, #fef3c7, #fde68a);
            border: 2px solid #f59e0b;
            color: #92400e;
        }
        .success-box {
            background: linear-gradient(45deg, #d1fae5, #a7f3d0);
            border: 2px solid #059669;
            color: #065f46;
        }
        .info-box {
            background: linear-gradient(45deg, #dbeafe, #bfdbfe);
            border: 2px solid #2563eb;
            color: #1e40af;
        }
        .code-file {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 8px;
            margin: 16px 0;
        }
        .file-header {
            background: #1e293b;
            padding: 8px 16px;
            border-bottom: 1px solid #334155;
            font-family: 'Ubuntu Mono', monospace;
            color: #94a3b8;
            font-size: 14px;
        }
        .file-content {
            padding: 16px;
            color: #e2e8f0;
            font-family: 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .nav-tabs {
            display: flex;
            background: #1e293b;
            border-radius: 8px 8px 0 0;
        }
        .nav-tab {
            padding: 12px 24px;
            cursor: pointer;
            background: #334155;
            color: #94a3b8;
            border-right: 1px solid #475569;
            transition: all 0.3s ease;
        }
        .nav-tab:first-child {
            border-radius: 8px 0 0 0;
        }
        .nav-tab:last-child {
            border-right: none;
            border-radius: 0 8px 0 0;
        }
        .nav-tab.active {
            background: #7c3aed;
            color: white;
        }
        .nav-tab:hover {
            background: #475569;
        }
        .tab-content {
            display: none;
            background: #0f172a;
            padding: 24px;
            border-radius: 0 0 8px 8px;
            border: 1px solid #334155;
            border-top: none;
        }
        .tab-content.active {
            display: block;
        }
        .provider-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .provider-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .provider-card:hover {
            border-color: #7c3aed;
            box-shadow: 0 8px 25px rgba(124, 58, 237, 0.1);
        }
        .provider-card.recommended {
            border-color: #7c3aed;
            background: linear-gradient(45deg, #f3f4f6, #e5e7eb);
        }
        .latency-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .latency-low { background-color: #10b981; }
        .latency-medium { background-color: #f59e0b; }
        .latency-high { background-color: #ef4444; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="data-bg text-white py-8">
        <div class="container mx-auto px-6">
            <div class="flex items-center space-x-4">
                <i data-lucide="radio" class="w-8 h-8"></i>
                <div>
                    <h1 class="text-4xl font-bold">Real-Time Data Feeds Guide</h1>
                    <p class="text-xl opacity-90">Power Your AstroA System with Live Market Data</p>
                </div>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-6 py-8">
        <!-- Navigation -->
        <div class="mb-8">
            <div class="nav-tabs">
                <div class="nav-tab active" onclick="showTab('overview')">
                    <i data-lucide="info" class="w-4 h-4 inline mr-2"></i>Overview
                </div>
                <div class="nav-tab" onclick="showTab('providers')">
                    <i data-lucide="server" class="w-4 h-4 inline mr-2"></i>Data Providers
                </div>
                <div class="nav-tab" onclick="showTab('websockets')">
                    <i data-lucide="wifi" class="w-4 h-4 inline mr-2"></i>WebSocket Feeds
                </div>
                <div class="nav-tab" onclick="showTab('integration')">
                    <i data-lucide="git-merge" class="w-4 h-4 inline mr-2"></i>AstroA Integration
                </div>
                <div class="nav-tab" onclick="showTab('deployment')">
                    <i data-lucide="rocket" class="w-4 h-4 inline mr-2"></i>Deployment
                </div>
                <div class="nav-tab" onclick="showTab('monitoring')">
                    <i data-lucide="activity" class="w-4 h-4 inline mr-2"></i>Monitoring
                </div>
            </div>

            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">📡 Real-Time Data Overview</h2>

                <div class="info-box p-6 rounded-lg mb-6">
                    <div class="flex items-center mb-3">
                        <i data-lucide="zap" class="w-6 h-6 mr-3"></i>
                        <h3 class="text-xl font-bold">⚡ Why Real-Time Data is Critical</h3>
                    </div>
                    <ul class="space-y-2">
                        <li>🔄 Immediate reaction to market movements</li>
                        <li>📊 Accurate strategy signal generation</li>
                        <li>⚖️ Precise risk management execution</li>
                        <li>🎯 Optimal entry and exit timing</li>
                        <li>📈 Competitive advantage in volatile markets</li>
                    </ul>
                </div>

                <div class="grid md:grid-cols-3 gap-6 mb-8">
                    <div class="step-card bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <span class="latency-indicator latency-low"></span>
                            Low Latency (&lt;100ms)
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>🚀 WebSocket connections</li>
                            <li>📊 Real-time price updates</li>
                            <li>⚡ Instant signal generation</li>
                            <li>🎯 Optimal for scalping/momentum</li>
                        </ul>
                        <p class="text-sm text-purple-600 mt-4 font-semibold">Best for: High-frequency strategies</p>
                    </div>

                    <div class="step-card bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <span class="latency-indicator latency-medium"></span>
                            Medium Latency (1-5s)
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>🔄 REST API polling</li>
                            <li>📈 Regular price updates</li>
                            <li>⚖️ Good for swing trading</li>
                            <li>💰 Cost-effective option</li>
                        </ul>
                        <p class="text-sm text-amber-600 mt-4 font-semibold">Best for: Medium-term strategies</p>
                    </div>

                    <div class="step-card bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <span class="latency-indicator latency-high"></span>
                            High Latency (15m+)
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>📅 Delayed/historical data</li>
                            <li>🔍 Good for backtesting</li>
                            <li>📊 Fundamental analysis</li>
                            <li>🆓 Often free</li>
                        </ul>
                        <p class="text-sm text-red-600 mt-4 font-semibold">Best for: Research & development</p>
                    </div>
                </div>

                <div class="success-box p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-3">
                        <i data-lucide="target" class="w-6 h-6 inline mr-2"></i>
                        🎯 AstroA Data Requirements
                    </h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <p class="font-semibold mb-2">Cryptocurrency Data:</p>
                            <ul class="space-y-1 text-sm">
                                <li>• OHLCV (1m, 5m, 1h, 4h, 1d timeframes)</li>
                                <li>• Order book depth (L2 data)</li>
                                <li>• Trade tick data</li>
                                <li>• Volume analysis</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-semibold mb-2">Alternative Data:</p>
                            <ul class="space-y-1 text-sm">
                                <li>• News sentiment feeds</li>
                                <li>• Social media sentiment</li>
                                <li>• Market correlation data</li>
                                <li>• Economic indicators</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Providers Tab -->
            <div id="providers" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🌐 Data Provider Comparison</h2>

                <div class="provider-grid">
                    <!-- Binance WebSocket -->
                    <div class="provider-card recommended">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl">B</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Binance WebSocket</h3>
                                <span class="text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded">🏆 RECOMMENDED</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="font-semibold text-gray-700">✅ Pros:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Free real-time WebSocket</li>
                                    <li>• Ultra-low latency (&lt;50ms)</li>
                                    <li>• Comprehensive crypto coverage</li>
                                    <li>• High reliability</li>
                                    <li>• Order book depth</li>
                                </ul>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-700">⚠️ Cons:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Crypto only</li>
                                    <li>• Rate limits on connections</li>
                                </ul>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <p class="text-sm font-medium text-purple-700">💰 Cost: FREE</p>
                                <p class="text-sm text-gray-600">🔗 binance-docs.github.io</p>
                                <p class="text-sm text-gray-600">⚡ Latency: &lt;50ms</p>
                            </div>
                        </div>
                    </div>

                    <!-- CoinGecko -->
                    <div class="provider-card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl">C</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">CoinGecko API</h3>
                                <span class="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">RELIABLE</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="font-semibold text-gray-700">✅ Pros:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Comprehensive market data</li>
                                    <li>• Historical data access</li>
                                    <li>• Market cap and volume data</li>
                                    <li>• Good free tier</li>
                                </ul>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-700">⚠️ Cons:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Higher latency (REST only)</li>
                                    <li>• Rate limits on free tier</li>
                                    <li>• No real-time WebSocket</li>
                                </ul>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <p class="text-sm font-medium text-green-700">💰 Cost: FREE tier, $129+/mo pro</p>
                                <p class="text-sm text-gray-600">🔗 coingecko.com/api</p>
                                <p class="text-sm text-gray-600">⚡ Latency: 1-5 seconds</p>
                            </div>
                        </div>
                    </div>

                    <!-- Alpha Vantage -->
                    <div class="provider-card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl">A</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Alpha Vantage</h3>
                                <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">PREMIUM</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="font-semibold text-gray-700">✅ Pros:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Stocks, forex, crypto</li>
                                    <li>• Technical indicators built-in</li>
                                    <li>• High-quality historical data</li>
                                    <li>• Economic data</li>
                                </ul>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-700">⚠️ Cons:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Expensive for real-time</li>
                                    <li>• Limited free tier</li>
                                    <li>• Higher latency</li>
                                </ul>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <p class="text-sm font-medium text-blue-700">💰 Cost: FREE limited, $49+/mo</p>
                                <p class="text-sm text-gray-600">🔗 alphavantage.co</p>
                                <p class="text-sm text-gray-600">⚡ Latency: 1-15 minutes</p>
                            </div>
                        </div>
                    </div>

                    <!-- Polygon.io -->
                    <div class="provider-card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl">P</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Polygon.io</h3>
                                <span class="text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded">ENTERPRISE</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="font-semibold text-gray-700">✅ Pros:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Ultra-low latency WebSocket</li>
                                    <li>• Stocks, options, forex, crypto</li>
                                    <li>• Level 2 market data</li>
                                    <li>• Professional grade</li>
                                </ul>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-700">⚠️ Cons:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Expensive ($99+/month)</li>
                                    <li>• Overkill for crypto-only</li>
                                    <li>• Complex setup</li>
                                </ul>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <p class="text-sm font-medium text-purple-700">💰 Cost: $99-$399+/month</p>
                                <p class="text-sm text-gray-600">🔗 polygon.io</p>
                                <p class="text-sm text-gray-600">⚡ Latency: &lt;10ms</p>
                            </div>
                        </div>
                    </div>

                    <!-- FinnHub -->
                    <div class="provider-card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl">F</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">FinnHub</h3>
                                <span class="text-sm bg-red-100 text-red-800 px-2 py-1 rounded">BALANCED</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="font-semibold text-gray-700">✅ Pros:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Good free tier</li>
                                    <li>• WebSocket support</li>
                                    <li>• News and sentiment data</li>
                                    <li>• Multiple asset classes</li>
                                </ul>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-700">⚠️ Cons:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Limited crypto coverage</li>
                                    <li>• Rate limits</li>
                                    <li>• Moderate latency</li>
                                </ul>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <p class="text-sm font-medium text-red-700">💰 Cost: FREE tier, $59+/mo</p>
                                <p class="text-sm text-gray-600">🔗 finnhub.io</p>
                                <p class="text-sm text-gray-600">⚡ Latency: 100-500ms</p>
                            </div>
                        </div>
                    </div>

                    <!-- Twelve Data -->
                    <div class="provider-card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl">T</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Twelve Data</h3>
                                <span class="text-sm bg-indigo-100 text-indigo-800 px-2 py-1 rounded">AFFORDABLE</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="font-semibold text-gray-700">✅ Pros:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• WebSocket real-time data</li>
                                    <li>• Good pricing tiers</li>
                                    <li>• Multiple asset classes</li>
                                    <li>• Technical indicators API</li>
                                </ul>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-700">⚠️ Cons:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Limited free tier</li>
                                    <li>• Newer provider</li>
                                    <li>• Less crypto coverage</li>
                                </ul>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <p class="text-sm font-medium text-indigo-700">💰 Cost: FREE basic, $8+/mo</p>
                                <p class="text-sm text-gray-600">🔗 twelvedata.com</p>
                                <p class="text-sm text-gray-600">⚡ Latency: 100ms-1s</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="warning-box p-6 rounded-lg mt-8">
                    <h3 class="text-xl font-bold mb-3">
                        <i data-lucide="lightbulb" class="w-6 h-6 inline mr-2"></i>
                        🎯 Recommendation for AstroA
                    </h3>
                    <p class="mb-3">
                        <strong>Start with Binance WebSocket</strong> for crypto data, supplemented by <strong>CoinGecko</strong> for market overview:
                    </p>
                    <ul class="space-y-1">
                        <li>✅ Binance WebSocket: Real-time OHLCV, order book, trades</li>
                        <li>✅ CoinGecko API: Market cap, volume, historical data</li>
                        <li>✅ Cost: FREE for both</li>
                        <li>✅ Perfect match for your crypto-focused strategies</li>
                    </ul>
                </div>
            </div>

            <!-- WebSocket Implementation Tab -->
            <div id="websockets" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">📡 WebSocket Implementation</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Binance WebSocket Client</h3>

                    <div class="code-file">
                        <div class="file-header">agents/data_feeds/binance_websocket_client.py</div>
                        <div class="file-content">#!/usr/bin/env python3
"""
Binance WebSocket Client for Real-time Data Feeds
Integrates with AstroA data collection system
"""

import asyncio
import json
import logging
import websockets
from datetime import datetime
from typing import Dict, List, Callable, Optional
import pandas as pd
from dataclasses import dataclass

@dataclass
class MarketData:
    """Market data structure"""
    symbol: str
    price: float
    volume: float
    timestamp: datetime
    bid: Optional[float] = None
    ask: Optional[float] = None
    high_24h: Optional[float] = None
    low_24h: Optional[float] = None

class BinanceWebSocketClient:
    """
    Real-time WebSocket client for Binance
    Streams live market data to AstroA system
    """

    def __init__(self, symbols: List[str], on_message_callback: Callable = None):
        self.symbols = [symbol.lower().replace('/', '') for symbol in symbols]
        self.logger = logging.getLogger(__name__)
        self.on_message_callback = on_message_callback
        self.connections = {}
        self.is_running = False

        # WebSocket URLs
        self.base_url = "wss://stream.binance.com:9443/ws"
        self.combined_url = "wss://stream.binance.com:9443/stream"

    async def start(self):
        """Start WebSocket connections"""
        self.logger.info(f"Starting Binance WebSocket for {len(self.symbols)} symbols")
        self.is_running = True

        # Create tasks for different data types
        tasks = []

        # Ticker data (24hr statistics)
        tasks.append(self._connect_ticker_stream())

        # Trade data (individual trades)
        tasks.append(self._connect_trade_stream())

        # Order book data (depth updates)
        tasks.append(self._connect_depth_stream())

        # Kline data (OHLCV candles)
        tasks.append(self._connect_kline_stream())

        # Run all streams concurrently
        await asyncio.gather(*tasks)

    async def stop(self):
        """Stop all WebSocket connections"""
        self.logger.info("Stopping Binance WebSocket connections")
        self.is_running = False

        for connection in self.connections.values():
            if connection and not connection.closed:
                await connection.close()

    async def _connect_ticker_stream(self):
        """Connect to 24hr ticker statistics stream"""
        stream_names = [f"{symbol}@ticker" for symbol in self.symbols]
        combined_stream = "/".join(stream_names)
        url = f"{self.combined_url}?streams={combined_stream}"

        try:
            async with websockets.connect(url) as websocket:
                self.connections['ticker'] = websocket
                self.logger.info("Connected to Binance ticker stream")

                async for message in websocket:
                    if not self.is_running:
                        break

                    data = json.loads(message)
                    await self._process_ticker_message(data)

        except Exception as e:
            self.logger.error(f"Ticker stream error: {str(e)}")

    async def _connect_trade_stream(self):
        """Connect to individual trade stream"""
        stream_names = [f"{symbol}@trade" for symbol in self.symbols]
        combined_stream = "/".join(stream_names)
        url = f"{self.combined_url}?streams={combined_stream}"

        try:
            async with websockets.connect(url) as websocket:
                self.connections['trade'] = websocket
                self.logger.info("Connected to Binance trade stream")

                async for message in websocket:
                    if not self.is_running:
                        break

                    data = json.loads(message)
                    await self._process_trade_message(data)

        except Exception as e:
            self.logger.error(f"Trade stream error: {str(e)}")

    async def _connect_depth_stream(self):
        """Connect to order book depth stream"""
        stream_names = [f"{symbol}@depth20@100ms" for symbol in self.symbols]
        combined_stream = "/".join(stream_names)
        url = f"{self.combined_url}?streams={combined_stream}"

        try:
            async with websockets.connect(url) as websocket:
                self.connections['depth'] = websocket
                self.logger.info("Connected to Binance depth stream")

                async for message in websocket:
                    if not self.is_running:
                        break

                    data = json.loads(message)
                    await self._process_depth_message(data)

        except Exception as e:
            self.logger.error(f"Depth stream error: {str(e)}")

    async def _connect_kline_stream(self):
        """Connect to kline (OHLCV) stream"""
        # Multiple timeframes
        timeframes = ['1m', '5m', '1h', '4h', '1d']
        stream_names = []

        for symbol in self.symbols:
            for tf in timeframes:
                stream_names.append(f"{symbol}@kline_{tf}")

        combined_stream = "/".join(stream_names)
        url = f"{self.combined_url}?streams={combined_stream}"

        try:
            async with websockets.connect(url) as websocket:
                self.connections['kline'] = websocket
                self.logger.info("Connected to Binance kline stream")

                async for message in websocket:
                    if not self.is_running:
                        break

                    data = json.loads(message)
                    await self._process_kline_message(data)

        except Exception as e:
            self.logger.error(f"Kline stream error: {str(e)}")

    async def _process_ticker_message(self, data: Dict):
        """Process 24hr ticker statistics"""
        if 'data' in data:
            ticker = data['data']
            market_data = MarketData(
                symbol=ticker['s'],
                price=float(ticker['c']),  # Current price
                volume=float(ticker['v']),  # 24hr volume
                timestamp=datetime.fromtimestamp(ticker['E'] / 1000),
                high_24h=float(ticker['h']),
                low_24h=float(ticker['l'])
            )

            # Send to AstroA data collection system
            if self.on_message_callback:
                await self.on_message_callback('ticker', market_data)

    async def _process_trade_message(self, data: Dict):
        """Process individual trade data"""
        if 'data' in data:
            trade = data['data']
            market_data = MarketData(
                symbol=trade['s'],
                price=float(trade['p']),
                volume=float(trade['q']),
                timestamp=datetime.fromtimestamp(trade['T'] / 1000)
            )

            if self.on_message_callback:
                await self.on_message_callback('trade', market_data)

    async def _process_depth_message(self, data: Dict):
        """Process order book depth data"""
        if 'data' in data:
            depth = data['data']

            # Extract best bid and ask
            bids = depth['bids']
            asks = depth['asks']

            if bids and asks:
                best_bid = float(bids[0][0])
                best_ask = float(asks[0][0])

                market_data = MarketData(
                    symbol=depth['s'],
                    price=(best_bid + best_ask) / 2,  # Mid price
                    volume=0,  # Not applicable for depth
                    timestamp=datetime.fromtimestamp(depth['T'] / 1000),
                    bid=best_bid,
                    ask=best_ask
                )

                if self.on_message_callback:
                    await self.on_message_callback('depth', market_data)

    async def _process_kline_message(self, data: Dict):
        """Process kline (OHLCV) data"""
        if 'data' in data:
            kline_data = data['data']
            kline = kline_data['k']

            # Only process closed candles
            if kline['x']:  # Kline is closed
                ohlcv_data = {
                    'symbol': kline['s'],
                    'timeframe': kline['i'],
                    'open_time': datetime.fromtimestamp(kline['t'] / 1000),
                    'close_time': datetime.fromtimestamp(kline['T'] / 1000),
                    'open': float(kline['o']),
                    'high': float(kline['h']),
                    'low': float(kline['l']),
                    'close': float(kline['c']),
                    'volume': float(kline['v']),
                    'quote_volume': float(kline['q']),
                    'trades': int(kline['n'])
                }

                if self.on_message_callback:
                    await self.on_message_callback('kline', ohlcv_data)

class RealTimeDataManager:
    """
    Manager for real-time data feeds integration with AstroA
    """

    def __init__(self, symbols: List[str]):
        self.symbols = symbols
        self.logger = logging.getLogger(__name__)
        self.websocket_client = None
        self.data_buffer = {}
        self.callbacks = []

    def add_callback(self, callback: Callable):
        """Add callback for data processing"""
        self.callbacks.append(callback)

    async def start_feeds(self):
        """Start real-time data feeds"""
        self.logger.info("Starting real-time data feeds")

        # Initialize WebSocket client
        self.websocket_client = BinanceWebSocketClient(
            symbols=self.symbols,
            on_message_callback=self._on_data_received
        )

        # Start WebSocket connections
        await self.websocket_client.start()

    async def stop_feeds(self):
        """Stop real-time data feeds"""
        if self.websocket_client:
            await self.websocket_client.stop()

    async def _on_data_received(self, data_type: str, data):
        """Handle incoming real-time data"""
        # Store in buffer
        symbol = data.symbol if hasattr(data, 'symbol') else data.get('symbol')

        if symbol not in self.data_buffer:
            self.data_buffer[symbol] = {}

        self.data_buffer[symbol][data_type] = data

        # Notify callbacks
        for callback in self.callbacks:
            try:
                await callback(data_type, data)
            except Exception as e:
                self.logger.error(f"Callback error: {str(e)}")

    def get_latest_data(self, symbol: str) -> Dict:
        """Get latest data for symbol"""
        return self.data_buffer.get(symbol, {})

# Usage Example
async def main():
    # Define symbols to track
    symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']

    # Create data manager
    data_manager = RealTimeDataManager(symbols)

    # Add callback for processing
    async def process_data(data_type, data):
        print(f"Received {data_type}: {data}")

    data_manager.add_callback(process_data)

    # Start feeds
    await data_manager.start_feeds()

if __name__ == "__main__":
    asyncio.run(main())</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Data Feed Configuration</h3>

                    <div class="code-file">
                        <div class="file-header">config/data_feeds_config.py</div>
                        <div class="file-content">"""
Real-time Data Feeds Configuration
"""
import os
from typing import List, Dict
from dataclasses import dataclass

@dataclass
class DataFeedsConfig:
    """Configuration for real-time data feeds"""

    # Primary data source
    primary_source: str = 'binance'

    # Symbols to track (matches AstroA trading pairs)
    symbols: List[str] = [
        'BTC/USDT',
        'ETH/USDT',
        'ADA/USDT',
        'SOL/USDT',
        'DOT/USDT',
        'LINK/USDT',
        'AVAX/USDT',
        'MATIC/USDT'
    ]

    # Timeframes for OHLCV data
    timeframes: List[str] = ['1m', '5m', '1h', '4h', '1d']

    # WebSocket connection settings
    max_connections: int = 10
    reconnect_delay: int = 5  # seconds
    heartbeat_interval: int = 30  # seconds
    connection_timeout: int = 10  # seconds

    # Data buffer settings
    buffer_size: int = 1000  # Number of data points to keep
    flush_interval: int = 60  # seconds

    # Rate limiting
    max_messages_per_second: int = 100
    burst_limit: int = 200

    # Binance WebSocket URLs
    binance_base_url: str = "wss://stream.binance.com:9443/ws"
    binance_combined_url: str = "wss://stream.binance.com:9443/stream"

    # CoinGecko API settings
    coingecko_api_key: str = os.getenv('COINGECKO_API_KEY', '')
    coingecko_base_url: str = "https://api.coingecko.com/api/v3"
    coingecko_rate_limit: int = 10  # requests per minute on free tier

    # Data quality settings
    price_change_threshold: float = 0.1  # 10% max price change filter
    volume_spike_threshold: float = 5.0  # 5x volume spike filter
    stale_data_threshold: int = 300  # 5 minutes

    # Logging
    log_level: str = 'INFO'
    log_data_points: bool = False  # Set to True for debugging

    # Integration with AstroA
    astroa_integration_enabled: bool = True
    astroa_callback_enabled: bool = True
    database_logging_enabled: bool = True

# Global configuration instance
feeds_config = DataFeedsConfig()</div>
                    </div>
                </div>
            </div>

            <!-- Integration Tab -->
            <div id="integration" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🔗 AstroA Integration</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Real-Time Data Agent</h3>

                    <div class="code-file">
                        <div class="file-header">agents/data_feeds/real_time_data_agent.py</div>
                        <div class="file-content">#!/usr/bin/env python3
"""
Real-time Data Agent for AstroA
Integrates live market data with existing data collection system
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import psycopg2
import json

from agents.base_agent import BaseAgent
from agents.data_feeds.binance_websocket_client import RealTimeDataManager
from config.data_feeds_config import feeds_config
from shared.types.agent_types import AgentResult

class RealTimeDataAgent(BaseAgent):
    """
    Real-time data agent that streams live market data
    """

    def __init__(self):
        super().__init__("RealTimeDataAgent")
        self.data_manager = None
        self.is_streaming = False
        self.data_buffer = {}
        self.last_update = {}

        # Database connection
        self.db_config = {
            'host': 'localhost',
            'database': 'mathematical_trading',
            'user': 'trading_user',
            'password': 'hejhej'
        }

    async def execute(self, **kwargs) -> AgentResult:
        """Execute real-time data collection"""
        try:
            symbols = kwargs.get('symbols', feeds_config.symbols)
            duration = kwargs.get('duration', None)  # seconds

            self.logger.info(f"Starting real-time data collection for {len(symbols)} symbols")

            # Initialize data manager
            self.data_manager = RealTimeDataManager(symbols)
            self.data_manager.add_callback(self._process_real_time_data)

            # Start streaming
            self.is_streaming = True

            if duration:
                # Stream for specified duration
                await asyncio.wait_for(
                    self.data_manager.start_feeds(),
                    timeout=duration
                )
            else:
                # Stream indefinitely
                await self.data_manager.start_feeds()

            return AgentResult(
                agent_name=self.name,
                status='success',
                data={
                    'symbols_tracked': symbols,
                    'data_points_collected': len(self.data_buffer),
                    'streaming_duration': duration
                }
            )

        except Exception as e:
            self.logger.error(f"Real-time data collection failed: {str(e)}")
            return AgentResult(
                agent_name=self.name,
                status='error',
                error_message=str(e)
            )
        finally:
            await self.stop_streaming()

    async def stop_streaming(self):
        """Stop real-time data streaming"""
        self.is_streaming = False
        if self.data_manager:
            await self.data_manager.stop_feeds()

    async def _process_real_time_data(self, data_type: str, data):
        """Process incoming real-time data"""
        try:
            # Update buffer
            symbol = data.symbol if hasattr(data, 'symbol') else data.get('symbol')

            if symbol not in self.data_buffer:
                self.data_buffer[symbol] = {}

            self.data_buffer[symbol][data_type] = data
            self.last_update[symbol] = datetime.now()

            # Log to database if enabled
            if feeds_config.database_logging_enabled:
                await self._log_to_database(data_type, data)

            # Trigger strategy analysis if price data
            if data_type in ['ticker', 'trade'] and feeds_config.astroa_integration_enabled:
                await self._trigger_strategy_analysis(symbol, data)

        except Exception as e:
            self.logger.error(f"Error processing real-time data: {str(e)}")

    async def _log_to_database(self, data_type: str, data):
        """Log real-time data to database"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            if data_type == 'ticker':
                # Log ticker data
                cursor.execute("""
                    INSERT INTO real_time_prices
                    (symbol, price, volume, high_24h, low_24h, timestamp, data_type)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (
                    data.symbol,
                    data.price,
                    data.volume,
                    data.high_24h,
                    data.low_24h,
                    data.timestamp,
                    data_type
                ))

            elif data_type == 'kline':
                # Log OHLCV data
                cursor.execute("""
                    INSERT INTO market_data
                    (symbol, timestamp, open_price, high_price, low_price, close_price, volume, timeframe, exchange)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (symbol, timestamp, timeframe, exchange)
                    DO UPDATE SET
                        open_price = EXCLUDED.open_price,
                        high_price = EXCLUDED.high_price,
                        low_price = EXCLUDED.low_price,
                        close_price = EXCLUDED.close_price,
                        volume = EXCLUDED.volume
                """, (
                    data['symbol'],
                    data['close_time'],
                    data['open'],
                    data['high'],
                    data['low'],
                    data['close'],
                    data['volume'],
                    data['timeframe'],
                    'binance'
                ))

            conn.commit()

        except Exception as e:
            self.logger.error(f"Database logging error: {str(e)}")
        finally:
            if conn:
                conn.close()

    async def _trigger_strategy_analysis(self, symbol: str, data):
        """Trigger AstroA strategy analysis with new data"""
        try:
            # Only trigger analysis if significant price change
            if hasattr(data, 'price') and symbol in self.last_update:
                # Get previous price from buffer
                prev_data = self.data_buffer.get(symbol, {}).get('ticker')
                if prev_data and hasattr(prev_data, 'price'):
                    price_change = abs(data.price - prev_data.price) / prev_data.price

                    # Trigger analysis if price change > threshold
                    if price_change > feeds_config.price_change_threshold / 100:
                        # Send signal to trading strategy agent
                        await self._notify_trading_agent(symbol, data, price_change)

        except Exception as e:
            self.logger.error(f"Strategy analysis trigger error: {str(e)}")

    async def _notify_trading_agent(self, symbol: str, data, price_change: float):
        """Notify trading strategy agent of significant market move"""
        try:
            # Create market event notification
            market_event = {
                'symbol': symbol,
                'event_type': 'price_move',
                'price': data.price,
                'price_change_pct': price_change * 100,
                'timestamp': data.timestamp,
                'volume': getattr(data, 'volume', 0)
            }

            # Log market event
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO market_events
                (symbol, event_type, event_data, timestamp)
                VALUES (%s, %s, %s, %s)
            """, (
                symbol,
                'price_move',
                json.dumps(market_event),
                datetime.now()
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"Market event: {symbol} moved {price_change*100:.2f}%")

        except Exception as e:
            self.logger.error(f"Trading agent notification error: {str(e)}")

    def get_latest_price(self, symbol: str) -> Optional[float]:
        """Get latest price for symbol"""
        data = self.data_buffer.get(symbol, {})
        ticker_data = data.get('ticker')
        if ticker_data and hasattr(ticker_data, 'price'):
            return ticker_data.price
        return None

    def get_latest_data(self, symbol: str) -> Dict:
        """Get all latest data for symbol"""
        return self.data_buffer.get(symbol, {})

    def is_data_stale(self, symbol: str) -> bool:
        """Check if data for symbol is stale"""
        if symbol not in self.last_update:
            return True

        time_diff = datetime.now() - self.last_update[symbol]
        return time_diff.total_seconds() > feeds_config.stale_data_threshold

# Usage
async def main():
    agent = RealTimeDataAgent()

    # Stream for 1 hour
    result = await agent.execute(duration=3600)
    print(f"Streaming result: {result.status}")

if __name__ == "__main__":
    asyncio.run(main())</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Database Schema Updates</h3>

                    <div class="code-file">
                        <div class="file-header">setup_real_time_tables.sql</div>
                        <div class="file-content">-- Real-time data tables for AstroA system
-- Run this to add real-time data support

-- Real-time price data table
CREATE TABLE IF NOT EXISTS real_time_prices (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    price DECIMAL(20, 8) NOT NULL,
    volume DECIMAL(20, 8),
    bid DECIMAL(20, 8),
    ask DECIMAL(20, 8),
    high_24h DECIMAL(20, 8),
    low_24h DECIMAL(20, 8),
    timestamp TIMESTAMP NOT NULL,
    data_type VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for fast price lookups
CREATE INDEX IF NOT EXISTS idx_real_time_prices_symbol_timestamp
ON real_time_prices(symbol, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_real_time_prices_symbol_latest
ON real_time_prices(symbol, created_at DESC);

-- Market events table (for significant moves)
CREATE TABLE IF NOT EXISTS market_events (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB,
    timestamp TIMESTAMP NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for event processing
CREATE INDEX IF NOT EXISTS idx_market_events_symbol_timestamp
ON market_events(symbol, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_market_events_unprocessed
ON market_events(processed, created_at) WHERE processed = FALSE;

-- Data feed status table
CREATE TABLE IF NOT EXISTS data_feed_status (
    id SERIAL PRIMARY KEY,
    feed_name VARCHAR(50) NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL,
    last_update TIMESTAMP,
    error_count INTEGER DEFAULT 0,
    last_error TEXT,
    uptime_percentage DECIMAL(5, 2),
    data_points_today INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert initial feed status
INSERT INTO data_feed_status (feed_name, status, last_update) VALUES
('binance_websocket', 'stopped', CURRENT_TIMESTAMP),
('coingecko_api', 'stopped', CURRENT_TIMESTAMP)
ON CONFLICT (feed_name) DO NOTHING;

-- Function to get latest price
CREATE OR REPLACE FUNCTION get_latest_price(p_symbol VARCHAR(20))
RETURNS DECIMAL(20, 8) AS $$
DECLARE
    latest_price DECIMAL(20, 8);
BEGIN
    SELECT price INTO latest_price
    FROM real_time_prices
    WHERE symbol = p_symbol
    ORDER BY timestamp DESC
    LIMIT 1;

    RETURN latest_price;
END;
$$ LANGUAGE plpgsql;

-- Function to get price change
CREATE OR REPLACE FUNCTION get_price_change_24h(p_symbol VARCHAR(20))
RETURNS DECIMAL(20, 8) AS $$
DECLARE
    current_price DECIMAL(20, 8);
    price_24h_ago DECIMAL(20, 8);
    price_change DECIMAL(20, 8);
BEGIN
    -- Get current price
    SELECT price INTO current_price
    FROM real_time_prices
    WHERE symbol = p_symbol
    ORDER BY timestamp DESC
    LIMIT 1;

    -- Get price 24h ago
    SELECT price INTO price_24h_ago
    FROM real_time_prices
    WHERE symbol = p_symbol
      AND timestamp <= CURRENT_TIMESTAMP - INTERVAL '24 hours'
    ORDER BY timestamp DESC
    LIMIT 1;

    -- Calculate change
    IF current_price IS NOT NULL AND price_24h_ago IS NOT NULL THEN
        price_change := ((current_price - price_24h_ago) / price_24h_ago) * 100;
    ELSE
        price_change := 0;
    END IF;

    RETURN price_change;
END;
$$ LANGUAGE plpgsql;

-- View for latest prices
CREATE OR REPLACE VIEW latest_prices AS
SELECT DISTINCT ON (symbol)
    symbol,
    price,
    volume,
    bid,
    ask,
    timestamp,
    data_type
FROM real_time_prices
ORDER BY symbol, timestamp DESC;

-- View for market overview
CREATE OR REPLACE VIEW market_overview AS
SELECT
    symbol,
    price,
    get_price_change_24h(symbol) as change_24h_pct,
    volume,
    timestamp
FROM latest_prices
ORDER BY symbol;</div>
                    </div>
                </div>
            </div>

            <!-- Deployment Tab -->
            <div id="deployment" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🚀 Deployment & Testing</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 1: Setup Database Tables</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Create real-time data tables
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -f setup_real_time_tables.sql

# Verify tables created
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public' AND table_name LIKE '%real_time%';"</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 2: Install Dependencies</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Navigate to AstroA directory
cd /home/<USER>/axmadcodes/AstroA

# Activate virtual environment
source venv/bin/activate

# Install WebSocket dependencies
pip install websockets
pip install asyncio-throttle
pip install python-binance
pip install aiohttp

# Verify installations
python -c "import websockets; print('WebSockets installed')"
python -c "import asyncio; print('AsyncIO ready')"</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-8/step 3: Test WebSocket Connection</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Test basic WebSocket connection
python -c "
import asyncio
import websockets
import json

async def test_binance_connection():
    uri = 'wss://stream.binance.com:9443/ws/btcusdt@ticker'

    try:
        async with websockets.connect(uri) as websocket:
            print('✅ Connected to Binance WebSocket')

            # Receive a few messages
            for i in range(3):
                message = await websocket.recv()
                data = json.loads(message)
                print(f'📊 BTC Price: ${float(data[\"c\"]):,.2f}')

    except Exception as e:
        print(f'❌ Connection failed: {e}')

asyncio.run(test_binance_connection())
"</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 4: Start Real-Time Data Collection</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Start real-time data agent (5 minutes test)
python -c "
import asyncio
from agents.data_feeds.real_time_data_agent import RealTimeDataAgent

async def main():
    agent = RealTimeDataAgent()
    print('🌟 Starting real-time data collection...')

    result = await agent.execute(duration=300)  # 5 minutes
    print(f'📊 Result: {result.status}')
    print(f'📈 Data collected: {result.data}')

asyncio.run(main())
"

# Or run indefinitely (stop with Ctrl+C)
python agents/data_feeds/real_time_data_agent.py</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 5: Verify Data Collection</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Check if data is being collected
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT
    symbol,
    price,
    volume,
    timestamp,
    data_type
FROM real_time_prices
ORDER BY timestamp DESC
LIMIT 10;
"

# Check latest prices view
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT * FROM latest_prices;
"

# Check market events
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT
    symbol,
    event_type,
    timestamp,
    processed
FROM market_events
ORDER BY timestamp DESC
LIMIT 5;
"</div>
                </div>
            </div>

            <!-- Monitoring Tab -->
            <div id="monitoring" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">📊 Monitoring & Management</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Real-Time Data Dashboard</h3>

                    <div class="code-file">
                        <div class="file-header">create_data_feeds_dashboard.py</div>
                        <div class="file-content">#!/usr/bin/env python3
"""
Real-time Data Feeds Dashboard
Monitor live data streams and system health
"""

import streamlit as st
import psycopg2
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
import json

class DataFeedsDashboard:
    """Dashboard for monitoring real-time data feeds"""

    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'mathematical_trading',
            'user': 'trading_user',
            'password': 'hejhej'
        }

    def run(self):
        """Run the Streamlit dashboard"""
        st.set_page_config(
            page_title="AstroA Real-Time Data Dashboard",
            page_icon="📡",
            layout="wide"
        )

        st.title("📡 AstroA Real-Time Data Feeds Dashboard")
        st.markdown("Monitor live market data streams and system health")

        # Auto-refresh
        if st.checkbox("Auto-refresh (10s)", value=True):
            time.sleep(10)
            st.rerun()

        # Main content
        self._display_feed_status()
        self._display_latest_prices()
        self._display_market_events()
        self._display_data_volume_charts()

    def _display_feed_status(self):
        """Display data feed status"""
        st.subheader("📊 Feed Status")

        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT feed_name, status, last_update, error_count, uptime_percentage, data_points_today
                FROM data_feed_status
                ORDER BY feed_name
            """)

            results = cursor.fetchall()

            if results:
                col1, col2, col3, col4 = st.columns(4)

                for feed_name, status, last_update, error_count, uptime_pct, data_points in results:
                    with col1 if feed_name == 'binance_websocket' else col2:
                        # Status color
                        color = "🟢" if status == "running" else "🔴"
                        st.metric(
                            f"{color} {feed_name.replace('_', ' ').title()}",
                            status.upper(),
                            delta=f"{error_count} errors today"
                        )

                    with col3 if feed_name == 'binance_websocket' else col4:
                        st.metric(
                            "Data Points Today",
                            f"{data_points:,}",
                            delta=f"{uptime_pct:.1f}% uptime" if uptime_pct else None
                        )

        except Exception as e:
            st.error(f"Error loading feed status: {e}")
        finally:
            if conn:
                conn.close()

    def _display_latest_prices(self):
        """Display latest prices"""
        st.subheader("💰 Latest Prices")

        try:
            conn = psycopg2.connect(**self.db_config)

            df = pd.read_sql("""
                SELECT
                    symbol,
                    price,
                    volume,
                    timestamp,
                    EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - timestamp)) as seconds_ago
                FROM latest_prices
                ORDER BY symbol
            """, conn)

            if not df.empty:
                # Format data
                df['price'] = df['price'].apply(lambda x: f"${float(x):,.2f}")
                df['volume'] = df['volume'].apply(lambda x: f"{float(x):,.0f}")
                df['last_update'] = df['seconds_ago'].apply(
                    lambda x: f"{int(x)}s ago" if x < 60 else f"{int(x/60)}m ago"
                )

                # Display table
                display_df = df[['symbol', 'price', 'volume', 'last_update']].copy()
                display_df.columns = ['Symbol', 'Price', 'Volume', 'Last Update']

                st.dataframe(display_df, use_container_width=True)
            else:
                st.info("No real-time price data available")

        except Exception as e:
            st.error(f"Error loading prices: {e}")
        finally:
            if conn:
                conn.close()

    def _display_market_events(self):
        """Display recent market events"""
        st.subheader("⚡ Recent Market Events")

        try:
            conn = psycopg2.connect(**self.db_config)

            df = pd.read_sql("""
                SELECT
                    symbol,
                    event_type,
                    event_data,
                    timestamp,
                    processed
                FROM market_events
                WHERE timestamp > CURRENT_TIMESTAMP - INTERVAL '1 hour'
                ORDER BY timestamp DESC
                LIMIT 20
            """, conn)

            if not df.empty:
                # Parse event data
                events_data = []
                for _, row in df.iterrows():
                    event_data = json.loads(row['event_data']) if row['event_data'] else {}
                    events_data.append({
                        'Symbol': row['symbol'],
                        'Event': row['event_type'].replace('_', ' ').title(),
                        'Price': f"${event_data.get('price', 0):.2f}",
                        'Change': f"{event_data.get('price_change_pct', 0):.2f}%",
                        'Time': row['timestamp'].strftime('%H:%M:%S'),
                        'Processed': '✅' if row['processed'] else '⏳'
                    })

                events_df = pd.DataFrame(events_data)
                st.dataframe(events_df, use_container_width=True)
            else:
                st.info("No recent market events")

        except Exception as e:
            st.error(f"Error loading market events: {e}")
        finally:
            if conn:
                conn.close()

    def _display_data_volume_charts(self):
        """Display data volume charts"""
        st.subheader("📈 Data Volume Trends")

        try:
            conn = psycopg2.connect(**self.db_config)

            # Data points per minute
            df = pd.read_sql("""
                SELECT
                    DATE_TRUNC('minute', timestamp) as minute,
                    COUNT(*) as data_points,
                    COUNT(DISTINCT symbol) as symbols_active
                FROM real_time_prices
                WHERE timestamp > CURRENT_TIMESTAMP - INTERVAL '2 hours'
                GROUP BY DATE_TRUNC('minute', timestamp)
                ORDER BY minute
            """, conn)

            if not df.empty:
                col1, col2 = st.columns(2)

                with col1:
                    fig = go.Figure()
                    fig.add_trace(go.Scatter(
                        x=df['minute'],
                        y=df['data_points'],
                        mode='lines+markers',
                        name='Data Points/Minute',
                        line=dict(color='#7c3aed', width=2)
                    ))
                    fig.update_layout(
                        title="Data Points Per Minute",
                        xaxis_title="Time",
                        yaxis_title="Data Points"
                    )
                    st.plotly_chart(fig, use_container_width=True)

                with col2:
                    fig = go.Figure()
                    fig.add_trace(go.Scatter(
                        x=df['minute'],
                        y=df['symbols_active'],
                        mode='lines+markers',
                        name='Active Symbols',
                        line=dict(color='#10b981', width=2)
                    ))
                    fig.update_layout(
                        title="Active Symbols Per Minute",
                        xaxis_title="Time",
                        yaxis_title="Symbol Count"
                    )
                    st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error loading charts: {e}")
        finally:
            if conn:
                conn.close()

if __name__ == "__main__":
    dashboard = DataFeedsDashboard()
    dashboard.run()</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Data Feed Management Commands</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Install dashboard dependencies
pip install streamlit plotly

# Start dashboard
streamlit run create_data_feeds_dashboard.py

# Access at: http://localhost:8501</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Health Check & Maintenance</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Check data freshness
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT
    symbol,
    EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - timestamp)) as seconds_ago
FROM latest_prices
WHERE EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - timestamp)) > 300
ORDER BY seconds_ago DESC;
"

# Clean old data (keep last 7 days)
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
DELETE FROM real_time_prices
WHERE timestamp < CURRENT_TIMESTAMP - INTERVAL '7 days';
"

# Check database size
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
"</div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-12 bg-gray-800 text-white py-8">
            <div class="container mx-auto px-6 text-center">
                <p class="text-lg">📡 AstroA Real-Time Data Feeds Guide</p>
                <p class="mt-2 opacity-75">Power your strategies with live market data</p>
                <div class="mt-4 flex justify-center space-x-6">
                    <div class="flex items-center space-x-2">
                        <i data-lucide="zap" class="w-5 h-5"></i>
                        <span>Ultra-Low Latency</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="wifi" class="w-5 h-5"></i>
                        <span>WebSocket Streams</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="activity" class="w-5 h-5"></i>
                        <span>Real-Time Analytics</span>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Copy to clipboard functionality
        function copyToClipboard(button) {
            const codeBlock = button.parentNode;
            const code = codeBlock.textContent.replace('Copy', '').trim();

            navigator.clipboard.writeText(code).then(function() {
                button.textContent = 'Copied!';
                setTimeout(function() {
                    button.textContent = 'Copy';
                }, 2000);
            });
        }

        // Add click event listeners to all copy buttons
        document.addEventListener('DOMContentLoaded', function() {
            const copyButtons = document.querySelectorAll('.copy-button');
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    copyToClipboard(this);
                });
            });
        });
    </script>
</body>
</html>