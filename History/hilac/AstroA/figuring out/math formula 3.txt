
here are the top 10 most important things you need to know about
0:08
matrices must know number one what is a matrix a matrix is a rectangular array
0:15
of numbers arranged into rows and columns so here's a sample of what a
0:20
matrix would look like and the size or order of a matrix is said to be M by n
0:27
where m is just the number of rows The Matrix has and N is the number of
0:33
columns if I wanted the size of this sample Matrix I notice that it has two
0:39
rows and I'll number those rows Row one and row two and it also has three
0:45
columns and I'll number those columns 1 through three because this Matrix has
0:50
two rows and three columns we could say that this is a 2x3 Matrix and let's give
0:56
this Matrix a name let's say this is Matrix a so I could say Matrix a is a
1:02
2x3 Matrix now for this Matrix a there are six elements and all six elements
1:09
have a unique position within the Matrix the position of all of the elements that make up Matrix a could be defined based
1:17
on their row and column indices I and J where the index I States what row number
1:24
the element is in and the index J States what column number it's in so all six of
1:30
these elements have a unique position that can be defined using this notation so how that would look within the Matrix
1:37
would be this element here that is in row one and column 1 would be element A1
1:45
1 and this element which is in row one column 2 would be element
1:52
A1 2 and this element which is in row one column 3 would be element A1 three
2:00
and I'll also fill in the notations for the elements that are in row number two now that you know what a matrix looks
2:06
like how to describe its size and how to reference any of the elements in a matrix based on its row and column Let's
2:14
do an example let's say I have Matrix B let's State the size of this Matrix and
2:20
also find the value of element B24 and b32 to define the size of the Matrix I
2:27
need to know how many rows and columns does it have remember rows are horizontal so we have three rows and
2:34
columns are vertical so there are four columns which means this is a 3x4 matrix
2:40
now if I want element B24 remember the first index is the row number and the second index is the
2:47
column number so I'm looking for the element that is in row two column 4 I
2:54
can see based on where those lines intersect that I'm looking at that element right there which has a value of four and and for element b32 I'm looking
3:02
for the element that is in Row 3 and column 2 which would be this element
3:07
right here which has a value of
Basic Operations
3:14
five must know number two basic Matrix operations the basic operations I'll
3:21
show you in this section are adding and subtracting matrices and scalar multiplication let's start with adding
3:27
and subtracting matrices the first thing we should note is that to add or subtract matrices the matrices must be
3:34
the same size and all you have to do to add or subtract matrices is add or subtract their corresponding elements so
3:41
for example if I have Matrix A and B that are both 2x two matrices the sum of
3:47
those two matrices would also be a 2X two Matrix made up of four
3:53
elements to get the element that goes in row one column 1 I would just add the elements from Matrix a and Matrix B that
4:00
are in Row 1 column 1 so I would do a11 + b11 and to get the element that goes
4:06
in Row 1 column 2 I would add these two corresponding elements and the same pattern for the elements that are in row
4:13
two so let's do a quick example of this given Matrix C and Matrix D let's find
4:18
the sum of those matrices because the original two matrices are the same size they're both 2x3 I know when adding them
4:25
together I will get a 2x3 Matrix as well to get the element that goes in row one column 1 I'll add the corresponding
4:32
elements for Matrix C and Matrix D that' be 1 + 7 and then for Row 1 column 2
4:39
I'll add 2 + 8 and then for the element that goes in Row 1 column 3 add the corresponding elements 3 and n and then
4:47
I'll follow that same rule to get the elements that go in the second row of this Matrix and now let me make some
4:52
room so that I can simplify all those elements the elements in Matrix C plus
4:58
Matrix D simplify to 8 10 12 14 16 and
5:04
18 let's now move on to scalar multiplication for scal multiplication each element of the Matrix gets
5:11
multiplied by the same factor and when doing this the Matrix stays the same size so if we have some Matrix Matrix a
5:19
I could multiply that Matrix a by some scalar I'll call that scalar K to get the elements that go inside of that
5:25
Matrix I would just multiply all of the elements of Matrix a by the scalar K let's practice that with
5:32
an example given some Matrix Matrix B let's find what 3 * Matrix B would equal
5:39
and I could calculate each of the elements just by taking all six of these elements and multiplying them by the
5:45
scaler 3 and then I could simplify each of those Elements by doing the multiplications so 3 * Matrix B would
5:53
have the elements 6 12 18 3 9 15
Elementary Row Operations
6:06
must know number three Elementary row operations there are three operations
6:12
I'm going to show you in this section interchange scaling and replacement but
6:18
before I show you those operations it's time we talked about one of the main applications of matrices and that's
6:24
representing systems of linear equations so if we have a system of three linear equations there are a couple different
6:30
ways I could represent the systems using matrices the first way which we'll talk about a lot more later is the Matrix
6:37
form Matrix form is the format a x = b
6:42
where a is what's called a coefficient Matrix and to create that Matrix a I
6:47
just have to look at the coefficients of the variables in these equations so for Row one of Matrix a I just look at
6:54
equation one and specifically what are the coefficients of the variables they're 4 -1 and 2
7:00
and then to get Row 2 and 3 of Matrix a I just look at the coefficients of the variables from equation 2 and 3 x stands
7:07
for the column Vector of the variables that are in the equations the variables in the equations are x y and z and b is
7:16
the column Vector of the constant values that the three equations are equal to and what we're going to be doing in this
7:21
section is actually combining together the coefficient Matrix with this column
7:27
Vector of constant values to create what's called an augmented Matrix when combining them together we just place
7:34
these constant values as a fourth column combined with this coefficient Matrix
7:40
and often you'll see a vertical line separating the coefficient Matrix from the constant values and now the whole
7:46
reason we would want to rewrite this system of equations in this augmented Matrix is so that we can perform these
7:52
three operations to make the solution or solutions to this system easier to find
7:59
because no matter which of these three operations we do to an augmented Matrix the result is a matrix that has the
8:06
exact same solution set so let's look at these three operations and then in the next section I'll show you how they can
8:12
be used to find the solution to a system of equations the first operation is
8:17
interchange which just means you're allowed to swap two rows with each other so if we start with this augmented
8:23
Matrix I could for example swap Row one and row two with each other and the
8:29
notation for that is you would write Row one and row two are switching places
8:35
with each other and when we do that we get a new Matrix where row two of the original Matrix is now up in row one and
8:43
Row one of the Matrix is now down in row two and Row three stays exactly where it is the second operation is scaling that
8:51
just means you're allowed to multiply any row by a scalar so let's continue on with this Matrix here I could for
8:58
example take row two and multiply it by 3 so I would multiply Row Two by 3 and
9:05
then whatever those answers are put all of those values into row two doing that
9:11
we'll create a new Matrix where rows 1 and three stay the same but the values
9:16
in row two need to be tripled and the last operation is replacement which
9:21
means you can replace any row with the sum of itself and any other Row in The
9:27
Matrix continuing on with this Matrix what I can do is I can replace Row three
9:33
with the sum of Row 3 plus Row 2 I'll communicate that by saying Row 2 + Row 3
9:41
that result I'm going to put into Row 3 now an important part here because I'm
9:48
adding row two and Row three the result of that can only be put into either row two or Row three it can't go into Row
9:55
one if that's not included in the addition and also I could put a multiple in front of either of these rows as well
10:02
so I could have done let's say 2 * Row 2 plus Row 3 but I'll keep it simple for this example creating this new Matrix I
10:10
notice only Row three is being replaced so rows one and row two are going to stay exactly the same but Row three is
10:17
going to be replaced with the sum of row two and Row three so when I add the corresponding elements of row two and
10:23
Row three together when I add the first elements together 12 + 2 is 14 so that becomes the new new element in Row 3 it
10:30
becomes 14 -3 + 3 is 0 6 + -4 is 2 and
10:36
21 + 9 is 30 looking back even though we started with this Matrix here we
10:42
performed 1 2 three different Elementary R operations on that Matrix and end it
10:48
up here this new Matrix we've created has the exact same solution set as the
10:53
original Matrix and in the next section I'm going to show you how we can choose which of these operations to do so that
10:59
we end up with a matrix that shows us the solution to the system of equations that we're working
Reduced Row Echelon Form
11:09
with must know number four reduced row Echelon form let's say we had a system
11:15
of three linear equations I could use an augmented Matrix to represent this system to write the augmented Matrix the
11:21
first thing I do is create the coefficient Matrix which I Do by looking at the coefficients of all the variables
11:27
and all the equations so for equation number one the coefficients of the variables are 2 1 and six so that
11:33
becomes the first row of my coefficient Matrix Row 2 would be 3 4 3 and then Row
11:40
three would be 1 -24 and I augment that with the column
11:45
of constant values that the equations are equal to 78 and 9 what I want to teach you how to
11:52
do in this section is how to use Elementary row operations to convert this augmented Matrix into a matrix mat
11:59
that's in reduced row Echelon form and the reason why we would want the Matrix in this format is because it's easy to
12:06
tell from that format if the Matrix has no Solutions if it has infinite solutions or if it has one solution what
12:13
that solution actually is and any Matrix that is in this format has a structure
12:18
that follows five rules number one if there are any rows of the Matrix that are all zero they're at the very bottom
12:25
of the Matrix each leading entry is in a column to the right of the leading entry
12:31
above it so if we look at row two its first nonzero number its leading entry
12:36
is right here it's this one notice that leading entry is in a column that's to the right of the leading entry that's
12:42
above it and number three all entries in a column below a leading entry are zero
12:48
so if we look at the leading entry from Row one everything below it is a zero
12:53
now any Matrix that follows those first three rules is in what we call row Echelon form but for a matrix to be in
13:00
reduced R Echelon form all the leading entries also have to be a one notice
13:05
they're all ones and each leading one is the only nonzero in its column so
13:12
looking at the leading entry from row two not only is there a zero below it but there's also a zero above it it's
13:19
the only nonzero number in its entire column and to get a matrix into this
13:25
reduced Ro Echelon form there is a process called gaussian elimination that we're going to follow so let me start by
13:32
copying this augmented Matrix down here and we'll start doing some Elementary row operations now the first thing we
13:38
want to do is begin at the leftmost nonzero column so right here and make a
13:45
nonzero entry the pivot at the top while all of the entries in this column are nonzero so we could use any of them as
13:51
the pivot at the top but I notice in reduced Ro Echelon form eventually I'm going to want a one there so we might as
13:58
well make this one the pivot at the top of that column so to get that one to the
14:03
top of the column we'll have to swap this entire row with the row that's at
14:09
the top so in order to get this one up to the top of the column we can swap all
14:14
of Row three with all of Row one so this one is now called our pivot and what we
14:20
want to do next is make all of the numbers below that pivot so the three and the two we want them to be a zero
14:26
I'll have to do that using the replacement operation I'll start by making this three become a zero I could
14:31
do that by replacing all of row two with Row 2 minus 3 * Row 1 so row one's
14:40
actually staying exactly the same I'll leave it as it is but I'm replacing all of row two with each of these entries
14:46
minus 3 * those entries and now I also want this two to become a zero so I can
14:52
replace all of Row 3 with Row 3 - 2 * Row 1 now that we've created zeros below
14:59
our first pivot we're going to ignore the first row and repeat that same
15:05
process for the remaining submatrix we're going to look at the first nonzero column we're going to make a nonzero
15:12
value be the pivot I might as well let 10 be the pivot and then make everything below that 10 become a zero so I just
15:19
need to make this five become a zero we'll continue doing that down below continuing with this Matrix I can make
15:26
this 5 be a zero by replacing all of Row 3 with 2 * Row 3 minus Row 2 we could
15:35
now get our anwers by writing the three equations that these rows correspond to
15:41
and using back substitution to get the answers for the variables but we're going to continue on to get this Matrix
15:47
into reduced row Echelon form to make it more obvious what the answers are so
15:52
what I have to do now going from right to left is create zeros above each
15:58
leading entry and also make the leading entries all be one looking at Row three it would be
16:05
easy to make this leading entry be a one just by multiplying all of Row three by
16:10
1 over 13 let me make a little bit more room so I have space to work and what we
16:16
want to do now is make the entries Above This leading one we want these entries to both be zero so I'll leave Row three
16:22
the same but I'll replace row two with Row 2 - 15 * Row 3 and I'll change this
16:31
-4 into a zero just by doing Row 1 + 4 *
16:36
Row 3 let me Zoom back out and let's now make this 10 which is the leading entry
16:42
in row two let's make it become a one by multiplying all of Row Two by 1 over 10 so I'll continue that down here and the
16:49
last thing we have to do is get the number Above This leading one to be a zero so this -2 has to become zero I
16:55
could do that by replacing Row one with Row one plus 2 * Row 2 and now this Matrix here
17:03
actually reveals to us the answer to this system remember this part of the system is the coefficient Matrix those
17:10
columns represent the coefficients of the variables X Y and Z so each of the
17:17
three rows corresponds to an equation of our system the first row of our Matrix
17:23
tells us that 1 x is equal to 3 the second row tells us that 1 Y is equal to
17:30
-5 and the third row says that 1 Z is equal to 1 so our system of linear
17:37
equations has one unique solution but that's not always the case let me give you a couple examples for what the
17:44
reduced Ro Echelon form could look like they would indicate to you that this is not the scenario so let's erase what we
17:50
have Above So this scenario is when there is one unique solution and the
17:56
system is often called an independent system if there's one solution but let
18:01
me also show you what it would look like if there were no Solutions or infinite solutions let me start by drawing a
18:07
matrix that's in reduced Ro Echelon form that has no Solutions I know this Matrix would indicate that the system has no
18:13
solutions by looking at Row three based on Row three I see that 0 would have to
18:20
be equal to one and I know that 0 cannot equal one therefore there are no
18:25
solutions to that system what about infinite solutions let Mew draw a matrix that represents that scenario based on
18:31
the coefficient part of this Matrix I can tell there are three columns so there must have been three variables in
18:36
the equations I'll call them X1 X2 X3 but when I look at the number of leading
18:42
ones in my Matrix I see there are only two leading ones but there are three
18:48
variables when you have more variables than leading ones we know there's going to be infinite solutions and if I were
18:54
to try and write the solutions from Row one I can see that 1 X1 + 2x3 is equal
19:02
to 3 and if I were to rearrange that to isolate X1 I can move this 2x3 to the other side and from row two X2 - X3 is
19:11
equal to 4 and I could rearrange that to isolate X2 and in the column for X3
19:17
there are no leading entries two is not a leading entry negative 1's not a leading entry and Zer is not a leading
19:23
entry which means the X3 is what we call a free variable so so you could pick
19:29
anything you wanted for X3 and then based on what you pick for X3 that's going to alter your answers for X1 and
19:36
X2 which is going to create an infinite number of answers that'll satisfy your original system of linear
Matrix Multiplication
19:46
equations must know number five matrix multiplication let's say I have two
19:52
matrices being multiplied together how I find the product of these matrices is by
19:57
finding the dot product of each row from the first Matrix and each column of the second Matrix and if you don't know what
20:04
dot product is it's just the sum of the products of corresponding entries and in
20:09
order to be able to do that dot product the shapes of these matrices has to meet a set of criteria notice in this example
20:16
the first Matrix is a 2x2 matrix it has two rows and two columns and the second
20:22
Matrix has two rows and three columns so it's a 2x3 Matrix in order for
20:27
multiplication to work these two numbers have to match meaning the number of columns from the first
20:33
Matrix has to match the number of rows from the second Matrix and then these numbers indicate what the size of the
20:41
resulting Matrix is going to be so the product of these matrices is going to result in a 2x3 Matrix which means there
20:48
are going to be 1 2 3 4 5 6 entries in this Matrix to find the entry that goes
20:55
in row one column 1 I find the dot product of Row one from my first Matrix and
21:02
column one of my second Matrix and to find that dot product I find the sum of
21:07
the products of their corresponding entries so the first entry in the row times the first entry in the column 2 *
21:13
4 plus the second entry in the row times the second entry in the column 3 * 1 and
21:19
then I'll just repeat this process five more times to get the entry in row one column two I'll use Row one of my first
21:26
Matrix and column two of my second matx Matrix and then find the sum of the products of their corresponding entries
21:32
2 * 3 + 3 * -2 and then Row one column 3
21:38
use Row one of the first Matrix column 3 of the second Matrix and then find the dotproduct of their corresponding
21:44
entries 2 * 6 + 3 * 3 and then to find
21:49
the three entries that are in row two of my product Matrix I'll be finding the dot product of row two of Matrix 1 with
21:57
all three column colums of Matrix 2 so row two and column 1 row two column 2
22:05
and row two column 3 and now I can just simplify all six of those entries and I
22:11
get the Matrix 11 0 21 -1
22:18
13 -9 so that's how you multiply
Determinant of 2x2
22:26
matrices must know number six the determinant of a 2x2 matrix now we're
22:32
starting with the determinant of a 2x2 matrix because the determinant of a 1x1 Matrix is just itself and we'll actually
22:39
need to know that property as we do this example here let's start by defining a
22:44
determinant for any Square Matrix so for any n byn Matrix we'll call it Matrix a
22:51
the determinant of Matrix a which has a couple different notations can be
22:56
calculated by EXP expanding along any row or column if we're expanding along
23:03
row I the formula for calculating the determinant of a would equal the sum
23:09
from J = 1 to n of the product of each
23:15
element in row I so a i j with its corresponding co-actor so in row I we do
23:23
each element times its co-actor and add it all together but what is a co-actor
23:28
to find the co-actor of any element in the Matrix you do -1 to the exponent of
23:35
the row number plus the column number times the minor of element i j but
23:42
what's the minor of element i j well any minor element is just the determinant of
23:49
the submatrix obtained by removing the I row and jth column but remember since we
23:55
have a 2x2 matrix when we remove a row and a column what we're left with is
24:01
just a 1x1 Matrix and the determinant of a 1x1 Matrix is just the value itself so
24:07
calculating the minor of any element in a 2x2 matrix is really simple and you'll see how that works as we do an example
24:14
let's say we have Matrix a now because this is a square matrix it's a 2X two
24:19
Matrix I know I can calculate a determinant and to calculate the determinant I have to pick any row or
24:25
column in this Matrix to expand across it wouldn't matter which one would get the same answer in the end so I will
24:30
just pick Row one and what this formula tells me to do is to take each of the
24:36
values in row one multiply them by their corresponding co-actor and then add
24:42
those products together so I would have to do the element in row one column one
24:47
so a11 multiply it by its corresponding co-actor plus do the element in row one
24:54
column 2 and multiply that by its corresponding co-actor well I know the value of elements a11 and a12 it's 3 and
25:02
5 but I'll have to calculate their co-actors so let me start by calculating the co-actor of the element in row one
25:10
column one using the co-actor formula I would have to do -1 to the^ of 1 + 1 and
25:18
that gets multiplied by the minor of the corresponding element so that would be
25:23
times the minor of element 1 1 well -1 the^ of 2 is just one and the minor of
25:30
the element in row one column 1 well to calculate a minor I remove row I in
25:36
column J so that would mean remove Row one and column 1 and then find the
25:42
determinant of the remaining submatrix the remaining submatrix is just a 1x one Matrix so its determinant is just its
25:49
own value 7 which means the co-actor of the element in Row 1 column 1 is 1 * 7
25:55
which is 7 and now let me make a bit of room so we can Calculate co-actor 1 two
26:01
to find the co-actor of the element in Row 1 column 2 so the co-actor of five I
26:06
would do -1 to the exponent of 1 + 2 * the minor of the corresponding element
26:14
well -1 the^ of 3 is -1 and then to get the minor of the element in Row 1 column
26:19
2 I would have to eliminate Row 1 and column 2 and find the determinant of the
26:25
remaining submatrix the determinant of 2 is just 2 so -1 * 2 is -2 and I can now
26:33
Sub in for all four of the values required to calculate the determinant of Matrix a 21 + -10 is 11 so the
26:43
determinant of this Matrix is 11 but if we have specifically a 2x2 matrix like
26:49
this one there's a shortcut let me show you that shortcut and how it would work so I'll redraw Matrix a that we started
26:55
with and then to find the determinant of Matrix a because it's a 2x2 matrix I can
27:01
just find the product of the numbers in that diagonal 3 and 7 and then subtract
27:07
the product of the numbers in that diagonal 2 * 5 and notice that would give me the same answer 21 - 10 which is
Determinant of 3x3
27:21
11 must know number seven the determinant of a 3X3 Matrix let's say we
27:27
have Matrix B if I want to calculate its determinant it will probably be helpful
27:33
if I remind you of the formula for calculating the determinant of any n byn Matrix so let's say we're finding the
27:40
determinant of Matrix B and let's say we're expanding across row I of Matrix B
27:47
I would find the sum from J = 1 to n of
27:52
each element in row I times its corresponding co-actor so for this
27:58
Matrix we can pick any row or column to expand across to keep it consistent I'll
28:04
expand across Row one so to find the determinant of Matrix B I would need to
28:09
do the sums of the products of each element in row one times its
28:14
corresponding co-actor now I know the value of all of the elements in row one
28:20
it's 1 5 and zero but I'll have to calculate these three co-actors I'll start by calculating the
28:26
co-actor of the element in row row and column one up here I know to calculate a co-actor I do -1 to the power of the sum
28:35
of the row and column indices so to the power of 1 + 1 times the minor of the
28:41
element that's in Row 1 column 1 -1 to the^ of two is just one and then to
28:46
calculate the minor of the element that's in row one column 1 I have to imagine removing Row one and column one
28:54
from my Matrix and then find the determinant of the remaining submatrix
28:59
so I need the determinant of the Matrix 4 -1
29:05
-2 and in the last section I showed you the shortcut for finding the determinant of a 2x2 matrix I just do the product of
29:13
that diagonal 4 * 0 minus the product of this diagonal -2 And1 and if I simplify
29:20
that I get2 now I'll make a little bit of room so I can calculate my remaining two
29:26
co-actors if I continue standing across Row one the next co-actor I have to find is for this element the element that's
29:32
in Row 1 column 2 co-actor 1 2 would be equal to -1 to^ of 1 + 2 * the minor of
29:40
the element that's in Row 1 column 2 -1 ^ of 3 is1 and the minor of element 1 2
29:47
if I eliminate Row 1 and column 2 I then just find the determinant of the remaining submatrix so I have -1 * 2 * 0
29:57
- 0 0 * 1 which gives me -1 * 0 which is
30:02
0 and then continuing along Row 1 the last element I have to find the co-actor of is the element in row one column 3 so
30:09
I'll give you that solution quickly here and now that I have all three elements of Row one and all three of their
30:14
co-actors I can sub into my determinant formula and evaluating this expression gives me a determinant of -2 and now let
30:21
me show you a shortcut you can use for any 3x3 Matrix if you want its determinant the shortcut we'll need a
30:27
copy of the original Matrix B and then what we do is we copy the first two
30:32
columns to the right of the Matrix and then what I do to find the determinant of B I find the sum of the products of
30:40
the numbers in these three downward diagonals in the first diagonal the product of 1 4 and 0 is 0 plus the
30:49
product of 5-1 and 0 is also zero plus the product of 0 2 and -2 is also zero
30:58
and then I subtract the products of the three upward diagonals for the first
31:03
upward diagonal the product of the three numbers 0 4 and 0 is 0 minus the product
31:10
of the three numbers that make up the next upward diagonal -2 -1 and 1 have a
31:15
product of two minus the product of the three numbers in that diagonal is zero
31:21
and then we can just evaluate this expression which is just -2
Inverse of a Matrix
31:30
must know number eight how to calculate the inverse of a matrix now that you know how to calculate the determinant of
31:37
a matrix we can look at one of the main applications of determinants which is to help find the inverse of a matrix an N
31:44
byn Matrix we'll call it Matrix a is invertible meaning it has an inverse if
31:50
there exists a matrix the inverse Matrix of a so that the inverse Matrix of a
31:55
Time Matrix a equals the identity Matrix and the formula for calculating the
32:01
inverse of a matrix is as follows to find the inverse of Matrix a you do one
32:06
/ the determinant of Matrix a and multiply that by What's called the adjugate or sometimes the adjoint of
32:13
Matrix a and within this formula there's a couple things to note we know we can only calculate determinants for square
32:19
matrices so that means the only matrices that are invertible are square matrices
32:24
also the adjugate of Matrix a is just equ Al to the transpose of the co-actor
32:30
Matrix of Matrix a and one other thing we should note is that if the determinant of the Matrix was Zero we
32:37
would have 1/ Z which is undefined which would mean that the Matrix is not invertible and we call that a singular
32:44
Matrix and a determinant being zero telling us that Matrix a is not
32:50
invertible also actually tells us there's not a unique solution to the system of equations but we'll get more
32:56
into that in the next section so let's go ahead and do an example where we use this formula to calculate the inverse of
33:02
a matrix given this Matrix a let's find the inverse of Matrix a in this formula
33:07
for the inverse I need two things the determinant of Matrix a and the adjugate
33:13
of Matrix a let's start by finding the determinant since this is a 3X3 Matrix I know there's a shortcut for finding the
33:19
determinant I just rewrite the first two columns beside the Matrix and then find
33:24
the sum of the product of these three diagonals so this first diagonal is 4 *
33:29
5 * 3 which is 60 plus 2 * 2 * 2 which is 8+ 1 * 3 * 1 which is 3 and then I
33:38
subtract the products of these three diagonals so I have to subtract 2 * 5 *
33:43
1 which is 10 1 * 2 * 4 is 8 and 3 * 3 * 2 is 18 and if I simplify that
33:51
expression I get a determinant of 35 and now I need to find the adjugate of
33:57
Matrix a which I know is equal to the transpose of the co-factor Matrix of
34:02
Matrix a so we'll start by finding the co-actor Matrix of Matrix a I'm going to
34:07
have to find the co-actor of all nine elements of Matrix a let's start with the element that's in row one column 1
34:15
to calculate its co-actor I do -1 to the^ of 1 + 1 the exponent is always
34:22
equal to the sum of the row and column indices of the element and then that has to be multiplied by the minor of this
34:28
element and remember the minor of an element is just the determinant of the submatrix we get when we eliminate the
34:35
row and column that the element is in so I now just have to find the determinant
34:40
of that submatrix so doing this calculation will give me the co-actor of four and to get the whole co-factor
34:46
Matrix I have to do that calculation for all of the terms in the Matrix and now that I have all nine terms of this
34:53
Matrix I need to simplify each of them so let's start with the element that's in Row 1 column 1 of our co-actor Matrix
35:02
-1 to^ 1 + 1 is just 1 and then I need the determinant of this 2x2 matrix and I
35:08
know to find the determinant let me zoom in a bit I just have to do the product of that diagonal 5 * 3 is 15 minus the
35:16
product of this diagonal 1 * 2 is 2 15 - 2 is 13 so I know the first element in
35:23
my co-actor Matrix is 13 and then I can repeat that process for for the other
35:28
eight elements in this Matrix and I now have the co-actor Matrix of Matrix a but
35:33
what I need in my inverse formula is the adjugate of Matrix a which is the
35:39
transpose of this co-actor Matrix so I have to transpose this Matrix the
35:44
transpose of the co-actor Matrix is the Matrix that just has the rows and columns switched with each other so Row
35:51
one of the co-actor Matrix becomes column one of the transposed version of
35:56
that Matrix and I'll do the same thing with row two becomes column 2 and Row three becomes
36:02
column 3 and now that I have the transpose of the co-actor Matrix and the determinant I can use this formula I'll
36:10
have to make a little bit of room and then I'll sub into the formula the inverse of Matrix a equals 1 over 35 and
36:18
since this value is not zero since the determinant is not zero I know that the Matrix does have an inverse so that
36:24
means there is actually a unique solution to the system which will get to in the next section of this video and
36:29
that has to be multiplied by the adjugate of Matrix a which is this transposed co-actor Matrix and then what
36:36
we could do if we wanted to go further is we could multiply all nine elements of this matrix by the scaler in front
36:43
and that would give me the following Matrix and there we have it there's the inverse of Matrix
Inverse using Row Reduction
36:52
a must know number nine finding the inverse of a matrix using row reduction
36:58
this is an alternate way instead of using the inverse of a matrix formula what we can do is we can make an
37:05
augmented Matrix out of the Matrix we want the inverse of we'll call it Matrix a and we'll augment that with an
37:11
identity Matrix of the same size as a and then we use Elementary row operations to transform that Matrix into
37:19
the Matrix I augmented with the inverse of Matrix a and how we do that is we
37:26
focus on doing row operations to transform Matrix a into an identity
37:32
Matrix and when we do that the operations that do that transformation are going to make this identity Matrix
37:39
go into the opposite or inverse of Matrix a so let's see how that works
37:44
let's say we have Matrix a which is just a 2x2 matrix let's find the inverse of
37:49
that Matrix so we'll construct our augmented matrix by augmenting matrix a
37:55
with an identity Matrix that's also 2x two so that would look like this 1 0 0 1
38:01
and then we'll use Elementary row operations to try and make this Matrix into this identity Matrix I'll start by
38:08
making four my first pivot value and then making the two underneath it become a zero and I'll do that by replacing all
38:14
of row two with 2 * Row 2 minus Row 1 now that the upper triangular Matrix is
38:21
done I'll make the value Above This leading entry be a zero so above the five is seven I want that s to be zero
38:28
so I will replace all of Row 1 with 5 * Row 1 - 7 * Row 2 and now I need my
38:35
leading entries to be one so I need the numbers in this diagonal to be one I can do that by multiplying Row one by 1 over
38:42
20 and by multiplying Row 2 by 1 over5 and now the process is done the original
38:48
Matrix a has been transformed using row operations into the identity Matrix
38:54
which means the identity Matrix has been transformed into the inverse of Matrix a
39:00
so I can conclude now that the inverse of Matrix a is this Matrix right here
39:05
and now that you know a couple ways for finding the inverse of a matrix we should also talk about how we could use
39:11
the inverse of a matrix to help find the solution to a system of equations so let
39:17
me make some room to talk about that inverse matrices can be used to solve linear systems if we first write them in
39:23
Matrix form which means we write the system of equations in the format a * x
39:29
= b where a is the coefficient Matrix X is our variable vector and B is the
39:36
constant vector and the reason we would want to write the system of equations in this Matrix form is because if I
39:43
multiplied both sides of this equation by the inverse of Matrix a on the left
39:48
side the inverse of Matrix a Time Matrix a I know is just the identity Matrix
39:53
that's the same size as Matrix a and multiplying by an identity Matrix is like multiplying by one when dealing
39:59
with matrices so the identity Matrix time the column Vector X is just equal to X so this tells us to get what the
40:05
column Vector of variables is equal to we just do the inverse of Matrix a times
40:11
the column Vector of the constant values so let's see how that would work let's say we have a system of two linear
40:17
equations I could write this system in Matrix form by doing the coefficient Matrix which would be the Matrix 47 2
40:25
6times the column Vector of the variables and our variables are X and Y in both of the equations and that would
40:32
be equal to the column Vector of the constants 6 and three and what I tried to show you down here is that if we
40:38
multiply both sides of this equation by the inverse of the coefficient Matrix we end up getting the column Vector of
40:45
variables isolated and this Matrix we actually already found the inverse of it earlier and I wrote it down up here it's
40:51
this Matrix right here so I'll multiply both sides of my equation by that Matrix on the left side of the equation I have
40:57
Matrix a Time its own inverse I know that's going to be the identity Matrix and on the right side of the equation I
41:04
have two matrices that I have to multiply together I have a 2x2 matrix being multiplied by a 2x1 matrix because
41:12
these elements match I'm allowed to multiply those matrices together and the product is going to be a 2X 1 Matrix to
41:20
get the element that goes in row one column one I do Row one of the first Matrix dotproduct with column 1 of the
41:28
second Matrix which means do 3 5 * 6 +
41:33
-71 * 3 and to get the element in row two column 1 do row two dotproduct with
41:40
column 1 -1 over5 * 6 + 2 5 * 3 and now
41:45
I just have to simplify on the left when multiplying by an identity Matrix you just get itself so the column Vector
41:53
variables X Y would be equal to these two rows simplified go to 1 and 1/2 and
42:00
zero notice for this system we got a unique solution when X is 1.5 and when Y
42:06
is zero both of these equations are satisfied and finding that one unique solution will happen if your coefficient
42:13
Matrix is invertible because it's if it's invertible you're able to use this process to narrow it down to the one
42:18
unique solution if it's not invertible meaning its determinant would have been zero then you would know you don't have
42:24
a unique solution and you'd have to use a different strategy to find out if you have infinite solutions or maybe no
Cramer's Rule
42:35
Solutions must know number 10 Kramer's rule Kramer's rule is a method for
42:40
solving a system of linear equations where the Matrix must be square and it has to have a nonzero determinant for to
42:48
have a nonzero determinant that means there is a unique solution to the system of equations so Kramer's rule only works
42:54
to find us the unique solution of the system and Kramer's rule works for the
42:59
Matrix equation of a system axal B we can get the answer for the variable x i
43:06
by doing the determinant of AI divided by the determinant of Matrix a but what
43:14
is Matrix AI That's the important thing you have to know here and it's actually quite easy once we do an example Matrix
43:21
AI is Matrix a with the I column replaced with vector B and although this
43:28
may sound a little bit confusing right now when we do an example you'll see how easy it is so let's say we have a system
43:34
of two equations with two variables each I could solve this system of equations by first writing the system in Matrix
43:40
form and then before I go ahead and use this formula to find the value of the variables I notice in that formula I
43:47
need the determinant of a so this formula is only going to work if my determinant of a is non Z so let's start
43:54
by finding that determinant the determin of Matrix a well remember Matrix a is
44:00
just this 2x2 coefficient Matrix right here I can find the determinant of a 22 matrix by subtracting the product of the
44:07
diagonals that would be 3 * 4 which is 12 and then I subtract this product of
44:12
ne5 * -2 which is 10 and 12 - 10 gives me 2 so since my determinant is non zero
44:21
I know that my system has a unique solution which means I can use Kramer's rule to find those Solutions which means
44:28
this formula right here and when I use this formula to solve for X1 I'll be replacing these I with one which means I
44:35
need the determinant of Matrix A1 but what is Matrix A1 let's write that down
44:41
as well Matrix A1 is just Matrix a but the First Column of it is going to be
44:46
replaced with the column Vector of my constants 6 and 8 so instead of the First Column being 35 it gets replaced
44:53
with 68 but the second column stays exactly the same and when I solve for X2
44:59
I'm going to need Matrix A2 Matrix A2 is just Matrix a but it's second column is
45:06
replaced with the constant Vector First Column stays the same but it second column changes to 68 and now let me go
45:13
ahead and use this formula when calculating the value of X1 I do the
45:19
determinant of A1 divided by the determinant of a well we have the determinant of a we already calculated
45:26
that to be two but now I need the determinant of Matrix A1 which would be 24 minus -16 that's
45:34
40 which means X1 is 40 / 2 which is 20 and X2 would be the determinant of A2
45:44
/ the determinant of a determinant of a is 2 and we need the determinant of the
45:50
A2 Matrix which would be 24 minus -30
45:55
which is 54 and 54 / 2 is 27 So based on
46:00
Kramer's rule I see there is one unique solution to this system it's when X1 is
46:05

