# 🚀 Multi-Agent Crypto Analysis System – Step-by-Step Manual  

## Part 2 – Data Fetching Agents  

In this step, we will design **AI/Data Agents** that can fetch:  
1. Historical & live **crypto data** (prices, volume, trades).  
2. **News & sentiment data** related to crypto assets.  
3. **Cross-asset data** (commodities, indices, FX) to find correlations.  

---

### 1. Crypto Data Agent (using CCXT)
Install `ccxt` if not done already:  
```bash
pip install ccxt
```

**Example – Fetch historical OHLCV data:**
```python
import ccxt
import pandas as pd

exchange = ccxt.binance()
symbol = "BTC/USDT"
timeframe = "1h"
limit = 1000

ohlcv = exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
df = pd.DataFrame(ohlcv, columns=["timestamp", "open", "high", "low", "close", "volume"])
df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")

print(df.head())
```

This gives you **historical candles** for any asset.  

---

### 2. News Data Agent (using NewsAPI)
Install:  
```bash
pip install newsapi-python
```

**Example – Fetch crypto news:**  
```python
from newsapi import NewsApiClient

newsapi = NewsApiClient(api_key="YOUR_NEWSAPI_KEY")
articles = newsapi.get_everything(q="bitcoin", language="en", sort_by="relevancy")

for article in articles["articles"][:5]:
    print(article["title"], "-", article["publishedAt"])
```

You can store this data in `/data/news/`.  

---

### 3. Cross-Asset Data Agent (using yfinance)
Install:  
```bash
pip install yfinance
```

**Example – Fetch gold and S&P500 data to compare with crypto:**  
```python
import yfinance as yf

assets = ["GC=F", "^GSPC", "BTC-USD"]
data = yf.download(assets, period="1mo", interval="1d")

print(data.head())
```

This provides **commodities + equities + crypto** in one dataset.  

---

### 4. Organizing the Data Agent
Suggested file structure:  
```
agents/
│── data_agent.py   # Central agent that calls all three
data/
│── crypto/
│── news/
│── correlations/
```

Inside `data_agent.py`, define three functions:  
- `fetch_crypto_data()`  
- `fetch_news_data()`  
- `fetch_crossasset_data()`  

Each saves results into `/data/`.  

---

✅ That’s **Part 2 (Data Fetching Agents)**.  
