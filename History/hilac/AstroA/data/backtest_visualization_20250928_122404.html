
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstroA 2-Hour Backtest Results</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .header .timestamp {
            font-size: 1rem;
            opacity: 0.7;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .card h3 {
            font-size: 1.4rem;
            color: #4a5568;
            margin-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #f7fafc;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
            color: #718096;
        }

        .metric-value {
            font-weight: 700;
            font-size: 1.1rem;
        }

        .positive {
            color: #38a169;
        }

        .negative {
            color: #e53e3e;
        }

        .neutral {
            color: #4a5568;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
        }

        .portfolio-chart {
            width: 100%;
            height: 400px;
            margin-bottom: 20px;
        }

        .trades-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .trades-table th,
        .trades-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .trades-table th {
            background-color: #f7fafc;
            font-weight: 600;
            color: #4a5568;
        }

        .trade-buy {
            color: #38a169;
            font-weight: 600;
        }

        .trade-sell {
            color: #e53e3e;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e2e8f0;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #38a169);
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .risk-gauge {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }

        .gauge {
            width: 200px;
            height: 100px;
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 30px;
            opacity: 0.8;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🌟 AstroA Trading System</h1>
            <div class="subtitle">2-Hour Backtesting Results</div>
            <div class="timestamp">Generated on 2025-09-28 at 12:24:04 UTC</div>
        </div>

        <!-- Key Metrics Dashboard -->
        <div class="dashboard">
            <div class="card">
                <h3>💰 Portfolio Performance</h3>
                <div class="metric">
                    <span class="metric-label">Initial Value</span>
                    <span class="metric-value neutral">$80,000.00</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Final Value</span>
                    <span class="metric-value neutral">$79,999.81</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Return</span>
                    <span class="metric-value negative">-0.00%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Max Drawdown</span>
                    <span class="metric-value negative">0.00%</span>
                </div>
            </div>

            <div class="card">
                <h3>📊 Trading Activity</h3>
                <div class="metric">
                    <span class="metric-label">Total Trades</span>
                    <span class="metric-value neutral">3</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Analysis Cycles</span>
                    <span class="metric-value neutral">10</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avg Positions</span>
                    <span class="metric-value neutral">0.4</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Sharpe Ratio</span>
                    <span class="metric-value positive">0.987</span>
                </div>
            </div>

            <div class="card">
                <h3>⚡ System Performance</h3>
                <div class="metric">
                    <span class="metric-label">Duration</span>
                    <span class="metric-value neutral">0.1 hours</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Data Points</span>
                    <span class="metric-value neutral">150</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Errors</span>
                    <span class="metric-value positive">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Success Rate</span>
                    <span class="metric-value positive">100.0%</span>
                </div>
            </div>
        </div>

        <!-- Portfolio Value Chart -->
        <div class="chart-container">
            <div class="chart-title">📈 Portfolio Value Over Time</div>
            <svg class="portfolio-chart" viewBox="0 0 800 400">
                <!-- Chart background -->
                <rect width="800" height="400" fill="#f8fafc"/>

                <!-- Grid lines -->
                <line x1="50" y1="50" x2="750" y2="50" stroke="#e2e8f0" stroke-width="1"/>\n<line x1="50" y1="130" x2="750" y2="130" stroke="#e2e8f0" stroke-width="1"/>\n<line x1="50" y1="210" x2="750" y2="210" stroke="#e2e8f0" stroke-width="1"/>\n<line x1="50" y1="290" x2="750" y2="290" stroke="#e2e8f0" stroke-width="1"/>\n<line x1="50" y1="370" x2="750" y2="370" stroke="#e2e8f0" stroke-width="1"/>\n<line x1="50" y1="450" x2="750" y2="450" stroke="#e2e8f0" stroke-width="1"/>\n<line x1="50" y1="50" x2="50" y2="350" stroke="#e2e8f0" stroke-width="1"/>\n<line x1="190" y1="50" x2="190" y2="350" stroke="#e2e8f0" stroke-width="1"/>\n<line x1="330" y1="50" x2="330" y2="350" stroke="#e2e8f0" stroke-width="1"/>\n<line x1="470" y1="50" x2="470" y2="350" stroke="#e2e8f0" stroke-width="1"/>\n<line x1="610" y1="50" x2="610" y2="350" stroke="#e2e8f0" stroke-width="1"/>\n<line x1="750" y1="50" x2="750" y2="350" stroke="#e2e8f0" stroke-width="1"/>

                <!-- Portfolio value line -->
                <polyline points="50.0,50.0 127.77777777777777,50.0 205.55555555555554,50.0 283.3333333333333,50.0 361.1111111111111,50.0 438.8888888888889,50.0 516.6666666666666,50.0 594.4444444444445,311.1582157189445 672.2222222222222,350.0 750.0,268.36570945984" fill="none" stroke="#4299e1" stroke-width="3"/>\n<circle cx="50.0" cy="50.0" r="4" fill="#2b6cb0"/>\n<circle cx="127.77777777777777" cy="50.0" r="4" fill="#2b6cb0"/>\n<circle cx="205.55555555555554" cy="50.0" r="4" fill="#2b6cb0"/>\n<circle cx="283.3333333333333" cy="50.0" r="4" fill="#2b6cb0"/>\n<circle cx="361.1111111111111" cy="50.0" r="4" fill="#2b6cb0"/>\n<circle cx="438.8888888888889" cy="50.0" r="4" fill="#2b6cb0"/>\n<circle cx="516.6666666666666" cy="50.0" r="4" fill="#2b6cb0"/>\n<circle cx="594.4444444444445" cy="311.1582157189445" r="4" fill="#2b6cb0"/>\n<circle cx="672.2222222222222" cy="350.0" r="4" fill="#2b6cb0"/>\n<circle cx="750.0" cy="268.36570945984" r="4" fill="#2b6cb0"/>

                <!-- Axis labels -->
                <text x="40" y="355.0" text-anchor="end" font-size="12" fill="#718096">$80,000</text>\n<text x="40" y="280.0" text-anchor="end" font-size="12" fill="#718096">$80,000</text>\n<text x="40" y="205.0" text-anchor="end" font-size="12" fill="#718096">$80,000</text>\n<text x="40" y="130.0" text-anchor="end" font-size="12" fill="#718096">$80,000</text>\n<text x="40" y="55.0" text-anchor="end" font-size="12" fill="#718096">$80,000</text>\n<text x="50.0" y="375" text-anchor="middle" font-size="12" fill="#718096">0min</text>\n<text x="190.0" y="375" text-anchor="middle" font-size="12" fill="#718096">24min</text>\n<text x="330.0" y="375" text-anchor="middle" font-size="12" fill="#718096">48min</text>\n<text x="470.0" y="375" text-anchor="middle" font-size="12" fill="#718096">72min</text>\n<text x="610.0" y="375" text-anchor="middle" font-size="12" fill="#718096">96min</text>\n<text x="750.0" y="375" text-anchor="middle" font-size="12" fill="#718096">120min</text>
            </svg>
        </div>

        <!-- Charts Grid -->
        <div class="grid-2">
            <!-- Risk Assessment -->
            <div class="chart-container">
                <div class="chart-title">⚠️ Risk Assessment</div>
                <div class="risk-gauge">
                    <svg class="gauge" viewBox="0 0 200 100">
                        
    <!-- Gauge background -->
    <path d="M 20 80 A 80 80 0 0 1 180 80" fill="none" stroke="#e2e8f0" stroke-width="10"/>

    <!-- Risk zones -->
    <path d="M 20 80 A 80 80 0 0 1 100 10" fill="none" stroke="#38a169" stroke-width="8"/>
    <path d="M 100 10 A 80 80 0 0 1 180 80" fill="none" stroke="#e53e3e" stroke-width="8"/>

    <!-- Needle -->
    <line x1="100" y1="80" x2="86.82608432242127" y2="33.17391567757874" stroke="#2d3748" stroke-width="3"/>
    <circle cx="100" cy="80" r="5" fill="#2d3748"/>
    
                    </svg>
                </div>
                <div style="text-align: center; margin-top: 15px;">
                    <div style="font-size: 1.1rem; font-weight: 600; color: #4a5568;">
                        Current Risk Level: <span style="color: #e53e3e;">
                        Low
                        </span>
                    </div>
                </div>
            </div>

            <!-- Position Distribution -->
            <div class="chart-container">
                <div class="chart-title">🎯 Position Distribution</div>
                <svg width="100%" height="300" viewBox="0 0 300 300">
                    <circle cx="150" cy="150" r="20" fill="#4299e1" opacity="0.7"/>
                </svg>
            </div>
        </div>

        <!-- Recent Trades Table -->
        <div class="chart-container">
            <div class="chart-title">💼 Recent Trades</div>
            
    <table class="trades-table">
        <thead>
            <tr>
                <th>Symbol</th>
                <th>Action</th>
                <th>Quantity</th>
                <th>Price</th>
                <th>Time</th>
                <th>Strategy</th>
                <th>Confidence</th>
            </tr>
        </thead>
        <tbody>
    
        <tr>
            <td>DOT/USDT</td>
            <td class="trade-sell">SELL</td>
            <td>1.37</td>
            <td>$12.47</td>
            <td>2025-09-28 12:23</td>
            <td>mean_reversion_001</td>
            <td>70.1%</td>
        </tr>
        
        <tr>
            <td>DOT/USDT</td>
            <td class="trade-buy">BUY</td>
            <td>1.62</td>
            <td>$12.37</td>
            <td>2025-09-28 12:23</td>
            <td>momentum_001</td>
            <td>92.9%</td>
        </tr>
        
        <tr>
            <td>ETH/USDT</td>
            <td class="trade-sell">SELL</td>
            <td>1.52</td>
            <td>$3309.34</td>
            <td>2025-09-28 12:23</td>
            <td>momentum_001</td>
            <td>94.0%</td>
        </tr>
        
        </tbody>
    </table>
    
        </div>

        <!-- System Status -->
        <div class="chart-container">
            <div class="chart-title">🔧 System Status & Health</div>
            <div class="dashboard">
                <div style="text-align: center;">
                    <h4 style="color: #4a5568; margin-bottom: 15px;">Data Collection Status</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 62.5%"></div>
                    </div>
                    <p style="color: #718096; margin-top: 5px;">
                        150 / 240 expected data points
                    </p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Generated by AstroA Autonomous Trading System</p>
            <p>Powered by DeepSeek AI & Mathematical Analysis Engines</p>
        </div>
    </div>
</body>
</html>
