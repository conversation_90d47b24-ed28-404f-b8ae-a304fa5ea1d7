"""
Paper Trading Configuration for AstroA System
"""
import os
from typing import Dict, List
from dataclasses import dataclass, field
from dotenv import load_dotenv

load_dotenv()

@dataclass
class PaperTradingConfig:
    """Configuration for paper trading environment"""

    # Alpaca API Configuration
    api_key: str = os.getenv('ALPACA_API_KEY', '')
    secret_key: str = os.getenv('ALPACA_SECRET_KEY', '')
    base_url: str = os.getenv('ALPACA_BASE_URL', 'https://paper-api.alpaca.markets')

    # Trading Parameters
    initial_cash: float = float(os.getenv('PAPER_TRADING_INITIAL_CASH', '100000'))
    max_positions: int = int(os.getenv('PAPER_TRADING_MAX_POSITIONS', '10'))
    risk_limit: float = float(os.getenv('PAPER_TRADING_RISK_LIMIT', '0.02'))

    # Symbols to trade - Top 100 most traded assets
    tradable_symbols: List[str] = field(default_factory=lambda: [
        # Top Crypto (50 symbols)
        'BTCUSD', 'ETHUSD', 'ADAUSD', 'SOLUSD', 'DOTUSD', 'LINKUSD', 'AVAXUSD', 'MATICUSD',
        'BNBUSD', 'XRPUSD', 'LTCUSD', 'BCHUSD', 'UNIUSD', 'ATOMUSD', 'FILUSD', 'TRXUSD',
        'XLMUSD', 'VETUSD', 'ICPUSD', 'FTMUSD', 'ALGOUSD', 'MANAUSD', 'SANDUSD', 'AXSUSD',
        'THETAUSD', 'FLOWUSD', 'APEUSD', 'CHZUSD', 'ENJUSD', 'GALAUSD', 'ROSEUSD', 'NEARUSD',
        'GMTUSD', 'APTUSD', 'QNTUSD', 'LDOUSD', 'STXUSD', 'IMXUSD', 'INJUSD', 'OPUSD',
        'ARBUSD', 'SUIUSD', 'SEIUSD', 'TIAUSD', 'WUSD', 'PEPE1000USD', 'SHIBUSD', 'DOGEUSD',
        'WIFUSD', 'BONKUSD',

        # Top Stocks (50 symbols)
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'BRK.B', 'LLY', 'AVGO',
        'JPM', 'UNH', 'XOM', 'V', 'PG', 'MA', 'JNJ', 'HD', 'CVX', 'ABBV',
        'BAC', 'ORCL', 'KO', 'WMT', 'COST', 'PEP', 'TMO', 'NFLX', 'ABT', 'CRM',
        'ACN', 'MRK', 'CSCO', 'AMD', 'LIN', 'DHR', 'VZ', 'ADBE', 'TXN', 'WFC',
        'QCOM', 'PM', 'NOW', 'NEE', 'RTX', 'SPGI', 'HON', 'UPS', 'T', 'LOW'
    ])

    # Strategy Configuration - OPTIMIZED based on backtest results (90.9% win rate)
    strategies_enabled: List[str] = field(default_factory=lambda: [
        'mean_reversion',    # PRIMARY: 100% win rate on H < 0.40 signals
        'news_sentiment',    # SECONDARY: Your original news-based idea
        'ml_enhanced',       # EXPERIMENTAL: ML models for prediction
    ])

    # Risk Management - OPTIMIZED for mean reversion strategy
    max_position_size: float = 0.20  # 20% of portfolio per position (was 15%)
    stop_loss_percentage: float = 0.015  # 1.5% stop loss (tighter, was 2%)
    take_profit_percentage: float = 0.06  # 6% take profit (wider, was 4%)
    daily_loss_limit: float = 0.05  # 5% daily loss limit

    # OPTIMIZED: Mean Reversion Parameters (based on backtest)
    hurst_mean_reversion_threshold: float = 0.40  # H < 0.40 = mean reversion
    hurst_trending_threshold: float = 0.60  # H > 0.60 = trending
    hurst_random_walk_avoid: bool = True  # Avoid 0.40 <= H <= 0.60
    mean_reversion_position_boost: float = 1.3  # 30% larger positions for H < 0.40
    strong_mean_reversion_boost: float = 1.5  # 50% larger for H < 0.35

    # Data Feed Configuration
    real_time_enabled: bool = os.getenv('REAL_TIME_DATA_ENABLED', 'true').lower() == 'true'
    data_update_interval: int = 60  # seconds

    # Logging
    log_trades: bool = True
    log_signals: bool = True
    log_performance: bool = True

    def validate(self):
        """Validate configuration"""
        if not self.api_key:
            raise ValueError("ALPACA_API_KEY is required for paper trading")
        if not self.secret_key:
            raise ValueError("ALPACA_SECRET_KEY is required for paper trading")
        
        if self.initial_cash <= 0:
            raise ValueError("Initial cash must be positive")
        
        if not self.tradable_symbols:
            raise ValueError("At least one tradable symbol is required")
        
        return True

    def get_trading_config(self) -> Dict:
        """Get trading configuration as dictionary"""
        return {
            'initial_cash': self.initial_cash,
            'max_positions': self.max_positions,
            'risk_limit': self.risk_limit,
            'max_position_size': self.max_position_size,
            'stop_loss_percentage': self.stop_loss_percentage,
            'take_profit_percentage': self.take_profit_percentage,
            'daily_loss_limit': self.daily_loss_limit
        }

# Global configuration instance
paper_config = PaperTradingConfig()
