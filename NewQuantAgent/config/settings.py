"""
NewQuantAgent Configuration Settings
Centralized configuration for DeepSeek AI API and memecoin trading services
"""
import os
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv()

class Config:
    """Main configuration class for NewQuantAgent"""

    # Application Settings
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')
    SECRET_KEY = os.getenv('SECRET_KEY', 'newquantagent-secret-key-change-in-production')

    # Database Configuration
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///newquantagent.db')
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')

    # DeepSeek AI Configuration
    DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')
    DEEPSEEK_BASE_URL = os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
    DEEPSEEK_MODEL = os.getenv('DEEPSEEK_MODEL', 'deepseek-chat')
    DEEPSEEK_MAX_TOKENS = int(os.getenv('DEEPSEEK_MAX_TOKENS', '4000'))
    DEEPSEEK_TEMPERATURE = float(os.getenv('DEEPSEEK_TEMPERATURE', '0.1'))

    # Pump.tires/Pump.fun Configuration
    PUMP_TIRES_API_URL = os.getenv('PUMP_TIRES_API_URL', 'https://api.pump.tires')
    PUMP_FUN_API_URL = os.getenv('PUMP_FUN_API_URL', 'https://api.pump.fun')
    PUMP_TIRES_WS_URL = os.getenv('PUMP_TIRES_WS_URL', 'wss://ws.pump.tires')

    # Solana Configuration
    SOLANA_RPC_URL = os.getenv('SOLANA_RPC_URL', 'https://api.mainnet-beta.solana.com')
    SOLANA_WS_URL = os.getenv('SOLANA_WS_URL', 'wss://api.mainnet-beta.solana.com')
    SOLANA_PRIVATE_KEY = os.getenv('SOLANA_PRIVATE_KEY')  # Base58 encoded

    # Exchange API Keys
    BINANCE_API_KEY = os.getenv('BINANCE_API_KEY')
    BINANCE_SECRET_KEY = os.getenv('BINANCE_SECRET_KEY')
    COINBASE_API_KEY = os.getenv('COINBASE_API_KEY')
    COINBASE_SECRET_KEY = os.getenv('COINBASE_SECRET_KEY')

    # DEX Configuration
    JUPITER_API_URL = os.getenv('JUPITER_API_URL', 'https://quote-api.jup.ag/v6')
    RAYDIUM_API_URL = os.getenv('RAYDIUM_API_URL', 'https://api.raydium.io/v2')
    ORCA_API_URL = os.getenv('ORCA_API_URL', 'https://api.orca.so')

    # News & Data APIs
    NEWS_API_KEY = os.getenv('NEWS_API_KEY')
    COINGECKO_API_KEY = os.getenv('COINGECKO_API_KEY')
    DEXSCREENER_API_URL = os.getenv('DEXSCREENER_API_URL', 'https://api.dexscreener.com/latest')

    # Live Trading Configuration - $9 Test Wallet
    LIVE_TRADING_ENABLED = os.getenv('LIVE_TRADING_ENABLED', 'true').lower() == 'true'
    INITIAL_PORTFOLIO_VALUE = float(os.getenv('INITIAL_PORTFOLIO_VALUE', '9.0'))
    MAX_POSITION_SIZE = float(os.getenv('MAX_POSITION_SIZE', '0.02'))  # 2% max position for small wallet
    MAX_PORTFOLIO_VOLATILITY = float(os.getenv('MAX_PORTFOLIO_VOLATILITY', '0.15'))  # Conservative
    MAX_DRAWDOWN_LIMIT = float(os.getenv('MAX_DRAWDOWN_LIMIT', '0.10'))  # 10% max drawdown
    UPDATE_INTERVAL_SECONDS = int(os.getenv('UPDATE_INTERVAL_SECONDS', '60'))  # 60 second cycles

    # Risk Management - Conservative for $9 wallet
    MAX_POSITION_VALUE_USD = float(os.getenv('MAX_POSITION_VALUE_USD', '1.50'))  # Max $1.50 per trade
    MIN_POSITION_VALUE_USD = float(os.getenv('MIN_POSITION_VALUE_USD', '0.10'))  # Min $0.10 per trade
    MAX_DAILY_TRADES = int(os.getenv('MAX_DAILY_TRADES', '5'))  # Max 5 trades per day
    MAX_CONCURRENT_POSITIONS = int(os.getenv('MAX_CONCURRENT_POSITIONS', '3'))  # Max 3 positions
    STOP_LOSS_PERCENT = float(os.getenv('STOP_LOSS_PERCENTAGE', '5.0'))  # 5% stop loss
    TAKE_PROFIT_PERCENT = float(os.getenv('TAKE_PROFIT_PERCENTAGE', '15.0'))  # 15% take profit

    # Memecoin Trading Specific
    MIN_LIQUIDITY_USD = float(os.getenv('MIN_LIQUIDITY_USD', '5000'))  # Lower for small trades
    MAX_SLIPPAGE_PERCENT = float(os.getenv('MAX_SLIPPAGE_PERCENT', '3.0'))  # 3% max slippage
    GRADUATION_THRESHOLD = float(os.getenv('GRADUATION_THRESHOLD', '69000'))  # Pump.fun graduation
    MIN_HOLDER_COUNT = int(os.getenv('MIN_HOLDER_COUNT', '50'))  # Lower minimum holders
    MAX_HOLDER_CONCENTRATION = float(os.getenv('MAX_HOLDER_CONCENTRATION', '0.4'))  # 40% max single holder

    # Signal Generation Thresholds
    BUY_SIGNAL_THRESHOLD = int(os.getenv('BUY_SIGNAL_THRESHOLD', '70'))
    SELL_SIGNAL_THRESHOLD = int(os.getenv('SELL_SIGNAL_THRESHOLD', '30'))
    MIN_CONFIDENCE_THRESHOLD = float(os.getenv('MIN_CONFIDENCE_THRESHOLD', '0.6'))
    SIGNAL_TIMEOUT_SECONDS = int(os.getenv('SIGNAL_TIMEOUT_SECONDS', '300'))

    # Performance Monitoring
    PERFORMANCE_TRACKING_ENABLED = os.getenv('PERFORMANCE_TRACKING_ENABLED', 'true').lower() == 'true'
    METRICS_UPDATE_INTERVAL = int(os.getenv('METRICS_UPDATE_INTERVAL', '30'))
    ALERT_ON_DRAWDOWN = os.getenv('ALERT_ON_DRAWDOWN', 'true').lower() == 'true'
    ALERT_THRESHOLD_PERCENTAGE = float(os.getenv('ALERT_THRESHOLD_PERCENTAGE', '0.05'))

    POSITION_SIZE_MULTIPLIER = float(os.getenv('POSITION_SIZE_MULTIPLIER', '1.0'))
    VOLATILITY_ADJUSTMENT = os.getenv('VOLATILITY_ADJUSTMENT', 'True').lower() == 'true'

    # Mathematical Analysis
    HURST_WINDOW = int(os.getenv('HURST_WINDOW', '50'))
    MOMENTUM_WINDOW = int(os.getenv('MOMENTUM_WINDOW', '14'))
    VOLATILITY_WINDOW = int(os.getenv('VOLATILITY_WINDOW', '20'))
    CORRELATION_WINDOW = int(os.getenv('CORRELATION_WINDOW', '60'))

    # Data Directory Paths
    BASE_DIR = Path(__file__).parent.parent
    DATA_DIR = BASE_DIR / 'data'
    REPORTS_DIR = BASE_DIR / 'reports'
    LOGS_DIR = BASE_DIR / 'logs'
    MODELS_DIR = BASE_DIR / 'models'

    # Agent Configuration
    AGENT_TIMEOUT = int(os.getenv('AGENT_TIMEOUT', '300'))  # 5 minutes
    MAX_CONCURRENT_AGENTS = int(os.getenv('MAX_CONCURRENT_AGENTS', '10'))
    AGENT_RETRY_ATTEMPTS = int(os.getenv('AGENT_RETRY_ATTEMPTS', '3'))

    # WebSocket Configuration
    WS_RECONNECT_DELAY = int(os.getenv('WS_RECONNECT_DELAY', '5'))
    WS_MAX_RECONNECT_ATTEMPTS = int(os.getenv('WS_MAX_RECONNECT_ATTEMPTS', '10'))
    WS_PING_INTERVAL = int(os.getenv('WS_PING_INTERVAL', '30'))

    # Rate Limiting
    API_RATE_LIMIT_PER_MINUTE = int(os.getenv('API_RATE_LIMIT_PER_MINUTE', '60'))
    DEEPSEEK_RATE_LIMIT_PER_MINUTE = int(os.getenv('DEEPSEEK_RATE_LIMIT_PER_MINUTE', '20'))

    # Monitoring & Alerts
    ENABLE_TELEGRAM_ALERTS = os.getenv('ENABLE_TELEGRAM_ALERTS', 'False').lower() == 'true'
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
    TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')
    ENABLE_DISCORD_ALERTS = os.getenv('ENABLE_DISCORD_ALERTS', 'False').lower() == 'true'
    DISCORD_WEBHOOK_URL = os.getenv('DISCORD_WEBHOOK_URL')

    # Performance Monitoring
    ENABLE_METRICS = os.getenv('ENABLE_METRICS', 'True').lower() == 'true'
    METRICS_PORT = int(os.getenv('METRICS_PORT', '8080'))
    ENABLE_PROFILING = os.getenv('ENABLE_PROFILING', 'False').lower() == 'true'

    @classmethod
    def validate_config(cls):
        """Validate required configuration values"""
        required_fields = [
            'DEEPSEEK_API_KEY',
        ]

        missing_fields = []
        for field in required_fields:
            if not getattr(cls, field, None):
                missing_fields.append(field)

        if missing_fields:
            raise ValueError(f"Missing required configuration: {', '.join(missing_fields)}")

        # Validate numeric ranges
        if cls.MAX_POSITION_SIZE > 1.0 or cls.MAX_POSITION_SIZE <= 0:
            raise ValueError("MAX_POSITION_SIZE must be between 0 and 1")

        if cls.MAX_SLIPPAGE_PERCENT > 20.0 or cls.MAX_SLIPPAGE_PERCENT <= 0:
            raise ValueError("MAX_SLIPPAGE_PERCENT must be between 0 and 20")

        return True

    @classmethod
    def get_trading_config(cls) -> dict:
        """Get trading-specific configuration"""
        return {
            'max_position_size': cls.MAX_POSITION_SIZE,
            'max_portfolio_volatility': cls.MAX_PORTFOLIO_VOLATILITY,
            'max_drawdown_limit': cls.MAX_DRAWDOWN_LIMIT,
            'min_liquidity_usd': cls.MIN_LIQUIDITY_USD,
            'max_slippage_percent': cls.MAX_SLIPPAGE_PERCENT,
            'graduation_threshold': cls.GRADUATION_THRESHOLD,
            'min_holder_count': cls.MIN_HOLDER_COUNT,
            'max_holder_concentration': cls.MAX_HOLDER_CONCENTRATION,
            'stop_loss_percent': cls.STOP_LOSS_PERCENT,
            'take_profit_percent': cls.TAKE_PROFIT_PERCENT,
            'volatility_adjustment': cls.VOLATILITY_ADJUSTMENT
        }

    @classmethod
    def get_mathematical_config(cls) -> dict:
        """Get mathematical analysis configuration"""
        return {
            'hurst_window': cls.HURST_WINDOW,
            'momentum_window': cls.MOMENTUM_WINDOW,
            'volatility_window': cls.VOLATILITY_WINDOW,
            'correlation_window': cls.CORRELATION_WINDOW
        }

    @classmethod
    def get_api_config(cls) -> dict:
        """Get API configuration"""
        return {
            'deepseek': {
                'api_key': cls.DEEPSEEK_API_KEY,
                'base_url': cls.DEEPSEEK_BASE_URL,
                'model': cls.DEEPSEEK_MODEL,
                'max_tokens': cls.DEEPSEEK_MAX_TOKENS,
                'temperature': cls.DEEPSEEK_TEMPERATURE
            },
            'pump_tires': {
                'api_url': cls.PUMP_TIRES_API_URL,
                'ws_url': cls.PUMP_TIRES_WS_URL
            },
            'solana': {
                'rpc_url': cls.SOLANA_RPC_URL,
                'ws_url': cls.SOLANA_WS_URL
            }
        }

# Create directories if they don't exist
for directory in [Config.DATA_DIR, Config.REPORTS_DIR, Config.LOGS_DIR, Config.MODELS_DIR]:
    directory.mkdir(exist_ok=True)
