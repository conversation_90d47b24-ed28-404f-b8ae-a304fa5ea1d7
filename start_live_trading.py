#!/usr/bin/env python3
"""
NewQuantAgent Live Trading Startup Script

Safe deployment script for live trading with $9 test wallet.
Includes pre-flight checks, configuration validation, and monitoring setup.
"""

import asyncio
import sys
import logging
import os
from pathlib import Path
from datetime import datetime

# Add NewQuantAgent to path
sys.path.insert(0, str(Path(__file__).parent / "NewQuantAgent"))

def setup_logging():
    """Setup comprehensive logging for live trading"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Create timestamped log file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"live_trading_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"🚀 NewQuantAgent Live Trading Session Started")
    logger.info(f"📝 Logging to: {log_file}")
    return logger

def validate_environment():
    """Validate environment variables and configuration"""
    logger = logging.getLogger(__name__)
    
    # Check .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        logger.error("❌ .env file not found!")
        return False
    
    # Load and check critical environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    critical_vars = [
        'DEEPSEEK_API_KEY',
        'LIVE_TRADING_ENABLED',
        'INITIAL_PORTFOLIO_VALUE',
        'MAX_POSITION_VALUE_USD'
    ]
    
    missing_vars = []
    for var in critical_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing critical environment variables: {missing_vars}")
        return False
    
    # Validate configuration values
    portfolio_value = float(os.getenv('INITIAL_PORTFOLIO_VALUE', '0'))
    max_position = float(os.getenv('MAX_POSITION_VALUE_USD', '0'))
    
    if portfolio_value <= 0:
        logger.error("❌ Invalid portfolio value")
        return False
    
    if max_position > portfolio_value * 0.5:
        logger.warning(f"⚠️  Max position size (${max_position}) is >50% of portfolio (${portfolio_value})")
    
    logger.info("✅ Environment validation passed")
    return True

def check_dependencies():
    """Check if all required dependencies are installed"""
    logger = logging.getLogger(__name__)
    
    required_packages = [
        'aiohttp',
        'websockets', 
        'numpy',
        'pandas',
        'pydantic',
        'solana',
        'ccxt',
        'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"❌ Missing required packages: {missing_packages}")
        logger.info("Install with: pip install " + " ".join(missing_packages))
        return False
    
    logger.info("✅ All dependencies available")
    return True

def test_import():
    """Test if NewQuantAgent can be imported successfully"""
    logger = logging.getLogger(__name__)
    
    try:
        from NewQuantAgent.main import NewQuantAgent
        logger.info("✅ NewQuantAgent import successful")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to import NewQuantAgent: {e}")
        return False

def display_configuration():
    """Display current trading configuration"""
    logger = logging.getLogger(__name__)
    
    from dotenv import load_dotenv
    load_dotenv()
    
    config_info = f"""
🔧 LIVE TRADING CONFIGURATION
═══════════════════════════════════════
💰 Portfolio Value: ${os.getenv('INITIAL_PORTFOLIO_VALUE', 'N/A')}
📊 Max Position Size: {float(os.getenv('MAX_POSITION_SIZE', '0'))*100:.1f}% (${os.getenv('MAX_POSITION_VALUE_USD', 'N/A')})
🛡️  Stop Loss: {os.getenv('STOP_LOSS_PERCENTAGE', 'N/A')}%
🎯 Take Profit: {os.getenv('TAKE_PROFIT_PERCENTAGE', 'N/A')}%
📈 Max Daily Trades: {os.getenv('MAX_DAILY_TRADES', 'N/A')}
🔄 Update Interval: {os.getenv('UPDATE_INTERVAL_SECONDS', 'N/A')}s
🤖 AI Model: DeepSeek ({os.getenv('DEEPSEEK_API_KEY', 'Not configured')[:10]}...)
🌐 Environment: {os.getenv('ENVIRONMENT', 'N/A')}
═══════════════════════════════════════
"""
    
    logger.info(config_info)

async def run_pre_flight_checks():
    """Run comprehensive pre-flight checks"""
    logger = logging.getLogger(__name__)
    
    logger.info("🔍 Running pre-flight checks...")
    
    checks = [
        ("Environment Variables", validate_environment),
        ("Dependencies", check_dependencies), 
        ("Import Test", test_import)
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        logger.info(f"Checking {check_name}...")
        if not check_func():
            all_passed = False
            logger.error(f"❌ {check_name} check failed")
        else:
            logger.info(f"✅ {check_name} check passed")
    
    return all_passed

async def start_trading_system():
    """Start the NewQuantAgent trading system"""
    logger = logging.getLogger(__name__)
    
    try:
        from NewQuantAgent.main import NewQuantAgent
        
        logger.info("🚀 Initializing NewQuantAgent...")
        agent = NewQuantAgent()
        
        logger.info("🔄 Starting trading system...")
        if await agent.start():
            logger.info("✅ NewQuantAgent started successfully!")
            logger.info("📊 Live trading is now active with $9 test wallet")
            logger.info("🔄 Trading cycles running every 60 seconds")
            logger.info("🛡️  Conservative risk management active")
            
            # Keep running until interrupted
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                logger.info("🛑 Shutdown requested by user...")
                await agent.stop()
                logger.info("✅ NewQuantAgent stopped successfully")
        else:
            logger.error("❌ Failed to start NewQuantAgent")
            return False
            
    except Exception as e:
        logger.error(f"❌ Critical error in trading system: {e}")
        return False
    
    return True

async def main():
    """Main entry point for live trading"""
    logger = setup_logging()
    
    try:
        logger.info("🎯 NewQuantAgent Live Trading Deployment")
        logger.info("💰 Test Wallet: $9.00 USD")
        logger.info("🛡️  Risk Level: Conservative")
        
        # Display configuration
        display_configuration()
        
        # Run pre-flight checks
        if not await run_pre_flight_checks():
            logger.error("❌ Pre-flight checks failed. Aborting deployment.")
            return 1
        
        logger.info("✅ All pre-flight checks passed!")
        
        # Confirm deployment
        logger.info("🚨 LIVE TRADING DEPLOYMENT READY")
        logger.info("💡 This will trade with real money ($9 test wallet)")
        
        # Start trading system
        success = await start_trading_system()
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"❌ Critical error in main: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
